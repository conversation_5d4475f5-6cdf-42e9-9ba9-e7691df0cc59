import React, { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Modal,
  Image,
  ActivityIndicator,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import TransactionItem from "../../Components/TransactionItem";
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import FundWalletBottomSheet from "../../Components/FundWalletBottomSheet";
import { GetAccountDetails, GetAccountTrsansactions, GetWalletBalance } from "../../RequestHandler.tsx/Wallet";
import Loader from "../../Components/Loader";


interface pprops {
  navigation: any;
  country?: string;
  accountIds?: string[];
}
const WalletWithBalance = ({ country, navigation, accountIds }: pprops) => {
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const [loadBal, setLoadBal] = useState(false)
  const [bal, setBal] = useState(0)
  const [loadTran, setLoadTran] = useState(false)
  const [transactions, setTransactions] = useState<any>([])
  const [accountDetails, setAccountDetails] = useState<any>([])
  const [showDetails, setShowDetails] = useState(false)

  // Sample transaction data


  const getWalletBalance = async () => {
    setLoadBal(true)
    try {
      console.log(accountIds);
      const res = await GetWalletBalance(accountIds[0])
      if (res.data) {
        setBal(res.data.currentBalance)
      }
    } catch (error) {
      console.log(error)
    } finally {
      setLoadBal(false)
    }
  }

  const getAccountDetails = async () => {
    try {
      const res = await GetAccountDetails(accountIds[0])
      console.log(res);
      if (res.data) {
        setAccountDetails(res.data)
      }
    } catch (error) {
      console.log(error);
    }
  }
  const getAccountTransasactions = async () => {
    try {
      const res = await GetAccountTrsansactions(accountIds[0], 1, 10)
      console.log(res);
    } catch (error) {
      console.log(error);

    }
  }


  useEffect(() => {
    getAccountDetails()
    getAccountTransasactions()
  }, [])

  useFocusEffect(useCallback(() => {
    getWalletBalance()
  }, []))



  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Wallet Card */}
      <View style={styles.walletCard}>
        <View style={styles.walletCardHeader}>
          <View style={styles.walletInfo}>
            <Image
              source={{
                uri: `https://flagcdn.com/w2560/ng.png`,
              }}
              style={[
                styles.selectedFlag,
                {
                  objectFit:
                    "fill",
                },
              ]}
            />
            <Text style={styles.walletType}>NGN Wallet</Text>
          </View>
        </View>
        {loadBal ? <ActivityIndicator size={"small"} color={colors.black} /> : <Text style={styles.walletBalance}>₦{bal?.toFixed(2)}</Text>}

      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={() => {
          if (country === "NGA") {
            setIsBottomSheetVisible(true);
          } else {
            navigation.navigate('InternationalFundScreen');
          }
        }}>
          <SvgXml xml={svg.topUp} width={24} height={24} />
          <Text style={styles.actionButtonText}>Fund</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => navigation.navigate('SendToNigerianBankScreen')}>
          <SvgXml xml={svg.send} width={24} height={24} />
          <Text style={styles.actionButtonText}>Send</Text>
        </TouchableOpacity>
        {/* <TouchableOpacity style={styles.actionButton}>
          <SvgXml xml={svg.convert} width={24} height={24} />
          <Text style={styles.actionButtonText}>Convert</Text>
        </TouchableOpacity> */}
        <TouchableOpacity style={styles.actionButton} onPress={() => {
          setShowDetails(true)
        }}>
          <SvgXml xml={svg.infoCircle} width={24} height={24} />
          <Text style={styles.actionButtonText}>Details</Text>
        </TouchableOpacity>
      </View>

      {/* Transaction History */}
      <View style={styles.transactionHistoryHeader}>
        <Text style={styles.transactionHistoryTitle}>Transaction History</Text>
        {transactions.length > 5 && <TouchableOpacity>
          <Text style={styles.seeMoreText}>See More</Text>
        </TouchableOpacity>}
      </View>
      {transactions.length === 0 ? <View style={styles.emptyTransactions}>
        <Image
          source={require("../../assets/empty-transactions.png")}
          style={styles.emptyTransactionsImage}
        />
        <Text style={styles.emptyTransactionsTitle}>
          No transaction
        </Text>
        <Text style={styles.emptyTransactionsSubtitle}>
          Get a card and start{"\n"}managing your transactions
        </Text>
      </View> : <View style={styles.transactionsList}>
        {/* Date Header */}
        <Text style={styles.dateHeader}>24th April 2025</Text>
        {transactions.map((transaction) => (
          <TouchableOpacity
            key={transaction.id}
            onPress={() => navigation.navigate('TransactionDetailsScreen', { transactionData: transaction })}
          >
            <TransactionItem
              name={transaction.name}
              status={transaction.status}
              amount={transaction.amount}
              isDebit={transaction.isDebit}
            />
          </TouchableOpacity>
        ))}
      </View>}


      {/* Fund Wallet Bottom Sheet Modal */}
      <Modal
        transparent={true}
        visible={isBottomSheetVisible}
        animationType="slide"
        onRequestClose={() => setIsBottomSheetVisible(false)}
        statusBarTranslucent
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsBottomSheetVisible(false)}

        >
          <View style={styles.bottomSheetContainer}>
            <FundWalletBottomSheet
              text1="Fund Wallet"
              text2=" Kindly make a transfer to the account below to automatically fund your
        Naira wallet."
              accountHolder={accountDetails?.accountName}
              bankName={accountDetails?.provider}
              accountNumber={accountDetails?.accountNumber}
            />
          </View>
        </TouchableOpacity>
      </Modal>
      <Modal
        transparent={true}
        visible={showDetails}
        animationType="slide"
        onRequestClose={() => setShowDetails}
        statusBarTranslucent
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowDetails(false)}
        >
          <View style={styles.bottomSheetContainer}>
            <FundWalletBottomSheet
              text1="Account details"
              accountHolder={accountDetails?.accountName}
              bankName={accountDetails?.provider}
              accountNumber={accountDetails?.accountNumber}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    width: "100%",
    marginTop: 20
  },
  walletCard: {
    backgroundColor: colors.primary,
    borderRadius: 16,
    padding: 10,
    marginBottom: 24,
    minHeight: 40,
  },
  walletCardHeader: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  arrowButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    top: "30%",
  },
  walletInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  flagContainer: {
    flexDirection: "row",
    marginRight: 8,
    width: 18,
    height: 18,
    borderRadius: 9,
    overflow: "hidden",
  },
  flagGreen: {
    flex: 1,
    backgroundColor: "#008751",
  },
  flagWhite: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  walletType: {
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
    color: colors.textBlack,
  },
  walletBalance: {
    fontFamily: fonts.plusJBold,
    fontSize: 24,
    color: colors.textBlack,
    textAlign: "center",
  },
  actionButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  actionButton: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.primarySubtle,
    borderRadius: 12,
    paddingVertical: 16,
    marginHorizontal: 6,
  },
  actionButtonText: {
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
    color: colors.textBlack,
    marginTop: 8,
  },
  transactionHistoryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  transactionHistoryTitle: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 18,
    color: colors.textBlack,
  },
  seeMoreText: {
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
    color: colors.deepPrimary,
  },
  dateHeader: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 16,
  },
  transactionsList: {
    marginBottom: 24,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheetContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 0,
    paddingTop: 0,
    paddingBottom: 0,
  },
  selectedFlag: {
    width: 20,
    height: 20,
    marginRight: 4,
    borderRadius: 100,
  },
  emptyTransactions: {
    alignItems: "center",
    paddingVertical: 24,
    marginTop: 36,
  },
  emptyTransactionsImage: {
    width: 130,
    height: 98,
    marginBottom: 16,
  },
  emptyTransactionsTitle: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 8,
  },
  emptyTransactionsSubtitle: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: colors.textAsh,
    textAlign: "center",
    marginBottom: 24,
  },
});

export default WalletWithBalance;
