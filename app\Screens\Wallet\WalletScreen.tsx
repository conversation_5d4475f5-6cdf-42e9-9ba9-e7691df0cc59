import React, { useState, useCallback } from "react";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import WalletWithBalance from "./WalletWithBalance";
import EmptyWalletState from "./EmptyWalletState";
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect } from "@react-navigation/native";
import { GetUser, GetWallet } from "../../RequestHandler.tsx/User";
import Loader from "../../Components/Loader";
import NetworkError from "../../Components/NetworkError";

const WalletScreen = () => {
  const [hasWallet, setHasWallet] = useState<boolean | null>(null);
  const [isCardCreated, setIsCardCreated] = useState(false)
  const [loading, setLoading] = useState(false);
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [country, setCountry] = useState("")
  const navigation = useNavigation<any>();
  const [accountId, setAccountId] = useState(null)

  const checkWallet = async () => {
    setLoading(true);
    setIsNetworkError(false);
    try {
      const response = await GetWallet();
      console.log(response);
      
      const user = await GetUser()
      // Update hasWallet based on API response
      if (response?.data?.hasAccount !== undefined) {
        setHasWallet(response.data.hasAccount);
        setIsCardCreated(response.data.hasCard);
        console.log(response.data.accountIds);
        
        setAccountId(response.data.accountIds)
      }
      if (user.data) {
        setCountry(user.data.country)
      }
    } catch (error) {
      if (error?.isNetworkError) {
        setIsNetworkError(true);
      }
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      checkWallet();
    }, [])
  );

  // Show network error if there's a network issue
  if (isNetworkError) {
    return (
      <Div>
        <Header title="Transaction Hub" />
        <NetworkError
          type="fullscreen"
          errorType="network"
          onRetry={checkWallet}
        />
      </Div>
    );
  }

  // Show loading state while checking wallet status
  if (loading) {
    return <Loader type="overlay" size="large" style={{ marginLeft: 8 }} />;
  }

  return (
    <Div>
      <Header title="Transaction Hub" />
      {hasWallet ? (
        <WalletWithBalance navigation={navigation} country={country} accountIds={accountId}/>
      ) : (
        <EmptyWalletState navigation={navigation} country={country} isCardCreated={isCardCreated} />
      )}
    </Div>
  );
};

export default WalletScreen;