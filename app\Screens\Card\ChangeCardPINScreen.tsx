import React, { useState } from "react";
import {
  StyleSheet,
  View,
  SafeAreaView,
  Alert,
} from "react-native";
import { colors } from "../../Config/colors";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Button from "../../Components/Button";
import Input from "../../Components/Input";

interface ChangeCardPINScreenProps {
  navigation: any;
}

export default function ChangeCardPINScreen({
  navigation,
}: ChangeCardPINScreenProps) {
  const [newPin, setNewPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [loading, setLoading] = useState(false);

  // Handle confirm PIN change
  const handleConfirmPINChange = () => {
    // Validate inputs
    if (!newPin || !confirmPin) {
      Alert.alert("Error", "Please enter both PIN fields");
      return;
    }

    if (newPin.length < 4 || confirmPin.length < 4) {
      Alert.alert("Error", "PIN must be at least 4 digits");
      return;
    }

    if (newPin !== confirmPin) {
      Alert.alert("Error", "PINs do not match");
      return;
    }

    // Show loading
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      Alert.alert(
        "Success",
        "Your card PIN has been updated successfully",
        [
          {
            text: "OK",
            onPress: () => navigation.pop(2),
          },
        ]
      );
    }, 1500);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header title="New Card PIN" showNotification={true} />

        <View style={styles.content}>
          <View style={styles.inputContainer}>
            <Input
              label="New Card PIN"
              placeholder="Enter new PIN"
              value={newPin}
              onChangeText={setNewPin}
              keyboardType="numeric"
              maxLenght={4}
              type="password"
            />
          </View>

          <View style={styles.inputContainer}>
            <Input
              label="Confirm New Card PIN"
              placeholder="Confirm new PIN"
              value={confirmPin}
              onChangeText={setConfirmPin}
              keyboardType="numeric"
              maxLenght={4}
              type="password"
            />
          </View>

          <View style={styles.buttonContainer}>
            <Button
              btnText="Confirm"
              onPress={handleConfirmPINChange}
              loading={loading}
            />
          </View>
        </View>
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  buttonContainer: {
    marginTop: 20,
  },
});
