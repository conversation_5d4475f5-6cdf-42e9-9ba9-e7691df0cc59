export const svg = {
  // Wallet icons
  topUp: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.54 14.05C18.12 14.46 17.88 15.05 17.94 15.68C18.03 16.76 19.02 17.55 20.1 17.55H22V18.74C22 20.81 20.31 22.5 18.24 22.5H8.13C8.44 22.24 8.71 21.92 8.92 21.56C9.29 20.96 9.5 20.25 9.5 19.5C9.5 17.29 7.71 15.5 5.5 15.5C4.56 15.5 3.69 15.83 3 16.38V12.01C3 9.94001 4.69 8.25 6.76 8.25H18.24C20.31 8.25 22 9.94001 22 12.01V13.45H19.98C19.42 13.45 18.91 13.67 18.54 14.05Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3 12.9098V8.33986C3 7.14986 3.73 6.08982 4.84 5.66982L12.78 2.66982C14.02 2.19982 15.35 3.11985 15.35 4.44985V8.24983" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M23.0588 14.4702V16.5302C23.0588 17.0802 22.6188 17.5302 22.0588 17.5502H20.0988C19.0188 17.5502 18.0288 16.7602 17.9388 15.6802C17.8788 15.0502 18.1188 14.4602 18.5388 14.0502C18.9088 13.6702 19.4188 13.4502 19.9788 13.4502H22.0588C22.6188 13.4702 23.0588 13.9202 23.0588 14.4702Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.5 12.5H14.5" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.5 19.5C9.5 20.25 9.29 20.96 8.92 21.56C8.71 21.92 8.44 22.24 8.13 22.5C7.43 23.13 6.51 23.5 5.5 23.5C4.04 23.5 2.77 22.72 2.08 21.56C1.71 20.96 1.5 20.25 1.5 19.5C1.5 18.24 2.08 17.11 3 16.38C3.69 15.83 4.56 15.5 5.5 15.5C7.71 15.5 9.5 17.29 9.5 19.5Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.99171 19.4795H4.01172" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.5 18.0195V21.0095" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

  send: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.5 11.4998L21.7 3.2998" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22.4992 7.3V2.5H17.6992" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.5 2.5H9.5C4.5 2.5 2.5 4.5 2.5 9.5V15.5C2.5 20.5 4.5 22.5 9.5 22.5H15.5C20.5 22.5 22.5 20.5 22.5 15.5V13.5" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

  convert: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22.5 12.5C22.5 18.02 18.02 22.5 12.5 22.5C6.98 22.5 3.61 16.94 3.61 16.94M3.61 16.94H8.13M3.61 16.94V21.94M2.5 12.5C2.5 6.98 6.94 2.5 12.5 2.5C19.17 2.5 22.5 8.06 22.5 8.06M22.5 8.06V3.06M22.5 8.06H18.06" stroke="#1E1E1E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

  notification: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.0201 2.91C8.71009 2.91 6.02009 5.6 6.02009 8.91V11.8C6.02009 12.41 5.76009 13.34 5.45009 13.86L4.30009 15.77C3.59009 16.95 4.08009 18.26 5.38009 18.7C9.69009 20.14 14.3401 20.14 18.6501 18.7C19.8601 18.3 20.3901 16.87 19.7301 15.77L18.5801 13.86C18.2801 13.34 18.0201 12.41 18.0201 11.8V8.91C18.0201 5.61 15.3201 2.91 12.0201 2.91Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"/>
    <path d="M13.8699 3.2C13.5599 3.11 13.2399 3.04 12.9099 3C11.9499 2.88 11.0299 2.95 10.1699 3.2C10.4599 2.46 11.1799 1.94 12.0199 1.94C12.8599 1.94 13.5799 2.46 13.8699 3.2Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M15.02 19.06C15.02 20.71 13.67 22.06 12.02 22.06C11.2 22.06 10.44 21.72 9.90002 21.18C9.36002 20.64 9.02002 19.88 9.02002 19.06" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10"/>
  </svg>`,
  // Profile icons
  settings: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1.33398 8.58679V7.41345C1.33398 6.72012 1.90065 6.14679 2.60065 6.14679C3.80732 6.14679 4.30065 5.29345 3.69398 4.24679C3.34732 3.64679 3.55398 2.86679 4.16065 2.52012L5.31398 1.86012C5.84065 1.54679 6.52065 1.73345 6.83398 2.26012L6.90732 2.38679C7.50732 3.43345 8.49398 3.43345 9.10065 2.38679L9.17398 2.26012C9.48732 1.73345 10.1673 1.54679 10.694 1.86012L11.8473 2.52012C12.454 2.86679 12.6607 3.64679 12.314 4.24679C11.7073 5.29345 12.2007 6.14679 13.4073 6.14679C14.1007 6.14679 14.674 6.71345 14.674 7.41345V8.58679C14.674 9.28012 14.1073 9.85345 13.4073 9.85345C12.2007 9.85345 11.7073 10.7068 12.314 11.7535C12.6607 12.3601 12.454 13.1335 11.8473 13.4801L10.694 14.1401C10.1673 14.4535 9.48732 14.2668 9.17398 13.7401L9.10065 13.6135C8.50065 12.5668 7.51398 12.5668 6.90732 13.6135L6.83398 13.7401C6.52065 14.2668 5.84065 14.4535 5.31398 14.1401L4.16065 13.4801C3.55398 13.1335 3.34732 12.3535 3.69398 11.7535C4.30065 10.7068 3.80732 9.85345 2.60065 9.85345C1.90065 9.85345 1.33398 9.28012 1.33398 8.58679Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

  privacy: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 6.66683V5.3335C4 3.12683 4.66667 1.3335 8 1.3335C11.3333 1.3335 12 3.12683 12 5.3335V6.66683" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.00065 12.3333C8.92113 12.3333 9.66732 11.5871 9.66732 10.6667C9.66732 9.74619 8.92113 9 8.00065 9C7.08018 9 6.33398 9.74619 6.33398 10.6667C6.33398 11.5871 7.08018 12.3333 8.00065 12.3333Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.334 14.6665H4.66732C2.00065 14.6665 1.33398 13.9998 1.33398 11.3332V9.99984C1.33398 7.33317 2.00065 6.6665 4.66732 6.6665H11.334C14.0007 6.6665 14.6673 7.33317 14.6673 9.99984V11.3332C14.6673 13.9998 14.0007 14.6665 11.334 14.6665Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

  support: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.00065 14.6668C11.6673 14.6668 14.6673 11.6668 14.6673 8.00016C14.6673 4.3335 11.6673 1.3335 8.00065 1.3335C4.33398 1.3335 1.33398 4.3335 1.33398 8.00016C1.33398 11.6668 4.33398 14.6668 8.00065 14.6668Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 5.3335V8.66683" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.99609 10.6665H8.00208" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

  terms: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.58594 8.00005L7.1926 9.61339L10.4126 6.38672" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.16594 1.63338C7.62594 1.24005 8.37927 1.24005 8.84594 1.63338L9.89927 2.54005C10.0993 2.71338 10.4726 2.85338 10.7393 2.85338H11.8726C12.5793 2.85338 13.1593 3.43338 13.1593 4.14005V5.27338C13.1593 5.53338 13.2993 5.91338 13.4726 6.11338L14.3793 7.16671C14.7726 7.62671 14.7726 8.38005 14.3793 8.84671L13.4726 9.90005C13.2993 10.1 13.1593 10.4734 13.1593 10.74V11.8734C13.1593 12.58 12.5793 13.16 11.8726 13.16H10.7393C10.4793 13.16 10.0993 13.3 9.89927 13.4734L8.84594 14.38C8.38594 14.7734 7.6326 14.7734 7.16594 14.38L6.1126 13.4734C5.9126 13.3 5.53927 13.16 5.2726 13.16H4.11927C3.4126 13.16 2.8326 12.58 2.8326 11.8734V10.7334C2.8326 10.4734 2.6926 10.1 2.52594 9.90005L1.62594 8.84005C1.23927 8.38005 1.23927 7.63338 1.62594 7.17338L2.52594 6.11338C2.6926 5.91338 2.8326 5.54005 2.8326 5.28005V4.13338C2.8326 3.42671 3.4126 2.84671 4.11927 2.84671H5.2726C5.5326 2.84671 5.9126 2.70671 6.1126 2.53338L7.16594 1.63338Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

  logout: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.93359 5.04016C6.14026 2.64016 7.37359 1.66016 10.0736 1.66016H10.1603C13.1403 1.66016 14.3336 2.85349 14.3336 5.83349V10.1802C14.3336 13.1602 13.1403 14.3535 10.1603 14.3535H10.0736C7.39359 14.3535 6.16026 13.3868 5.94026 11.0268" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.0007 8H2.41406" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3.89935 5.7666L1.66602 7.99994L3.89935 10.2333" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

  goBack: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9.57 5.92993L3.5 11.9999L9.57 18.0699" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M20.4999 12H3.66992" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
  home: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.52 3.34016L4.13 7.54016C3.23 8.24016 2.5 9.73016 2.5 10.8602V18.2702C2.5 20.5902 4.39 22.4902 6.71 22.4902H18.29C20.61 22.4902 22.5 20.5902 22.5 18.2802V11.0002C22.5 9.79016 21.69 8.24016 20.7 7.55016L14.52 3.22016C13.12 2.24016 10.87 2.29016 9.52 3.34016Z" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.5 18.4902V15.4902" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  homeActive: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.5402 7.31969L14.7802 3.28969C13.2102 2.18969 10.8002 2.24969 9.29023 3.41969L4.28023 7.32969C3.28023 8.10969 2.49023 9.70969 2.49023 10.9697V17.8697C2.49023 20.4197 4.56023 22.4997 7.11023 22.4997H17.8902C20.4402 22.4997 22.5102 20.4297 22.5102 17.8797V11.0997C22.5102 9.74969 21.6402 8.08969 20.5402 7.31969ZM13.2502 18.4997C13.2502 18.9097 12.9102 19.2497 12.5002 19.2497C12.0902 19.2497 11.7502 18.9097 11.7502 18.4997V15.4997C11.7502 15.0897 12.0902 14.7497 12.5002 14.7497C12.9102 14.7497 13.2502 15.0897 13.2502 15.4997V18.4997Z" fill="#F7C148"/>
</svg>`,
  wallet: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.54 14.05C18.12 14.46 17.88 15.05 17.94 15.68C18.03 16.76 19.02 17.55 20.1 17.55H22V18.74C22 20.81 20.31 22.5 18.24 22.5H6.76C4.69 22.5 3 20.81 3 18.74V12.01C3 9.94001 4.69 8.25 6.76 8.25H18.24C20.31 8.25 22 9.94001 22 12.01V13.45H19.98C19.42 13.45 18.91 13.67 18.54 14.05Z" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3 12.9103V8.34035C3 7.15035 3.73 6.0903 4.84 5.6703L12.78 2.6703C14.02 2.2003 15.35 3.12033 15.35 4.45033V8.25032" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M23.0588 14.4702V16.5302C23.0588 17.0802 22.6188 17.5302 22.0588 17.5502H20.0988C19.0188 17.5502 18.0288 16.7602 17.9388 15.6802C17.8788 15.0502 18.1188 14.4602 18.5388 14.0502C18.9088 13.6702 19.4188 13.4502 19.9788 13.4502H22.0588C22.6188 13.4702 23.0588 13.9202 23.0588 14.4702Z" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.5 12.5H14.5" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

`,
  walletActive: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.35 4.4498V8.2498H13.85V4.4498C13.85 4.1798 13.61 4.0498 13.45 4.0498C13.4 4.0498 13.35 4.0598 13.3 4.0798L5.37 7.06981C4.84 7.2698 4.5 7.7698 4.5 8.3398V9.0098C3.59 9.6898 3 10.7798 3 12.0098V8.3398C3 7.1498 3.73 6.0898 4.84 5.6698L12.78 2.6698C13 2.5898 13.23 2.5498 13.45 2.5498C14.45 2.5498 15.35 3.3598 15.35 4.4498Z" fill="#F7C148"/>
<path d="M22.0007 15V16C22.0007 16.27 21.7907 16.49 21.5107 16.5H20.0507C19.5207 16.5 19.0407 16.11 19.0007 15.59C18.9707 15.28 19.0907 14.99 19.2907 14.79C19.4707 14.6 19.7207 14.5 19.9907 14.5H21.5007C21.7907 14.51 22.0007 14.73 22.0007 15Z" fill="#F7C148"/>
<path d="M19.98 13.45H21C21.55 13.45 22 13 22 12.45V12.01C22 9.94 20.31 8.25 18.24 8.25H6.76C5.91 8.25 5.13 8.53 4.5 9.01C3.59 9.69 3 10.78 3 12.01V18.74C3 20.81 4.69 22.5 6.76 22.5H18.24C20.31 22.5 22 20.81 22 18.74V18.55C22 18 21.55 17.55 21 17.55H20.13C19.17 17.55 18.25 16.96 18 16.03C17.79 15.27 18.04 14.54 18.54 14.05C18.91 13.67 19.42 13.45 19.98 13.45ZM14.5 13.25H7.5C7.09 13.25 6.75 12.91 6.75 12.5C6.75 12.09 7.09 11.75 7.5 11.75H14.5C14.91 11.75 15.25 12.09 15.25 12.5C15.25 12.91 14.91 13.25 14.5 13.25Z" fill="#F7C148"/>
</svg>

`,
  card: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 9.00488H22" stroke="#555059" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6 17.0049H8" stroke="#555059" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.5 17.0049H14.5" stroke="#555059" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.44 4.00488H17.55C21.11 4.00488 22 4.88488 22 8.39488V16.6049C22 20.1149 21.11 20.9949 17.56 20.9949H6.44C2.89 21.0049 2 20.1249 2 16.6149V8.39488C2 4.88488 2.89 4.00488 6.44 4.00488Z" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  cardActive: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22.5 8.05039C22.5 8.71039 21.96 9.25039 21.3 9.25039H3.7C3.04 9.25039 2.5 8.71039 2.5 8.05039V8.04039C2.5 5.75039 4.35 3.90039 6.64 3.90039H18.35C20.64 3.90039 22.5 5.76039 22.5 8.05039Z" fill="#F7C148"/>
<path d="M2.5 11.95V16.96C2.5 19.25 4.35 21.1 6.64 21.1H18.35C20.64 21.1 22.5 19.24 22.5 16.95V11.95C22.5 11.29 21.96 10.75 21.3 10.75H3.7C3.04 10.75 2.5 11.29 2.5 11.95ZM8.5 17.75H6.5C6.09 17.75 5.75 17.41 5.75 17C5.75 16.59 6.09 16.25 6.5 16.25H8.5C8.91 16.25 9.25 16.59 9.25 17C9.25 17.41 8.91 17.75 8.5 17.75ZM15 17.75H11C10.59 17.75 10.25 17.41 10.25 17C10.25 16.59 10.59 16.25 11 16.25H15C15.41 16.25 15.75 16.59 15.75 17C15.75 17.41 15.41 17.75 15 17.75Z" fill="#F7C148"/>
</svg>

`,
  menu: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.9299 7.26001L18.5599 20.79C18.3199 21.8 17.4199 22.5 16.3799 22.5H3.23989C1.72989 22.5 0.649901 21.0199 1.0999 19.5699L5.30989 6.05005C5.59989 5.11005 6.46991 4.45996 7.44991 4.45996H19.7499C20.6999 4.45996 21.4899 5.03997 21.8199 5.83997C22.0099 6.26997 22.0499 6.76001 21.9299 7.26001Z" stroke="#8E8693" stroke-width="1.5" stroke-miterlimit="10"/>
<path d="M16 22.5H20.78C22.07 22.5 23.08 21.41 22.99 20.12L22 6.5" stroke="#8E8693" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.67993 6.88049L10.7199 2.56055" stroke="#8E8693" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.3799 6.88977L17.3199 2.5498" stroke="#8E8693" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.69995 12.5H15.7" stroke="#8E8693" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.69995 16.5H14.7" stroke="#8E8693" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  menuActive: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.9299 7.26001L18.5599 20.79C18.3199 21.8 17.4199 22.5 16.3799 22.5H3.23989C1.72989 22.5 0.649901 21.0199 1.0999 19.5699L5.30989 6.05005C5.59989 5.11005 6.46991 4.45996 7.44991 4.45996H19.7499C20.6999 4.45996 21.4899 5.03997 21.8199 5.83997C22.0099 6.26997 22.0499 6.76001 21.9299 7.26001Z" stroke="#1E1E1E" stroke-width="1.5" stroke-miterlimit="10"/>
<path d="M16 22.5H20.78C22.07 22.5 23.08 21.41 22.99 20.12L22 6.5" stroke="#1E1E1E" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.67993 6.88049L10.7199 2.56055" stroke="#1E1E1E" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.3799 6.88977L17.3199 2.5498" stroke="#1E1E1E" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.69995 12.5H15.7" stroke="#1E1E1E" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.69995 16.5H14.7" stroke="#1E1E1E" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  arrowLeft: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.57 5.92993L3.5 11.9999L9.57 18.0699" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M20.4999 12H3.66992" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  eye: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.626 3.00008C13.3789 3.05417 16.4579 6.48248 18 9.43226C18 9.43226 17.4573 10.5606 16.9484 11.2829C16.7021 11.6323 16.443 11.9727 16.1707 12.302C15.9766 12.5364 15.7761 12.7646 15.5681 12.9864C13.7094 14.9698 11.04 16.4493 8.22122 15.8749C5.08975 15.2366 2.53539 12.5104 1 9.57831C1 9.57831 1.54516 8.44888 2.05695 7.72766C2.28623 7.4042 2.52687 7.08939 2.77886 6.78395C2.97194 6.54991 3.17212 6.32165 3.37903 6.09987C5.02125 4.34155 7.12842 2.98782 9.626 3.00008ZM9.61287 4.44252C7.52238 4.43458 5.78398 5.62063 4.40866 7.09299C4.22162 7.29313 4.04132 7.4994 3.8667 7.71071C3.63706 7.9891 3.41772 8.27651 3.20867 8.57113C2.99962 8.86538 2.78525 9.23681 2.61525 9.55234C3.96891 11.8635 6.00083 13.951 8.50054 14.4606C10.8299 14.9355 13.0038 13.6308 14.5399 11.9919C14.7276 11.7917 14.909 11.5851 15.0843 11.3734C15.3328 11.073 15.5691 10.7625 15.7938 10.4434C16.0018 10.1481 16.2158 9.77592 16.3855 9.46003C14.9807 7.07099 12.5772 4.4894 9.61287 4.44252Z" fill="black" fill-opacity="0.6"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.5 6.61426C11.0705 6.61426 12.3458 7.90992 12.3458 9.50526C12.3458 11.101 11.0705 12.3963 9.5 12.3963C7.92983 12.3963 6.6546 11.101 6.6546 9.50526C6.6546 7.90992 7.92983 6.61426 9.5 6.61426Z" fill="black" fill-opacity="0.6"/>
</svg>
`,
  eyeOff: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.5299 9.46992L9.46992 14.5299C8.81992 13.8799 8.41992 12.9899 8.41992 11.9999C8.41992 10.0199 10.0199 8.41992 11.9999 8.41992C12.9899 8.41992 13.8799 8.81992 14.5299 9.46992Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.8201 5.77047C16.0701 4.45047 14.0701 3.73047 12.0001 3.73047C8.47009 3.73047 5.18009 5.81047 2.89009 9.41047C1.99009 10.8205 1.99009 13.1905 2.89009 14.6005C3.68009 15.8405 4.60009 16.9105 5.60009 17.7705" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.41992 19.5297C9.55992 20.0097 10.7699 20.2697 11.9999 20.2697C15.5299 20.2697 18.8199 18.1897 21.1099 14.5897C22.0099 13.1797 22.0099 10.8097 21.1099 9.39969C20.7799 8.87969 20.4199 8.38969 20.0499 7.92969" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M15.5099 12.7002C15.2499 14.1102 14.0999 15.2602 12.6899 15.5202" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.47 14.5303L2 22.0003" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22 2L14.53 9.47" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  cloud: `<svg width="57" height="57" viewBox="0 0 57 57" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="28.5" cy="28.5" r="28" fill="#F9F9FA"/>
<path d="M21.4998 25.583C21.4998 22.0392 24.3727 19.1663 27.9165 19.1663C31.0557 19.1663 33.6705 21.4217 34.2247 24.4008C34.3024 24.8186 34.6011 25.161 35.0044 25.2948C37.327 26.0651 38.9998 28.2552 38.9998 30.833C38.9998 34.0547 36.3882 36.6663 33.1665 36.6663C32.5222 36.6663 31.9998 37.1887 31.9998 37.833C31.9998 38.4773 32.5222 38.9997 33.1665 38.9997C37.6768 38.9997 41.3332 35.3433 41.3332 30.833C41.3332 27.4585 39.2871 24.5645 36.3705 23.3192C35.3725 19.584 31.9669 16.833 27.9165 16.833C23.084 16.833 19.1665 20.7505 19.1665 25.583C19.1665 25.7 19.1688 25.8165 19.1734 25.9325C17.0787 27.141 15.6665 29.4043 15.6665 31.9997C15.6665 35.8657 18.8005 38.9997 22.6665 38.9997C23.3108 38.9997 23.8332 38.4773 23.8332 37.833C23.8332 37.1887 23.3108 36.6663 22.6665 36.6663C20.0892 36.6663 17.9998 34.577 17.9998 31.9997C17.9998 30.0661 19.176 28.4046 20.8566 27.6966C21.3429 27.4918 21.6312 26.986 21.5596 26.4632C21.5202 26.176 21.4998 25.8822 21.4998 25.583Z" fill="#555059"/>
<path d="M27.7247 31.1277C28.1668 30.7348 28.8329 30.7348 29.2749 31.1277L31.0249 32.6833C31.5065 33.1113 31.5499 33.8487 31.1218 34.3303C30.7473 34.7516 30.1361 34.8376 29.6665 34.5659V40.1663C29.6665 40.8107 29.1442 41.333 28.4998 41.333C27.8555 41.333 27.3332 40.8107 27.3332 40.1663V34.5659C26.8636 34.8376 26.2523 34.7516 25.8779 34.3303C25.4498 33.8487 25.4932 33.1113 25.9747 32.6833L27.7247 31.1277Z" fill="#555059"/>
</svg>
`,
  clock: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15259)">
<g clip-path="url(#clip1_2454_15259)">
<path d="M8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0ZM7.25 3.75V8C7.25 8.25 7.375 8.48438 7.58437 8.625L10.5844 10.625C10.9281 10.8562 11.3938 10.7625 11.625 10.4156C11.8562 10.0687 11.7625 9.60625 11.4156 9.375L8.75 7.6V3.75C8.75 3.33437 8.41562 3 8 3C7.58437 3 7.25 3.33437 7.25 3.75Z" fill="black"/>
</g>
</g>
<defs>
<clipPath id="clip0_2454_15259">
<rect width="16" height="16" fill="white"/>
</clipPath>
<clipPath id="clip1_2454_15259">
<path d="M0 0H16V16H0V0Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  info: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15265)">
<path d="M4.89062 13.9906L4.49687 14.9125C3.9125 14.6156 3.375 14.25 2.8875 13.8219L3.59687 13.1125C3.9875 13.4531 4.42188 13.75 4.89062 13.9906ZM1.26875 8.5H0.265625C0.309375 9.1625 0.434375 9.80313 0.63125 10.4094L1.5625 10.0375C1.40937 9.54688 1.30625 9.03125 1.26875 8.5ZM1.26875 7.5C1.3125 6.9125 1.43125 6.34375 1.61563 5.80937L0.69375 5.41563C0.459375 6.07188 0.3125 6.77187 0.265625 7.5H1.26875ZM2.00938 4.89062C2.25313 4.425 2.54688 3.99062 2.8875 3.59375L2.17812 2.88438C1.75 3.37188 1.38125 3.90938 1.0875 4.49375L2.00938 4.89062ZM12.4062 13.1125C11.9719 13.4875 11.4875 13.8094 10.9656 14.0625L11.3375 14.9937C11.9844 14.6844 12.5813 14.2875 13.1156 13.8188L12.4062 13.1125ZM3.59375 2.8875C4.02813 2.5125 4.5125 2.19062 5.03438 1.9375L4.6625 1.00625C4.01562 1.31562 3.41875 1.7125 2.8875 2.18125L3.59375 2.8875ZM13.9906 11.1094C13.7469 11.575 13.4531 12.0094 13.1125 12.4062L13.8219 13.1156C14.25 12.6281 14.6188 12.0875 14.9125 11.5063L13.9906 11.1094ZM14.7312 8.5C14.6875 9.0875 14.5687 9.65625 14.3844 10.1906L15.3062 10.5844C15.5406 9.925 15.6875 9.225 15.7312 8.49687H14.7312V8.5ZM10.0375 14.4375C9.54688 14.5938 9.03125 14.6937 8.5 14.7312V15.7344C9.1625 15.6906 9.80313 15.5656 10.4094 15.3687L10.0375 14.4375ZM7.5 14.7312C6.9125 14.6875 6.34375 14.5687 5.80937 14.3844L5.41563 15.3062C6.075 15.5406 6.775 15.6875 7.50313 15.7312V14.7312H7.5ZM14.4375 5.9625C14.5938 6.45312 14.6937 6.96875 14.7312 7.5H15.7344C15.6906 6.8375 15.5656 6.19687 15.3687 5.59062L14.4375 5.9625ZM2.8875 12.4062C2.5125 11.9719 2.19062 11.4875 1.9375 10.9656L1.00625 11.3375C1.31562 11.9844 1.7125 12.5813 2.18125 13.1156L2.8875 12.4062ZM8.5 1.26875C9.0875 1.3125 9.65312 1.43125 10.1906 1.61563L10.5844 0.69375C9.92813 0.459375 9.22812 0.3125 8.5 0.265625V1.26875ZM5.9625 1.5625C6.45312 1.40625 6.96875 1.30625 7.5 1.26875V0.265625C6.8375 0.309375 6.19687 0.434375 5.59062 0.63125L5.9625 1.5625ZM13.8219 2.88438L13.1125 3.59375C13.4875 4.02813 13.8094 4.5125 14.0656 5.03438L14.9969 4.6625C14.6875 4.01562 14.2906 3.41875 13.8219 2.88438ZM12.4062 2.8875L13.1156 2.17812C12.6281 1.75 12.0906 1.38125 11.5063 1.0875L11.1125 2.00938C11.575 2.25313 12.0125 2.54688 12.4062 2.8875Z" fill="black"/>
<path d="M8 12.25C8.48325 12.25 8.875 11.8582 8.875 11.375C8.875 10.8918 8.48325 10.5 8 10.5C7.51675 10.5 7.125 10.8918 7.125 11.375C7.125 11.8582 7.51675 12.25 8 12.25Z" fill="black"/>
<path d="M8.24069 9.75H7.74069C7.53444 9.75 7.36569 9.58125 7.36569 9.375C7.36569 7.15625 9.78444 7.37812 9.78444 6.00625C9.78444 5.38125 9.22819 4.75 7.99069 4.75C7.08132 4.75 6.60632 5.05 6.14069 5.64687C6.01882 5.80312 5.79382 5.83438 5.63444 5.72188L5.22507 5.43437C5.05007 5.3125 5.00944 5.06562 5.14382 4.89687C5.80632 4.04687 6.59382 3.5 7.99382 3.5C9.62819 3.5 11.0376 4.43125 11.0376 6.00625C11.0376 8.11875 8.61882 7.99062 8.61882 9.375C8.61569 9.58125 8.44694 9.75 8.24069 9.75Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_2454_15265">
<path d="M0 0H16V16H0V0Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  idCard: `<svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15273)">
<path d="M0 0H18V16H0V0Z" stroke="#E5E7EB"/>
<path d="M0 3H18C18 1.89688 17.1031 1 16 1H2C0.896875 1 0 1.89688 0 3ZM0 4V13C0 14.1031 0.896875 15 2 15H16C17.1031 15 18 14.1031 18 13V4H0ZM2 12.6656C2 11.7437 2.74688 11 3.66563 11H7.33437C8.25625 11 9 11.7469 9 12.6656C9 12.85 8.85 13 8.66562 13H2.33437C2.15 13 2 12.85 2 12.6656ZM5.5 6C6.03043 6 6.53914 6.21071 6.91421 6.58579C7.28929 6.96086 7.5 7.46957 7.5 8C7.5 8.53043 7.28929 9.03914 6.91421 9.41421C6.53914 9.78929 6.03043 10 5.5 10C4.96957 10 4.46086 9.78929 4.08579 9.41421C3.71071 9.03914 3.5 8.53043 3.5 8C3.5 7.46957 3.71071 6.96086 4.08579 6.58579C4.46086 6.21071 4.96957 6 5.5 6ZM11 6.5C11 6.225 11.225 6 11.5 6H15.5C15.775 6 16 6.225 16 6.5C16 6.775 15.775 7 15.5 7H11.5C11.225 7 11 6.775 11 6.5ZM11 8.5C11 8.225 11.225 8 11.5 8H15.5C15.775 8 16 8.225 16 8.5C16 8.775 15.775 9 15.5 9H11.5C11.225 9 11 8.775 11 8.5ZM11 10.5C11 10.225 11.225 10 11.5 10H15.5C15.775 10 16 10.225 16 10.5C16 10.775 15.775 11 15.5 11H11.5C11.225 11 11 10.775 11 10.5Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_2454_15273">
<rect width="18" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  cam: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15278)">
<path d="M0 0H16V16H0V0Z" stroke="#E5E7EB"/>
<path d="M4.65938 2.025L4.33437 3H2C0.896875 3 0 3.89687 0 5V13C0 14.1031 0.896875 15 2 15H14C15.1031 15 16 14.1031 16 13V5C16 3.89687 15.1031 3 14 3H11.6656L11.3406 2.025C11.1375 1.4125 10.5656 1 9.91875 1H6.08125C5.43438 1 4.8625 1.4125 4.65938 2.025ZM8 6C8.79565 6 9.55871 6.31607 10.1213 6.87868C10.6839 7.44129 11 8.20435 11 9C11 9.79565 10.6839 10.5587 10.1213 11.1213C9.55871 11.6839 8.79565 12 8 12C7.20435 12 6.44129 11.6839 5.87868 11.1213C5.31607 10.5587 5 9.79565 5 9C5 8.20435 5.31607 7.44129 5.87868 6.87868C6.44129 6.31607 7.20435 6 8 6Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_2454_15278">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  passport: `<svg width="21" height="30" viewBox="0 0 21 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 0H21V30H0V0Z" stroke="#E5E7EB"/>
<g clip-path="url(#clip0_2454_15446)">
<path d="M0 7C0 5.34531 1.34531 4 3 4H18C19.6547 4 21 5.34531 21 7V25C21 26.6547 19.6547 28 18 28H3C1.34531 28 0 26.6547 0 25V7ZM8.57812 17.0687C7.27031 16.45 6.30938 15.2219 6.06094 13.75H7.89375C7.96875 15.175 8.25469 16.2719 8.57812 17.0687ZM10.5141 17.5H10.5H10.4859C10.3734 17.3359 10.2188 17.0828 10.0594 16.7266C9.77813 16.0891 9.47812 15.1187 9.39375 13.75H11.6016C11.5172 15.1187 11.2219 16.0891 10.9359 16.7266C10.7766 17.0828 10.6219 17.3359 10.5094 17.5H10.5141ZM12.4219 17.0687C12.7406 16.2672 13.0266 15.175 13.1063 13.75H14.9391C14.6906 15.2219 13.7297 16.45 12.4219 17.0687ZM13.1063 12.25C13.0312 10.825 12.7453 9.72812 12.4219 8.93125C13.7297 9.55 14.6906 10.7781 14.9391 12.25H13.1063ZM10.4859 8.5H10.5H10.5141C10.6266 8.66406 10.7812 8.91719 10.9406 9.27344C11.2219 9.91094 11.5219 10.8812 11.6063 12.25H9.39844C9.48281 10.8812 9.77812 9.91094 10.0641 9.27344C10.2234 8.91719 10.3781 8.66406 10.4906 8.5H10.4859ZM8.57812 8.93125C8.25937 9.73281 7.97344 10.825 7.89375 12.25H6.06094C6.30938 10.7781 7.27031 9.55 8.57812 8.93125ZM16.5 13C16.5 11.4087 15.8679 9.88258 14.7426 8.75736C13.6174 7.63214 12.0913 7 10.5 7C8.9087 7 7.38258 7.63214 6.25736 8.75736C5.13214 9.88258 4.5 11.4087 4.5 13C4.5 14.5913 5.13214 16.1174 6.25736 17.2426C7.38258 18.3679 8.9087 19 10.5 19C12.0913 19 13.6174 18.3679 14.7426 17.2426C15.8679 16.1174 16.5 14.5913 16.5 13ZM5.25 22C4.8375 22 4.5 22.3375 4.5 22.75C4.5 23.1625 4.8375 23.5 5.25 23.5H15.75C16.1625 23.5 16.5 23.1625 16.5 22.75C16.5 22.3375 16.1625 22 15.75 22H5.25Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_2454_15446">
<path d="M0 4H21V28H0V4Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  infoBold: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15430)">
<g clip-path="url(#clip1_2454_15430)">
<path d="M8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16ZM6.75 10.5H7.5V8.5H6.75C6.33437 8.5 6 8.16562 6 7.75C6 7.33437 6.33437 7 6.75 7H8.25C8.66562 7 9 7.33437 9 7.75V10.5H9.25C9.66562 10.5 10 10.8344 10 11.25C10 11.6656 9.66562 12 9.25 12H6.75C6.33437 12 6 11.6656 6 11.25C6 10.8344 6.33437 10.5 6.75 10.5ZM8 4C8.26522 4 8.51957 4.10536 8.70711 4.29289C8.89464 4.48043 9 4.73478 9 5C9 5.26522 8.89464 5.51957 8.70711 5.70711C8.51957 5.89464 8.26522 6 8 6C7.73478 6 7.48043 5.89464 7.29289 5.70711C7.10536 5.51957 7 5.26522 7 5C7 4.73478 7.10536 4.48043 7.29289 4.29289C7.48043 4.10536 7.73478 4 8 4Z" fill="black"/>
</g>
</g>
<defs>
<clipPath id="clip0_2454_15430">
<rect width="16" height="16" fill="white"/>
</clipPath>
<clipPath id="clip1_2454_15430">
<path d="M0 0H16V16H0V0Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  bulb: `<svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15329)">
<g clip-path="url(#clip1_2454_15329)">
<path d="M8.5 12C8.8 11.0031 9.42188 10.1531 10.0375 9.30625C10.2 9.08437 10.3625 8.8625 10.5188 8.6375C11.1375 7.74687 11.5 6.66875 11.5 5.50313C11.5 2.4625 9.0375 0 6 0C2.9625 0 0.5 2.4625 0.5 5.5C0.5 6.66563 0.8625 7.74687 1.48125 8.63437C1.6375 8.85938 1.8 9.08125 1.9625 9.30313C2.58125 10.15 3.20312 11.0031 3.5 11.9969H8.5V12ZM6 16C7.38125 16 8.5 14.8813 8.5 13.5V13H3.5V13.5C3.5 14.8813 4.61875 16 6 16ZM3.5 5.5C3.5 5.775 3.275 6 3 6C2.725 6 2.5 5.775 2.5 5.5C2.5 3.56562 4.06562 2 6 2C6.275 2 6.5 2.225 6.5 2.5C6.5 2.775 6.275 3 6 3C4.61875 3 3.5 4.11875 3.5 5.5Z" fill="#222222"/>
</g>
</g>
<defs>
<clipPath id="clip0_2454_15329">
<rect width="12" height="16" fill="white"/>
</clipPath>
<clipPath id="clip1_2454_15329">
<path d="M0 0H12V16H0V0Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  dimension: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15334)">
<g clip-path="url(#clip1_2454_15334)">
<path d="M14 3.41563L15.7063 1.70938C16.0969 1.31875 16.0969 0.684375 15.7063 0.29375C15.3156 -0.096875 14.6812 -0.096875 14.2906 0.29375L12.5844 2H5V4H10.5844L4 10.5844V1C4 0.446875 3.55312 0 3 0C2.44688 0 2 0.446875 2 1V2H1C0.446875 2 0 2.44688 0 3C0 3.55312 0.446875 4 1 4H2V12C2 13.1031 2.89687 14 4 14H11V12H5.41563L12 5.41563V15C12 15.5531 12.4469 16 13 16C13.5531 16 14 15.5531 14 15V14H15C15.5531 14 16 13.5531 16 13C16 12.4469 15.5531 12 15 12H14V3.41563Z" fill="#222222"/>
</g>
</g>
<defs>
<clipPath id="clip0_2454_15334">
<rect width="16" height="16" fill="white"/>
</clipPath>
<clipPath id="clip1_2454_15334">
<path d="M0 0H16V16H0V0Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  cursor: `<svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15339)">
<g clip-path="url(#clip1_2454_15339)">
<path d="M1 1C1 0.446875 1.44687 0 2 0C2.55312 0 3 0.446875 3 1V7.5H1V1ZM7 6C7 5.44688 7.44688 5 8 5C8.55313 5 9 5.44688 9 6V8C9 8.55313 8.55313 9 8 9C7.44688 9 7 8.55313 7 8V6ZM5 4C5.55312 4 6 4.44688 6 5V6.5C6 7.05312 5.55312 7.5 5 7.5C4.44688 7.5 4 7.05312 4 6.5V5C4 4.44688 4.44688 4 5 4ZM10 7C10 6.44688 10.4469 6 11 6C11.5531 6 12 6.44688 12 7V9C12 9.55313 11.5531 10 11 10C10.4469 10 10 9.55313 10 9V7ZM7 9.75V9.73125C7.29375 9.9 7.63438 10 8 10C8.4125 10 8.79375 9.875 9.1125 9.6625C9.38438 10.4406 10.1281 11 11 11C11.3656 11 11.7063 10.9031 12 10.7312V11C12 13.7625 9.7625 16 7 16H5.07188C3.74688 16 2.475 15.4719 1.5375 14.5344L1.17188 14.1719C0.421875 13.4219 0 12.4031 0 11.3438V10.5C0 9.39688 0.896875 8.5 2 8.5H4.75C5.44063 8.5 6 9.05937 6 9.75C6 10.4406 5.44063 11 4.75 11H3C2.725 11 2.5 11.225 2.5 11.5C2.5 11.775 2.725 12 3 12H4.75C5.99375 12 7 10.9937 7 9.75Z" fill="#222222"/>
</g>
</g>
<defs>
<clipPath id="clip0_2454_15339">
<rect width="12" height="16" fill="white"/>
</clipPath>
<clipPath id="clip1_2454_15339">
<path d="M0 0H12V16H0V0Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  info2: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2454_15558)">
<path d="M4.89062 13.9906L4.49687 14.9125C3.9125 14.6156 3.375 14.25 2.8875 13.8219L3.59687 13.1125C3.9875 13.4531 4.42188 13.75 4.89062 13.9906ZM1.26875 8.5H0.265625C0.309375 9.1625 0.434375 9.80313 0.63125 10.4094L1.5625 10.0375C1.40937 9.54688 1.30625 9.03125 1.26875 8.5ZM1.26875 7.5C1.3125 6.9125 1.43125 6.34375 1.61563 5.80937L0.69375 5.41563C0.459375 6.07188 0.3125 6.77187 0.265625 7.5H1.26875ZM2.00938 4.89062C2.25313 4.425 2.54688 3.99062 2.8875 3.59375L2.17812 2.88438C1.75 3.37188 1.38125 3.90938 1.0875 4.49375L2.00938 4.89062ZM12.4062 13.1125C11.9719 13.4875 11.4875 13.8094 10.9656 14.0625L11.3375 14.9937C11.9844 14.6844 12.5813 14.2875 13.1156 13.8188L12.4062 13.1125ZM3.59375 2.8875C4.02813 2.5125 4.5125 2.19062 5.03438 1.9375L4.6625 1.00625C4.01562 1.31562 3.41875 1.7125 2.8875 2.18125L3.59375 2.8875ZM13.9906 11.1094C13.7469 11.575 13.4531 12.0094 13.1125 12.4062L13.8219 13.1156C14.25 12.6281 14.6188 12.0875 14.9125 11.5063L13.9906 11.1094ZM14.7312 8.5C14.6875 9.0875 14.5687 9.65625 14.3844 10.1906L15.3062 10.5844C15.5406 9.925 15.6875 9.225 15.7312 8.49687H14.7312V8.5ZM10.0375 14.4375C9.54688 14.5938 9.03125 14.6937 8.5 14.7312V15.7344C9.1625 15.6906 9.80313 15.5656 10.4094 15.3687L10.0375 14.4375ZM7.5 14.7312C6.9125 14.6875 6.34375 14.5687 5.80937 14.3844L5.41563 15.3062C6.075 15.5406 6.775 15.6875 7.50313 15.7312V14.7312H7.5ZM14.4375 5.9625C14.5938 6.45312 14.6937 6.96875 14.7312 7.5H15.7344C15.6906 6.8375 15.5656 6.19687 15.3687 5.59062L14.4375 5.9625ZM2.8875 12.4062C2.5125 11.9719 2.19062 11.4875 1.9375 10.9656L1.00625 11.3375C1.31562 11.9844 1.7125 12.5813 2.18125 13.1156L2.8875 12.4062ZM8.5 1.26875C9.0875 1.3125 9.65312 1.43125 10.1906 1.61563L10.5844 0.69375C9.92813 0.459375 9.22812 0.3125 8.5 0.265625V1.26875ZM5.9625 1.5625C6.45312 1.40625 6.96875 1.30625 7.5 1.26875V0.265625C6.8375 0.309375 6.19687 0.434375 5.59062 0.63125L5.9625 1.5625ZM13.8219 2.88438L13.1125 3.59375C13.4875 4.02813 13.8094 4.5125 14.0656 5.03438L14.9969 4.6625C14.6875 4.01562 14.2906 3.41875 13.8219 2.88438ZM12.4062 2.8875L13.1156 2.17812C12.6281 1.75 12.0906 1.38125 11.5063 1.0875L11.1125 2.00938C11.575 2.25313 12.0125 2.54688 12.4062 2.8875Z" fill="black"/>
<path d="M8 12.25C8.48325 12.25 8.875 11.8582 8.875 11.375C8.875 10.8918 8.48325 10.5 8 10.5C7.51675 10.5 7.125 10.8918 7.125 11.375C7.125 11.8582 7.51675 12.25 8 12.25Z" fill="black"/>
<path d="M8.24069 9.75H7.74069C7.53444 9.75 7.36569 9.58125 7.36569 9.375C7.36569 7.15625 9.78444 7.37812 9.78444 6.00625C9.78444 5.38125 9.22819 4.75 7.99069 4.75C7.08132 4.75 6.60632 5.05 6.14069 5.64687C6.01882 5.80312 5.79382 5.83438 5.63444 5.72188L5.22507 5.43437C5.05007 5.3125 5.00944 5.06562 5.14382 4.89687C5.80632 4.04687 6.59382 3.5 7.99382 3.5C9.62819 3.5 11.0376 4.43125 11.0376 6.00625C11.0376 8.11875 8.61882 7.99062 8.61882 9.375C8.61569 9.58125 8.44694 9.75 8.24069 9.75Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_2454_15558">
<path d="M0 0H16V16H0V0Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  bell: `<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="40" height="40" rx="20" fill="#FEF9ED"/>
<path d="M20.0206 10.9102C16.7106 10.9102 14.0206 13.6002 14.0206 16.9102V19.8002C14.0206 20.4102 13.7606 21.3402 13.4506 21.8602L12.3006 23.7702C11.5906 24.9502 12.0806 26.2602 13.3806 26.7002C17.6906 28.1402 22.3406 28.1402 26.6506 26.7002C27.8606 26.3002 28.3906 24.8702 27.7306 23.7702L26.5806 21.8602C26.2806 21.3402 26.0206 20.4102 26.0206 19.8002V16.9102C26.0206 13.6102 23.3206 10.9102 20.0206 10.9102Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M21.8699 11.2004C21.5599 11.1104 21.2399 11.0404 20.9099 11.0004C19.9499 10.8804 19.0299 10.9504 18.1699 11.2004C18.4599 10.4604 19.1799 9.94043 20.0199 9.94043C20.8599 9.94043 21.5799 10.4604 21.8699 11.2004Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M23.0195 27.0596C23.0195 28.7096 21.6695 30.0596 20.0195 30.0596C19.1995 30.0596 18.4395 29.7196 17.8995 29.1796C17.3595 28.6396 17.0195 27.8796 17.0195 27.0596" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10"/>
</svg>
`,
  walletMoney: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7516 16.8604V18.8904C10.7516 20.6104 9.15158 22.0004 7.18158 22.0004C5.21158 22.0004 3.60156 20.6104 3.60156 18.8904V16.8604C3.60156 18.5804 5.20158 19.8004 7.18158 19.8004C9.15158 19.8004 10.7516 18.5704 10.7516 16.8604Z" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.7498 14.1097C10.7498 14.6097 10.6098 15.0697 10.3698 15.4697C9.77981 16.4397 8.5698 17.0497 7.1698 17.0497C5.7698 17.0497 4.55979 16.4297 3.96979 15.4697C3.72979 15.0697 3.58984 14.6097 3.58984 14.1097C3.58984 13.2497 3.98982 12.4797 4.62982 11.9197C5.27982 11.3497 6.16979 11.0098 7.15979 11.0098C8.14979 11.0098 9.03982 11.3597 9.68982 11.9197C10.3498 12.4697 10.7498 13.2497 10.7498 14.1097Z" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.7516 14.11V16.86C10.7516 18.58 9.15158 19.8 7.18158 19.8C5.21158 19.8 3.60156 18.57 3.60156 16.86V14.11C3.60156 12.39 5.20158 11 7.18158 11C8.17158 11 9.06161 11.35 9.71161 11.91C10.3516 12.47 10.7516 13.25 10.7516 14.11Z" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22.0002 10.9702V13.0302C22.0002 13.5802 21.5602 14.0302 21.0002 14.0502H19.0402C17.9602 14.0502 16.9702 13.2602 16.8802 12.1802C16.8202 11.5502 17.0602 10.9602 17.4802 10.5502C17.8502 10.1702 18.3602 9.9502 18.9202 9.9502H21.0002C21.5602 9.9702 22.0002 10.4202 22.0002 10.9702Z" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2 10.5V8.5C2 5.78 3.64 3.88 6.19 3.56C6.45 3.52 6.72 3.5 7 3.5H16C16.26 3.5 16.51 3.50999 16.75 3.54999C19.33 3.84999 21 5.76 21 8.5V9.95001H18.92C18.36 9.95001 17.85 10.17 17.48 10.55C17.06 10.96 16.82 11.55 16.88 12.18C16.97 13.26 17.96 14.05 19.04 14.05H21V15.5C21 18.5 19 20.5 16 20.5H13.5" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  payment: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22 6V8.42C22 10 21 11 19.42 11H16V4.01C16 2.9 16.91 2 18.02 2C19.11 2.01 20.11 2.45 20.83 3.17C21.55 3.9 22 4.9 22 6Z" stroke="#1E1E5F" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2 7V21C2 21.83 2.93998 22.3 3.59998 21.8L5.31 20.52C5.71 20.22 6.27 20.26 6.63 20.62L8.28998 22.29C8.67998 22.68 9.32002 22.68 9.71002 22.29L11.39 20.61C11.74 20.26 12.3 20.22 12.69 20.52L14.4 21.8C15.06 22.29 16 21.82 16 21V4C16 2.9 16.9 2 18 2H7H6C3 2 2 3.79 2 6V7Z" stroke="#1E1E5F" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  simcard: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.54 6.54L16.47 3.47C15.53 2.53 14.26 2 12.93 2H8C5 2 3 4 3 7V17C3 20 5 22 8 22H16C19 22 21 20 21 17V10.07C21 8.74 20.47 7.47 19.54 6.54Z" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10 18.5H14C15.65 18.5 17 17.15 17 15.5V12.5C17 10.85 15.65 9.5 14 9.5H10C8.35 9.5 7 10.85 7 12.5V15.5C7 17.15 8.35 18.5 10 18.5Z" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 9.5V18.5" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.5 14H16.5" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  bCard: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 8.50488H22" stroke="#1E1E5F" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6 16.5049H8" stroke="#1E1E5F" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.5 16.5049H14.5" stroke="#1E1E5F" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.44 3.50488H17.55C21.11 3.50488 22 4.38488 22 7.89488V16.1049C22 19.6149 21.11 20.4949 17.56 20.4949H6.44C2.89 20.5049 2 19.6249 2 16.1149V7.89488C2 4.38488 2.89 3.50488 6.44 3.50488Z" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  redArrowUp: `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.666 24.667H12.3327" stroke="#DC2626" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22.666 12.333L13.3327 21.6663" stroke="#DC2626" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22.666 19.1797V12.333H15.8193" stroke="#DC2626" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  greenArrowDown: `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.334 21.6663L22.6673 12.333" stroke="#10B981" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M13.334 14.8193V21.666H20.1807" stroke="#10B981" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.334 24.667H23.6673" stroke="#10B981" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  profile: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.6207 13.28C12.5507 13.27 12.4607 13.27 12.3807 13.28C10.6207 13.22 9.2207 11.78 9.2207 10.01C9.2207 8.19998 10.6807 6.72998 12.5007 6.72998C14.3107 6.72998 15.7807 8.19998 15.7807 10.01C15.7707 11.78 14.3807 13.22 12.6207 13.28Z" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M19.2398 19.8801C17.4598 21.5101 15.0998 22.5001 12.4998 22.5001C9.89977 22.5001 7.53977 21.5101 5.75977 19.8801C5.85977 18.9401 6.45977 18.0201 7.52977 17.3001C10.2698 15.4801 14.7498 15.4801 17.4698 17.3001C18.5398 18.0201 19.1398 18.9401 19.2398 19.8801Z" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.5 22.5C18.0228 22.5 22.5 18.0228 22.5 12.5C22.5 6.97715 18.0228 2.5 12.5 2.5C6.97715 2.5 2.5 6.97715 2.5 12.5C2.5 18.0228 6.97715 22.5 12.5 22.5Z" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  profileActive: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22.5 12.5C22.5 6.99 18.01 2.5 12.5 2.5C6.99 2.5 2.5 6.99 2.5 12.5C2.5 15.4 3.75 18.01 5.73 19.84C5.73 19.85 5.73 19.85 5.72 19.86C5.82 19.96 5.94 20.04 6.04 20.13C6.1 20.18 6.15 20.23 6.21 20.27C6.39 20.42 6.59 20.56 6.78 20.7C6.85 20.75 6.91 20.79 6.98 20.84C7.17 20.97 7.37 21.09 7.58 21.2C7.65 21.24 7.73 21.29 7.8 21.33C8 21.44 8.21 21.54 8.43 21.63C8.51 21.67 8.59 21.71 8.67 21.74C8.89 21.83 9.11 21.91 9.33 21.98C9.41 22.01 9.49 22.04 9.57 22.06C9.81 22.13 10.05 22.19 10.29 22.25C10.36 22.27 10.43 22.29 10.51 22.3C10.79 22.36 11.07 22.4 11.36 22.43C11.4 22.43 11.44 22.44 11.48 22.45C11.82 22.48 12.16 22.5 12.5 22.5C12.84 22.5 13.18 22.48 13.51 22.45C13.55 22.45 13.59 22.44 13.63 22.43C13.92 22.4 14.2 22.36 14.48 22.3C14.55 22.29 14.62 22.26 14.7 22.25C14.94 22.19 15.19 22.14 15.42 22.06C15.5 22.03 15.58 22 15.66 21.98C15.88 21.9 16.11 21.83 16.32 21.74C16.4 21.71 16.48 21.67 16.56 21.63C16.77 21.54 16.98 21.44 17.19 21.33C17.27 21.29 17.34 21.24 17.41 21.2C17.61 21.08 17.81 20.97 18.01 20.84C18.08 20.8 18.14 20.75 18.21 20.7C18.41 20.56 18.6 20.42 18.78 20.27C18.84 20.22 18.89 20.17 18.95 20.13C19.06 20.04 19.17 19.95 19.27 19.86C19.27 19.85 19.27 19.85 19.26 19.84C21.25 18.01 22.5 15.4 22.5 12.5ZM17.44 17.47C14.73 15.65 10.29 15.65 7.56 17.47C7.12 17.76 6.76 18.1 6.46 18.47C4.94 16.93 4 14.82 4 12.5C4 7.81 7.81 4 12.5 4C17.19 4 21 7.81 21 12.5C21 14.82 20.06 16.93 18.54 18.47C18.25 18.1 17.88 17.76 17.44 17.47Z" fill="#F7C148"/>
<path d="M12.5 7.43018C10.43 7.43018 8.75 9.11018 8.75 11.1802C8.75 13.2102 10.34 14.8602 12.45 14.9202C12.48 14.9202 12.52 14.9202 12.54 14.9202C12.56 14.9202 12.59 14.9202 12.61 14.9202C12.62 14.9202 12.63 14.9202 12.63 14.9202C14.65 14.8502 16.24 13.2102 16.25 11.1802C16.25 9.11018 14.57 7.43018 12.5 7.43018Z" fill="#F7C148"/>
</svg>
`,
  arrowRight: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.93945 13.2802L10.2861 8.93355C10.7995 8.42021 10.7995 7.58021 10.2861 7.06688L5.93945 2.72021" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  conE: `<svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M48 0C74.5097 0 96 21.4903 96 48C96 74.5097 74.5097 96 48 96C21.4903 96 0 74.5097 0 48C0 21.4903 21.4903 0 48 0Z" fill="#FEF9ED"/>
<path d="M48 0C74.5097 0 96 21.4903 96 48C96 74.5097 74.5097 96 48 96C21.4903 96 0 74.5097 0 48C0 21.4903 21.4903 0 48 0Z" stroke="#E5E7EB"/>
<path d="M63 63H33V33H63V63Z" stroke="#E5E7EB"/>
<g clip-path="url(#clip0_2454_14918)">
<path d="M42.1699 59.2324L41.4316 60.9609C40.3359 60.4043 39.3281 59.7188 38.4141 58.916L39.7441 57.5859C40.4766 58.2246 41.291 58.7812 42.1699 59.2324ZM35.3789 48.9375H33.498C33.5801 50.1797 33.8145 51.3809 34.1836 52.5176L35.9297 51.8203C35.6426 50.9004 35.4492 49.9336 35.3789 48.9375ZM35.3789 47.0625C35.4609 45.9609 35.6836 44.8945 36.0293 43.8926L34.3008 43.1543C33.8613 44.3848 33.5859 45.6973 33.498 47.0625H35.3789ZM36.7676 42.1699C37.2246 41.2969 37.7754 40.4824 38.4141 39.7383L37.084 38.4082C36.2812 39.3223 35.5898 40.3301 35.0391 41.4258L36.7676 42.1699ZM56.2617 57.5859C55.4473 58.2891 54.5391 58.8926 53.5605 59.3672L54.2578 61.1133C55.4707 60.5332 56.5898 59.7891 57.5918 58.9102L56.2617 57.5859ZM39.7383 38.4141C40.5527 37.7109 41.4609 37.1074 42.4395 36.6328L41.7422 34.8867C40.5293 35.4668 39.4102 36.2109 38.4141 37.0898L39.7383 38.4141ZM59.2324 53.8301C58.7754 54.7031 58.2246 55.5176 57.5859 56.2617L58.916 57.5918C59.7188 56.6777 60.4102 55.6641 60.9609 54.5742L59.2324 53.8301ZM60.6211 48.9375C60.5391 50.0391 60.3164 51.1055 59.9707 52.1074L61.6992 52.8457C62.1387 51.6094 62.4141 50.2969 62.4961 48.9316H60.6211V48.9375ZM51.8203 60.0703C50.9004 60.3633 49.9336 60.5508 48.9375 60.6211V62.502C50.1797 62.4199 51.3809 62.1855 52.5176 61.8164L51.8203 60.0703ZM47.0625 60.6211C45.9609 60.5391 44.8945 60.3164 43.8926 59.9707L43.1543 61.6992C44.3906 62.1387 45.7031 62.4141 47.0684 62.4961V60.6211H47.0625ZM60.0703 44.1797C60.3633 45.0996 60.5508 46.0664 60.6211 47.0625H62.502C62.4199 45.8203 62.1855 44.6191 61.8164 43.4824L60.0703 44.1797ZM38.4141 56.2617C37.7109 55.4473 37.1074 54.5391 36.6328 53.5605L34.8867 54.2578C35.4668 55.4707 36.2109 56.5898 37.0898 57.5918L38.4141 56.2617ZM48.9375 35.3789C50.0391 35.4609 51.0996 35.6836 52.1074 36.0293L52.8457 34.3008C51.6152 33.8613 50.3027 33.5859 48.9375 33.498V35.3789ZM44.1797 35.9297C45.0996 35.6367 46.0664 35.4492 47.0625 35.3789V33.498C45.8203 33.5801 44.6191 33.8145 43.4824 34.1836L44.1797 35.9297ZM58.916 38.4082L57.5859 39.7383C58.2891 40.5527 58.8926 41.4609 59.373 42.4395L61.1191 41.7422C60.5391 40.5293 59.7949 39.4102 58.916 38.4082ZM56.2617 38.4141L57.5918 37.084C56.6777 36.2812 55.6699 35.5898 54.5742 35.0391L53.8359 36.7676C54.7031 37.2246 55.5234 37.7754 56.2617 38.4141Z" fill="black"/>
<path d="M48 55.9688C48.9061 55.9688 49.6406 55.2342 49.6406 54.3281C49.6406 53.422 48.9061 52.6875 48 52.6875C47.0939 52.6875 46.3594 53.422 46.3594 54.3281C46.3594 55.2342 47.0939 55.9688 48 55.9688Z" fill="black"/>
<path d="M48.4515 51.2812H47.514C47.1273 51.2812 46.8109 50.9648 46.8109 50.5781C46.8109 46.418 51.346 46.834 51.346 44.2617C51.346 43.0898 50.303 41.9062 47.9827 41.9062C46.2777 41.9062 45.387 42.4687 44.514 43.5879C44.2855 43.8809 43.8636 43.9395 43.5648 43.7285L42.7972 43.1895C42.4691 42.9609 42.3929 42.498 42.6448 42.1816C43.887 40.5879 45.3636 39.5625 47.9886 39.5625C51.053 39.5625 53.6956 41.3086 53.6956 44.2617C53.6956 48.2227 49.1605 47.9824 49.1605 50.5781C49.1546 50.9648 48.8382 51.2812 48.4515 51.2812Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_2454_14918">
<path d="M33 33H63V63H33V33Z" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  arrowRightCircle: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 2.5C6.49 2.5 2 6.99 2 12.5C2 18.01 6.49 22.5 12 22.5C17.51 22.5 22 18.01 22 12.5C22 6.99 17.51 2.5 12 2.5ZM14.79 13.03L11.26 16.56C11.11 16.71 10.92 16.78 10.73 16.78C10.54 16.78 10.35 16.71 10.2 16.56C9.91 16.27 9.91 15.79 10.2 15.5L13.2 12.5L10.2 9.5C9.91 9.21 9.91 8.73 10.2 8.44C10.49 8.15 10.97 8.15 11.26 8.44L14.79 11.97C15.09 12.26 15.09 12.74 14.79 13.03Z" fill="#261B02"/>
</svg>
`,
  infoCircle: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.5 22.5C18 22.5 22.5 18 22.5 12.5C22.5 7 18 2.5 12.5 2.5C7 2.5 2.5 7 2.5 12.5C2.5 18 7 22.5 12.5 22.5Z" stroke="#1E1E1E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.5 8.5V13.5" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.4941 16.5H12.5031" stroke="#292D32" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  circleCheck: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3024_13861)">
<path d="M8.00065 14.6663C11.6827 14.6663 14.6673 11.6817 14.6673 7.99967C14.6673 4.31767 11.6827 1.33301 8.00065 1.33301C4.31865 1.33301 1.33398 4.31767 1.33398 7.99967C1.33398 11.6817 4.31865 14.6663 8.00065 14.6663Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6 8.00033L7.33333 9.33366L10 6.66699" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3024_13861">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  cardAdd: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.5 9H14" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.5 17H8.5" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11 17H15" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22.5 12.53V16.61C22.5 20.12 21.61 21 18.06 21H6.94C3.39 21 2.5 20.12 2.5 16.61V8.39C2.5 4.88 3.39 4 6.94 4H14" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17 6.75H22.5" stroke="#222222" stroke-width="1.5" stroke-linecap="round"/>
<path d="M19.75 9.5V4" stroke="#222222" stroke-width="1.5" stroke-linecap="round"/>
</svg>
`,
  cardSetting: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.5 15.5C14.1569 15.5 15.5 14.1569 15.5 12.5C15.5 10.8431 14.1569 9.5 12.5 9.5C10.8431 9.5 9.5 10.8431 9.5 12.5C9.5 14.1569 10.8431 15.5 12.5 15.5Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2.5 13.3804V11.6204C2.5 10.5804 3.35 9.72043 4.4 9.72043C6.21 9.72043 6.95 8.44042 6.04 6.87042C5.52 5.97042 5.83 4.80042 6.74 4.28042L8.47 3.29042C9.26 2.82042 10.28 3.10042 10.75 3.89042L10.86 4.08042C11.76 5.65042 13.24 5.65042 14.15 4.08042L14.26 3.89042C14.73 3.10042 15.75 2.82042 16.54 3.29042L18.27 4.28042C19.18 4.80042 19.49 5.97042 18.97 6.87042C18.06 8.44042 18.8 9.72043 20.61 9.72043C21.65 9.72043 22.51 10.5704 22.51 11.6204V13.3804C22.51 14.4204 21.66 15.2804 20.61 15.2804C18.8 15.2804 18.06 16.5604 18.97 18.1304C19.49 19.0404 19.18 20.2004 18.27 20.7204L16.54 21.7104C15.75 22.1804 14.73 21.9004 14.26 21.1104L14.15 20.9204C13.25 19.3504 11.77 19.3504 10.86 20.9204L10.75 21.1104C10.28 21.9004 9.26 22.1804 8.47 21.7104L6.74 20.7204C5.83 20.2004 5.52 19.0304 6.04 18.1304C6.95 16.5604 6.21 15.2804 4.4 15.2804C3.35 15.2804 2.5 14.4204 2.5 13.3804Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  cardTransaction: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.23 20.2C8.05 19.32 9.3 19.39 10.02 20.35L11.03 21.7C11.84 22.77 13.15 22.77 13.96 21.7L14.97 20.35C15.69 19.39 16.94 19.32 17.76 20.2C19.54 22.1 20.99 21.47 20.99 18.81V7.54C21 3.51 20.06 2.5 16.28 2.5H8.72C4.94 2.5 4 3.51 4 7.54V18.8C4 21.47 5.46 22.09 7.23 20.2Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.59607 11.5H8.60505" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.3984 11.5H16.8984" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.59607 7.5H8.60505" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.3984 7.5H16.8984" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  chevronRight: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9 18.5L15 12.5L9 6.5" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  checkCircle: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 22.5C17.523 22.5 22 18.023 22 12.5C22 6.977 17.523 2.5 12 2.5C6.477 2.5 2 6.977 2 12.5C2 18.023 6.477 22.5 12 22.5Z" stroke="#059669" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9 12.5L11 14.5L15 10.5" stroke="#059669" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  phone: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17 2.5H7C5.89543 2.5 5 3.39543 5 4.5V20.5C5 21.6046 5.89543 22.5 7 22.5H17C18.1046 22.5 19 21.6046 19 20.5V4.5C19 3.39543 18.1046 2.5 17 2.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 18.5H12.01" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  email: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20 4.5H4C2.89543 4.5 2 5.39543 2 6.5V18.5C2 19.6046 2.89543 20.5 4 20.5H20C21.1046 20.5 22 19.6046 22 18.5V6.5C22 5.39543 21.1046 4.5 20 4.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22 7.5L13.03 13.2C12.7213 13.3934 12.3643 13.496 12 13.496C11.6357 13.496 11.2787 13.3934 10.97 13.2L2 7.5" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  briefcase: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20 7.5H4C2.89543 7.5 2 8.39543 2 9.5V19.5C2 20.6046 2.89543 21.5 4 21.5H20C21.1046 21.5 22 20.6046 22 19.5V9.5C22 8.39543 21.1046 7.5 20 7.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 21.5V5.5C16 4.96957 15.7893 4.46086 15.4142 4.08579C15.0391 3.71071 14.5304 3.5 14 3.5H10C9.46957 3.5 8.96086 3.71071 8.58579 4.08579C8.21071 4.46086 8 4.96957 8 5.5V21.5" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  location: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20 10.5C20 16.5 12 22.5 12 22.5C12 22.5 4 16.5 4 10.5C4 8.37827 4.84285 6.34344 6.34315 4.84315C7.84344 3.34285 9.87827 2.5 12 2.5C14.1217 2.5 16.1566 3.34285 17.6569 4.84315C19.1571 6.34344 20 8.37827 20 10.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 13.5C13.6569 13.5 15 12.1569 15 10.5C15 8.84315 13.6569 7.5 12 7.5C10.3431 7.5 9 8.84315 9 10.5C9 12.1569 10.3431 13.5 12 13.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  passwordCheck: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.34602 13H4.99935C4.58602 13 4.21935 12.9867 3.89268 12.94C2.13935 12.7467 1.66602 11.92 1.66602 9.66667V6.33333C1.66602 4.08 2.13935 3.25333 3.89268 3.06C4.21935 3.01333 4.58602 3 4.99935 3H7.30602" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.0137 3H11.0003C11.4137 3 11.7803 3.01333 12.107 3.06C13.8603 3.25333 14.3337 4.08 14.3337 6.33333V9.66667C14.3337 11.92 13.8603 12.7467 12.107 12.94C11.7803 12.9867 11.4137 13 11.0003 13H10.0137" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10 1.3335V14.6668" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.39608 7.99984H7.40207" stroke="#292D32" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4.73006 7.99984H4.73605" stroke="#292D32" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  key: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3104_32837)">
<path d="M14.0002 1.3335L12.6669 2.66683M12.6669 2.66683L14.6669 4.66683L12.3335 7.00016L10.3335 5.00016M12.6669 2.66683L10.3335 5.00016M7.59352 7.74016C7.93774 8.07981 8.21139 8.48419 8.39869 8.93002C8.58599 9.37586 8.68325 9.85434 8.68487 10.3379C8.68649 10.8215 8.59244 11.3006 8.40813 11.7477C8.22382 12.1948 7.95289 12.601 7.61095 12.9429C7.269 13.2849 6.8628 13.5558 6.41572 13.7401C5.96864 13.9244 5.48952 14.0185 5.00594 14.0168C4.52236 14.0152 4.04388 13.918 3.59804 13.7307C3.15221 13.5434 2.74783 13.2697 2.40818 12.9255C1.74027 12.234 1.37069 11.3077 1.37904 10.3464C1.3874 9.38497 1.77302 8.46532 2.45285 7.78549C3.13268 7.10566 4.05232 6.72005 5.01371 6.71169C5.9751 6.70334 6.90131 7.07292 7.59285 7.74083L7.59352 7.74016ZM7.59352 7.74016L10.3335 5.00016" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3104_32837">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  shield: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.99935 14.6668C7.99935 14.6668 13.3327 12.0002 13.3327 8.00016V3.3335L7.99935 1.3335L2.66602 3.3335V8.00016C2.66602 12.0002 7.99935 14.6668 7.99935 14.6668Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 5.3335V8.00016" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.6665H8.00667" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
copy: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16 12.9V17.1C16 20.6 14.6 22 11.1 22H6.9C3.4 22 2 20.6 2 17.1V12.9C2 9.4 3.4 8 6.9 8H11.1C14.6 8 16 9.4 16 12.9Z" stroke="#F7C148" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22 6.9V11.1C22 14.6 20.6 16 17.1 16H16V12.9C16 9.4 14.6 8 11.1 8H8V6.9C8 3.4 9.4 2 12.9 2H17.1C20.6 2 22 3.4 22 6.9Z" stroke="#F7C148" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
tHUb: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.89969 6.82015L16.3897 3.99015C20.1997 2.72015 22.2697 4.80015 21.0097 8.61015L18.1797 17.1002C16.2797 22.8102 13.1597 22.8102 11.2597 17.1002L10.4197 14.5802L7.89969 13.7402C2.18969 11.8402 2.18969 8.73015 7.89969 6.82015Z" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.6094 14.1496L14.1894 10.5596" stroke="#555059" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
tHubActive: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.6391 3.46004L7.60914 6.46004C1.53914 8.49004 1.53914 11.8 7.60914 13.82L10.2891 14.71L11.1791 17.39C13.1991 23.46 16.5191 23.46 18.5391 17.39L21.5491 8.37004C22.8891 4.32004 20.6891 2.11004 16.6391 3.46004ZM16.9591 8.84004L13.1591 12.66C13.0091 12.81 12.8191 12.88 12.6291 12.88C12.4391 12.88 12.2491 12.81 12.0991 12.66C11.8091 12.37 11.8091 11.89 12.0991 11.6L15.8991 7.78004C16.1891 7.49004 16.6691 7.49004 16.9591 7.78004C17.2491 8.07004 17.2491 8.55004 16.9591 8.84004Z" fill="#F7C148"/>
</svg>
`,
homeTHub: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.39969 6.32015L15.8897 3.49015C19.6997 2.22015 21.7697 4.30015 20.5097 8.11015L17.6797 16.6002C15.7797 22.3102 12.6597 22.3102 10.7597 16.6002L9.91969 14.0802L7.39969 13.2402C1.68969 11.3402 1.68969 8.23015 7.39969 6.32015Z" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.1094 13.6496L13.6894 10.0596" stroke="#1E1E5F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`
,
liveChat: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9 22.5H15C20 22.5 22 20.5 22 15.5V9.5C22 4.5 20 2.5 15 2.5H9C4 2.5 2 4.5 2 9.5V15.5C2 20.5 4 22.5 9 22.5Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.0009 12.3398H14.3209C13.6109 12.3398 13.1309 11.7998 13.1309 11.1498V9.65973C13.1309 9.00973 13.6109 8.46973 14.3209 8.46973H15.8109C16.4609 8.46973 17.0009 9.00973 17.0009 9.65973V12.3398Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.0002 12.3398C17.0002 15.1298 16.4802 15.5998 14.9102 16.5298" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.8602 12.3398H8.18023C7.47023 12.3398 6.99023 11.7998 6.99023 11.1498V9.65973C6.99023 9.00973 7.47023 8.46973 8.18023 8.46973H9.67023C10.3202 8.46973 10.8602 9.00973 10.8602 9.65973V12.3398Z" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.8595 12.3398C10.8595 15.1298 10.3395 15.5998 8.76953 16.5298" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
iG: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17 2.5H7C4.23858 2.5 2 4.73858 2 7.5V17.5C2 20.2614 4.23858 22.5 7 22.5H17C19.7614 22.5 22 20.2614 22 17.5V7.5C22 4.73858 19.7614 2.5 17 2.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M15.9997 11.8698C16.1231 12.7021 15.981 13.552 15.5935 14.2988C15.206 15.0456 14.5929 15.6512 13.8413 16.0295C13.0898 16.4077 12.2382 16.5394 11.4075 16.4057C10.5768 16.2721 9.80947 15.8799 9.21455 15.285C8.61962 14.6901 8.22744 13.9227 8.09377 13.092C7.96011 12.2614 8.09177 11.4097 8.47003 10.6582C8.84829 9.90667 9.45389 9.29355 10.2007 8.90605C10.9475 8.51856 11.7975 8.3764 12.6297 8.49981C13.4786 8.6257 14.2646 9.02128 14.8714 9.62812C15.4782 10.235 15.8738 11.0209 15.9997 11.8698Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.5 7H17.51" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
linkedin: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16 8.5C17.5913 8.5 19.1174 9.13214 20.2426 10.2574C21.3679 11.3826 22 12.9087 22 14.5V21.5H18V14.5C18 13.9696 17.7893 13.4609 17.4142 13.0858C17.0391 12.7107 16.5304 12.5 16 12.5C15.4696 12.5 14.9609 12.7107 14.5858 13.0858C14.2107 13.4609 14 13.9696 14 14.5V21.5H10V14.5C10 12.9087 10.6321 11.3826 11.7574 10.2574C12.8826 9.13214 14.4087 8.5 16 8.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6 9.5H2V21.5H6V9.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4 6.5C5.10457 6.5 6 5.60457 6 4.5C6 3.39543 5.10457 2.5 4 2.5C2.89543 2.5 2 3.39543 2 4.5C2 5.60457 2.89543 6.5 4 6.5Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
twitter: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22 4.50002C22 4.50002 21.3 6.60002 20 7.90002C21.6 17.9 10.6 25.2 2 19.5C4.2 19.6 6.4 18.9 8 17.5C3 16 0.5 10.1 3 5.50002C5.2 8.10002 8.6 9.60002 12 9.50002C11.1 5.30002 16 2.90002 19 5.70002C20.1 5.70002 22 4.50002 22 4.50002Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`, 
document: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21 7.5V17.5C21 20.5 19.5 22.5 16 22.5H8C4.5 22.5 3 20.5 3 17.5V7.5C3 4.5 4.5 2.5 8 2.5H16C19.5 2.5 21 4.5 21 7.5Z" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.5 5V7C14.5 8.1 15.4 9 16.5 9H18.5" stroke="#222222" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 13.5H12" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 17.5H16" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
verified: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.00026 2C7.20026 2 6.40026 2.4 6.00026 3.13333C5.60106 3.03113 5.18228 3.03343 4.78423 3.14002C4.38618 3.2466 4.02227 3.45388 3.72757 3.74189C3.43286 4.02991 3.21727 4.38895 3.10156 4.78444C2.98585 5.17993 2.97393 5.59856 3.06693 6C2.40026 6.4 1.93359 7.2 1.93359 8C1.93359 8.8 2.40026 9.6 3.06693 10C2.86693 10.8 3.06693 11.6667 3.73359 12.2667C4.26693 12.8 5.13359 13.0667 5.93359 12.9333C6.33359 13.6 7.13359 14 7.93359 14C8.73359 14 9.53359 13.6 9.93359 12.8667C10.7336 13.0667 11.6003 12.8667 12.2003 12.2C12.7336 11.6667 13.0003 10.8667 12.8669 10C13.5336 9.6 13.9336 8.8 13.9336 8C13.9336 7.2 13.5336 6.4 12.8003 6C13.0003 5.2 12.8003 4.33333 12.1336 3.73333C11.8486 3.45195 11.4993 3.24421 11.116 3.12805C10.7327 3.0119 10.3269 2.99083 9.93359 3.06667C9.53359 2.4 8.73359 2 7.93359 2H8.00026Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6 7.99984L7.33333 9.33317L10 6.6665" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
profile1: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.10573 7.24683C8.03906 7.24016 7.95906 7.24016 7.88573 7.24683C6.29906 7.1935 5.03906 5.8935 5.03906 4.2935C5.03906 2.66016 6.35906 1.3335 7.99906 1.3335C9.6324 1.3335 10.9591 2.66016 10.9591 4.2935C10.9524 5.8935 9.6924 7.1935 8.10573 7.24683Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4.7725 9.7065C3.15917 10.7865 3.15917 12.5465 4.7725 13.6198C6.60583 14.8465 9.6125 14.8465 11.4458 13.6198C13.0592 12.5398 13.0592 10.7798 11.4458 9.7065C9.61917 8.4865 6.6125 8.4865 4.7725 9.7065Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`
};
