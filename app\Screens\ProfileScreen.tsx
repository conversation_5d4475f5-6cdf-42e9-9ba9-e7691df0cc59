import React, { useCallback, useContext, useEffect, useState, } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity,
  StatusBar,
  ScrollView,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../Context/CredentailsContext";
import { useNavigation } from "@react-navigation/native";
import ProfileMenuItem from "../Components/ProfileMenuItem";
import Div from "../Components/Div";
import P from "../Components/P";
import Header from "../Components/Header";
import { GetUser } from "../RequestHandler.tsx/User";
import { useFocusEffectAPI } from "../hooks/useSafeFocusEffect";

const ProfileScreen = () => {
  const navigation = useNavigation<any>();
  const { setStoredCredentails } = useContext(CredentailsContext);
  const [lastName, setLastName] = useState("...");
  const [firstName, setFirstName] = useState("...")
  const [avatar, setAvatar] = useState(require("../assets/avatarLarge.png"));

  const getUser = async () => {
    try {
      const user = await GetUser();
      if (user.data) {
        setLastName(user?.data?.lastName);
        setFirstName(user?.data?.firstName);
      }
      if (
        user.data.avatar !== "" &&
        user.data.avatar !== null &&
        user.data.avatar !== undefined
      ) {
        setAvatar({ uri: user?.data?.avatar });
      } else {
        setAvatar(require("../assets/avatarLarge.png"));
      }
    } catch (error) {
    }
  };
  const handleLogout = async () => {
    try {
      // Clear credentials from AsyncStorage
      await AsyncStorage.removeItem("cookies");
      // Update context
      setStoredCredentails(null);
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

useEffect(()=>{
getUser()
},[])

  return (
    <Div>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <Header title="Profile" />

        {/* Profile Info */}
        <View style={styles.profileInfo}>
          <View style={styles.avatarContainer}>
            <Image source={avatar} style={styles.avatar} />
          </View>
          <Text style={styles.profileName}>{firstName} {lastName}</Text>
        </View>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          <ProfileMenuItem
            icon={svg.settings}
            label="Settings"
            onPress={() => navigation.navigate("SettingsScreen")}
          />
          <ProfileMenuItem
            icon={svg.privacy}
            label="Privacy & Security"
            onPress={() => navigation.navigate("PrivacySecurityScreen")}
          />
          <ProfileMenuItem
            icon={svg.support}
            label="Support"
            onPress={() => navigation.navigate("SupportScreen")}
          />
          <ProfileMenuItem
            icon={svg.terms}
            label="Terms of Service"
            onPress={() => navigation.navigate("TermsScreen")}
          />
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <SvgXml xml={svg.logout} width={18} height={18} />
          <Text style={styles.logoutText}>Log Out</Text>
        </TouchableOpacity>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
    width: "100%",
  },

  profileInfo: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 40,
  },
  avatarContainer: {
    width: 140,
    height: 140,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  avatar: {
    width: 140,
    height: 140,
    borderRadius: 100,
  },
  profileName: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 16,
    color: colors.textBlack,
  },
  menuContainer: {
    paddingHorizontal: 20,
    marginBottom: 40,
  },

  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
  },

  logoutText: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
    marginLeft: 16,
  },
});

export default ProfileScreen;
