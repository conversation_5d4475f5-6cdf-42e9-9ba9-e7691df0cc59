import React, { useState } from "react";
import { storeUserEmail } from "../../../utils/userEmail";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Keyboard,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";
import Input from "../../../Components/Input";
import * as yup from "yup";
import { Formik } from "formik";
import { CreateUser } from "../../../RequestHandler.tsx/Auth";
import { useToast } from "../../../Context/ToastContext";

const { width, height } = Dimensions.get("window");

export default function SignUpScreen1({ navigation }) {
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();

  // Define validation schema
  const signupSchema = yup.object().shape({
    email: yup
      .string()
      .email("Please enter a valid email address")
      .required("Email is required"),
    password: yup
      .string()
      .required("Password is required")
      .matches(
        /^(?=.*[0-9])(?=.*[*?!#$%&@^()\-_=+\\|[\]{};:/?.>])(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9*?!#$%&@^()\-_=+\\|[\]{};:/?.>]{8,}$/,
        "Password must contain at least 8 characters, including letters, numbers, and special characters, with at least one uppercase letter"
      ),
    confirmPassword: yup
      .string()
      .required("Please confirm your password")
      .oneOf([yup.ref("password")], "Passwords must match"),
  });

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            email: "",
            password: "",
            confirmPassword: "",
          }}
          validationSchema={signupSchema}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            setLoading(true);
            const body = {
              email: values.email,
              password: values.password,
            };
            try {
              // Store the user's email in AsyncStorage
              await storeUserEmail(values.email);
              const res = await CreateUser(body);
              navigation.navigate("VerifyEmailScreen", { email: values.email });
            } catch (error) {
              console.log(error);
              
              handleToast(error.message, "error");
            } finally {
              setLoading(false);
            }
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <PageHeader
                currentPage={0}
                totalPages={6}
                onBack={() => navigation.goBack()}
              />
              <ScrollView
                automaticallyAdjustContentInsets={true}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                  alignItems: "center",
                  minHeight: "90%",
                }}
              >
                <View style={styles.contentContainer}>
                  <H4 style={styles.mainTitle}>
                    Please enter your email and password
                  </H4>
                  <P style={styles.subtitle}>
                    This will be your log in requirement
                  </P>

                  <View style={styles.formContainer}>
                    <View style={styles.inputGroup}>
                      <Input
                        label={"Email address"}
                        placeholder="<EMAIL>"
                        value={formikProps.values.email}
                        onChangeText={formikProps.handleChange("email")}
                        onBlur={formikProps.handleBlur("email")}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        error={
                          formikProps.errors.email && formikProps.touched.email
                        }
                        errorText={formikProps.errors.email}
                      />
                    </View>

                    <View style={styles.inputGroup}>
                      <Input
                        contStyle={{ marginTop: 16 }}
                        label={"Password"}
                        value={formikProps.values.password}
                        onChangeText={formikProps.handleChange("password")}
                        onBlur={formikProps.handleBlur("password")}
                        type="password"
                        autoCapitalize="none"
                        error={
                          formikProps.errors.password &&
                          formikProps.touched.password
                        }
                        errorText={formikProps.errors.password}
                      />
                    </View>

                    <View style={styles.inputGroup}>
                      <Input
                        contStyle={{ marginTop: 16 }}
                        label={"Confirm password"}
                        value={formikProps.values.confirmPassword}
                        onChangeText={formikProps.handleChange(
                          "confirmPassword"
                        )}
                        onBlur={formikProps.handleBlur("confirmPassword")}
                        type="password"
                        autoCapitalize="none"
                        error={
                          formikProps.errors.confirmPassword &&
                          formikProps.touched.confirmPassword
                        }
                        errorText={formikProps.errors.confirmPassword}
                      />
                    </View>
                  </View>
                </View>

                {formikProps.isValid &&
                formikProps.values.email &&
                formikProps.values.password &&
                formikProps.values.confirmPassword ? (
                  <View
                    style={{ width: "90%", marginBottom: (5 * height) / 100 }}
                  >
                    <Button
                      btnText="Submit"
                      onPress={formikProps.handleSubmit}
                      loading={loading}
                    />
                  </View>
                ) : (
                  <View style={styles.footerContainer}>
                    <View style={styles.loginContainer}>
                      <Text style={styles.accountText}>
                        Already have an account?
                      </Text>
                      <TouchableOpacity
                        onPress={() => navigation.navigate("LoginScreen")}
                      >
                        <Text style={styles.loginText}>Log in</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </ScrollView>
            </View>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  formContainer: {
    width: "100%",
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#E5E5E5",
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
  },
  filledInput: {
    borderColor: colors.primary,
    borderWidth: 1,
  },
  footerContainer: {
    width: "100%",
    alignItems: "center",
  },
  loginContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  accountText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: "#666",
  },
  loginText: {
    fontFamily: fonts.plusJBold,
    fontSize: 14,
    color: colors.black,
    marginLeft: 5,
  },
  bottomIndicator: {
    width: 40,
    height: 5,
    backgroundColor: "#222",
    borderRadius: 3,
  },
});
