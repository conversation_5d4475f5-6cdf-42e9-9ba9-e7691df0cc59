import { StyleSheet, Text, View, Dimensions, TextStyle, StyleProp } from "react-native";
import React, { CSSProperties } from "react";
import { fonts } from "../Config/Fonts";
import { colors } from "../Config/colors";
const { width, height } = Dimensions.get("window");

interface PProps {
  children: any;
  style?: StyleProp<TextStyle> | CSSProperties; // Changed from CSSProperties to StyleProp<TextStyle>
  numberOfLines?: number;
  onPress?: any;
}

const baseWidth = 360;
export default function P({ children, style, numberOfLines, onPress }: PProps) {
  return (
    <Text
      onPress={onPress}
      numberOfLines={numberOfLines}
      style={[
        {
          fontFamily: fonts.plusJRegular,
          fontSize: 16,
          color: colors.textBlack,
          letterSpacing: 0,
        },
        // @ts-ignore
        style, // No need for @ts-ignore now
      ]}
    >
      {children}
    </Text>
  );
}

const styles = StyleSheet.create({});