import React, { useState, CSSProperties, useEffect, useContext } from "react";
import { View, TouchableOpacity, StyleSheet, Dimensions } from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";
import P from "./P";
import { colors } from "../Config/colors";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { ValidateGoogleToken } from "../RequestHandler.tsx/Auth";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useToast } from "../Context/ToastContext";
import { CredentailsContext } from "../Context/CredentailsContext";

interface PProps {
  contStyle?: CSSProperties;
  onPress?: any;
  navigation?: any;
}
const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function GoogleButton({
  contStyle,
  onPress,
  navigation,
}: PProps) {
  const { handleToast } = useToast();
  const [activeDot, setActiveDot] = useState(0);
  const [loading, setLoading] = useState(false);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const dotsArray = [1, 2, 3];
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length);
      }, 200);
      return () => clearInterval(interval);
    }
  }, [loading]);
  const validateGoogleToken = async (idToken) => {
    try {
      const validateToken = await ValidateGoogleToken({
        idToken: idToken,
      });
      if (validateToken.token) {
        AsyncStorage.setItem("cookies##$$", JSON.stringify(validateToken))
          .then(() => {
            // @ts-ignore
            setStoredCredentails(validateToken);
          })
          .catch((err) => {});
      } else {
        handleToast(validateToken.message, "error");
        await GoogleSignin.signOut();
      }
    } catch (error) {
      handleToast("Server error", "error");
    } finally {
      setLoading(false);
    }
  };
  const SignIn = async () => {
    const hasPreviousSignIn = GoogleSignin.hasPreviousSignIn();
    if (hasPreviousSignIn) {
      await GoogleSignin.signOut();
    } else {
      let _user = await GoogleSignin.getCurrentUser();
      if (!_user) {
        const user = await GoogleSignin.signIn();
        if (user.data.idToken) {
          setLoading(true);
          validateGoogleToken(user.data.idToken);
        } else {
        }
      }
      if (!_user.idToken) throw new Error("Auth Failed. Contact SFX!");
    }
  };
  return (
    // @ts-ignore
    <View style={[{ alignItems: "center", width: "48%" }, contStyle]}>
      <TouchableOpacity style={styles.socialButton} onPress={SignIn}>
        {loading ? (
          <View style={styles.loaderContainer}>
            {dotsArray.map((dot, index) => (
              <View
                key={dot}
                style={[
                  styles.dot,
                  {
                    backgroundColor:
                      activeDot === index ? colors.black : colors.gray,
                  },
                ]}
              />
            ))}
          </View>
        ) : (
          <>
            <SvgXml xml={svg.googleBtn} style={styles.socialIcon} />
          </>
        )}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: 30,
  },
  socialButton: {
    width: "100%",
    height: 56,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.stroke,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 100,
    backgroundColor: colors.gray,
    marginHorizontal: 2,
  },
  socialIcon: {
    width: 24,
    height: 24,
  },
});
