import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Alert,
  ScrollView,
  ImageBackground,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Button from "../../Components/Button";
import Icon from "react-native-vector-icons/Feather";

const { width, height } = Dimensions.get("window");

interface ChangeCardColorScreenProps {
  navigation: any;
  route?: {
    params?: {
      currentColor?: string;
    };
  };
}

// Available card colors
const cardColors = [
  { id: "gold", color: "#FFCC66", name: "Gold", isDark: false },
  { id: "navy", color: "#14143F", name: "Navy", isDark: true },
];

export default function ChangeCardColorScreen({
  navigation,
  route,
}: ChangeCardColorScreenProps) {
  const currentColor = route?.params?.currentColor || "gold";
  const [selectedColor, setSelectedColor] = useState(currentColor);
  const [loading, setLoading] = useState(false);

  // Handle color selection
  const handleColorSelection = (colorId: string) => {
    setSelectedColor(colorId);
  };

  // Handle continue button press
  const handleContinue = () => {
    if (!selectedColor) {
      Alert.alert("Error", "Please select a card color");
      return;
    }

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      Alert.alert("Success", "Card color updated successfully", [
        {
          text: "OK",
          onPress: () => navigation.goBack(),
        },
      ]);
    }, 1500);
  };

  // Get card background color based on selected color
  const getCardBackgroundColor = () => {
    const selected = cardColors.find((c) => c.id === selectedColor);
    return selected ? selected.color : cardColors[0].color;
  };

  // Check if the selected color is dark (currently only navy is dark)
  const isDarkColor = selectedColor === "navy";

  return (
    <View style={styles.container}>
      <Div>
        <Header title="Change Card Colour" showNotification={true} />
        <ScrollView style={{ width: "95%" }}>
          <View style={styles.content}>
            {/* Card Preview */}
            <View style={styles.cardContainer}>
              <View
                style={[
                  styles.card,
                  { backgroundColor: getCardBackgroundColor() },
                ]}
              >
                <ImageBackground
                  source={
                    isDarkColor
                      ? require("../../assets/BlueLogoMark.png")
                      : require("../../assets/bg-background.png")
                  }
                  style={{
                    width: "60%",
                    height: "110%",
                    position: "absolute",
                    right: -90,
                    opacity: isDarkColor ? 0.5 : 1, // Adjust opacity for better visibility
                  }}
                  resizeMode="cover"
                />
                <View style={styles.cardHeader}>
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      gap: 16,
                    }}
                  >
                    <Text
                      style={[
                        styles.cardHolderName,
                        selectedColor === "navy" && styles.whiteText,
                      ]}
                    >
                      Anita Amadi
                    </Text>
                    <Text
                      style={[
                        styles.cardExpiry,
                        selectedColor === "navy" && styles.whiteText,
                      ]}
                    >
                      07/25
                    </Text>
                  </View>
                  <TouchableOpacity style={styles.eyeIcon}>
                    <Icon name="eye" size={20} color={"#DF7400"} />
                  </TouchableOpacity>
                </View>
                <Text
                  style={[
                    styles.cardCvv,
                    selectedColor === "navy" && styles.whiteText,
                  ]}
                >
                  CVV: 654
                </Text>
                <Text
                  style={[
                    styles.cardNumber,
                    selectedColor === "navy" && styles.whiteText,
                  ]}
                >
                  4567 6574 2533 9018
                </Text>
                <View style={styles.cardBrand}>
                  <Text
                    style={[
                      styles.cardBalance,
                      selectedColor === "navy" && styles.whiteText,
                    ]}
                  >
                    NGN 8590.02
                  </Text>
                  <View style={styles.masterCardIcon}>
                    <View style={[styles.circle, styles.redCircle]} />
                    <View style={[styles.circle, styles.yellowCircle]} />
                  </View>
                </View>
              </View>
            </View>
            {/* Color Selection */}
            <View style={styles.colorSelectionContainer}>
              <Text style={styles.colorSelectionTitle}>Choose card colour</Text>
              <View style={styles.colorOptions}>
                {cardColors.map((color) => (
                  <TouchableOpacity
                    key={color.id}
                    style={[
                      styles.colorOption,
                      { backgroundColor: color.color },
                      selectedColor === color.id && styles.selectedColorOption,
                    ]}
                    onPress={() => handleColorSelection(color.id)}
                  />
                ))}
              </View>
            </View>

            {/* Continue Button */}
            <View style={styles.buttonContainer}>
              <Button
                btnText="Continue"
                onPress={handleContinue}
                loading={loading}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  cardContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 20,
  },
  card: {
    width: "100%",
    minHeight: 215,
    backgroundColor: colors.cardColor,
    borderRadius: 12,
    padding: 16,
    position: "relative",
    overflow: "hidden",
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  cardHolderName: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardExpiry: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  eyeIcon: {
    padding: 4,
  },
  cardCvv: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginBottom: 8,
    marginTop: 10,
  },
  cardNumber: {
    fontSize: 18,
    fontFamily: fonts.plusJBold,
    color: colors.textBlack,
    marginTop: 8,
    letterSpacing: 1,
  },
  cardBalance: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardBrand: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    position: "absolute",
    bottom: 16,
    right: 16,
  },
  masterCardIcon: {
    flexDirection: "row",
    alignItems: "center",
  },
  circle: {
    width: 31,
    height: 31,
    borderRadius: 100,
  },
  redCircle: {
    backgroundColor: "#EB001B",
    marginRight: -8,
  },
  yellowCircle: {
    backgroundColor: "#F79E1B",
  },
  whiteText: {
    color: colors.white,
  },
  colorSelectionContainer: {
    marginBottom: 30,
  },
  colorSelectionTitle: {
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
    marginBottom: 16,
  },
  colorOptions: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 12,
    marginRight: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "transparent",
  },
  selectedColorOption: {
    borderColor: colors.primary,
    borderWidth: 2,
  },
  buttonContainer: {
    marginTop: 20,
  },
});
