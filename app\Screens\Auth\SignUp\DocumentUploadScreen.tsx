import React, { useContext, useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  Alert,
} from "react-native";
import * as DocumentPicker from "expo-document-picker";
import * as FileSystem from "expo-file-system";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";
import Icon from "react-native-vector-icons/Feather";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import Link from "../../../Components/Link";
import { NGDocumentUpload } from "../../../RequestHandler.tsx/Auth";
import { useToast } from "../../../Context/ToastContext";
import { useUpdateNextStep } from "../../../utils/updateNextStep";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../../../Context/CredentailsContext";

const { height } = Dimensions.get("window");

export default function DocumentUploadScreen({ navigation }) {
  const [selectedDocType, setSelectedDocType] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const { updateUserNextStep } = useUpdateNextStep();
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);

  const documentTypes = [
    { label: "International passport", value: "international_passport" },
    { label: "NIN slip", value: "nin_slip" },
    { label: "Voter's card", value: "voters_card" },
    { label: "Driver's license", value: "drivers_license" },
  ];

  const pickDocument = async () => {
    try {
      setIsUploading(true);
      const result = await DocumentPicker.getDocumentAsync({
        type: ["image/*", "application/pdf"],
        copyToCacheDirectory: true,
      });

      if (
        result.canceled === false &&
        result.assets &&
        result.assets.length > 0
      ) {
        const file = result.assets[0];

        // Check file size (max 5MB)
        const fileInfo = await FileSystem.getInfoAsync(file.uri);
        // Check if file exists and has size property
        if (fileInfo.exists && "size" in fileInfo) {
          if (fileInfo.size > 5 * 1024 * 1024) {
            Alert.alert(
              "Error",
              "File size exceeds 5MB limit. Please choose a smaller file."
            );
            setIsUploading(false);
            return;
          }
          // Convert file to base64
          const base64 = await FileSystem.readAsStringAsync(file.uri, {
            encoding: FileSystem.EncodingType.Base64,
          });
          setUploadedFile({
            name: file.name,
            uri: file.uri,
            type: file.mimeType,
            size: fileInfo.size,
            base64: base64,
          });
        } else {
          // If we can't get the file size, just proceed with the file
          const base64 = await FileSystem.readAsStringAsync(file.uri, {
            encoding: FileSystem.EncodingType.Base64,
          });

          setUploadedFile({
            name: file.name,
            uri: file.uri,
            type: file.mimeType,
            size: 0, // Default size if unknown
            base64: base64,
          });
        }
      }
    } catch (error) {
      console.log("Error picking document:", error);
      Alert.alert("Error", "Failed to pick document. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };
  const handleSelectDocType = (type: string) => {
    setSelectedDocType(type);
    setIsDropdownOpen(false);
  };
  const clearLogin = async () => {
    try {
      await AsyncStorage.removeItem("cookies");
      setStoredCredentails(null);
    } catch (error) {
      console.error("Error clearing login:", error);
    }
  };

  const uploadDocument = async () => {
    if (!uploadedFile || !selectedDocType) {
      handleToast("Please select a document type and upload a file", "error");
      return;
    }
    setLoading(true);
    try {
      const body = {
        title: selectedDocType,
        document: `data:${uploadedFile.type};base64,${uploadedFile.base64}`,
      };
      const res = await NGDocumentUpload(body);
      console.log(res);
      handleToast("Document uploaded successfully", "success");
      navigation.navigate("TransactionPinScreen1");
      if (res.nextStep) {
        updateUserNextStep(res.nextStep);
      }
    } catch (error) {
      if (error.message === "Token expired") {
        clearLogin();
      }
      console.log("Error uploading document:", error);
      const errorMessage =
        error && error.message ? error.message : "Failed to upload document";
      handleToast(errorMessage, "error");
    } finally {
      setLoading(false);
    }
  };
  const handleContinue = () => {
    uploadDocument();
  };
  const getSelectedDocLabel = () => {
    const selected = documentTypes.find((doc) => doc.value === selectedDocType);
    return selected ? selected.label : "Select the KYC document to upload";
  };
  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={4}
            totalPages={6}
            onBack={() => navigation.pop()}
          />
          <ScrollView
            automaticallyAdjustContentInsets={true}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              alignItems: "center",
              minHeight: "90%",
            }}
          >
            <View style={styles.contentContainer}>
              <H4 style={styles.mainTitle}>Let's know you better</H4>
              <P style={styles.subtitle}>
                To help prevent fraud and identity theft, we'll need you to
                upload any of the document below
              </P>

              {/* Dropdown selector */}
              <View style={styles.dropdownContainer}>
                <TouchableOpacity
                  style={styles.dropdownSelector}
                  onPress={() => setIsDropdownOpen(!isDropdownOpen)}
                >
                  <P style={styles.dropdownText}>{getSelectedDocLabel()}</P>
                  <Icon
                    name={isDropdownOpen ? "chevron-up" : "chevron-down"}
                    size={20}
                    color={colors.black}
                  />
                </TouchableOpacity>

                {/* Dropdown options */}
                {isDropdownOpen && (
                  <View style={styles.dropdownOptions}>
                    {documentTypes.map((item, index) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.dropdownItem}
                        onPress={() => handleSelectDocType(item.value)}
                      >
                        <P style={styles.dropdownItemText}>{item.label}</P>
                        <Icon
                          name="chevron-right"
                          size={20}
                          color={colors.black}
                        />
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>

              {/* Upload area */}
              <View style={styles.uploadContainer}>
                {isUploading ? (
                  <View style={styles.uploadingContainer}>
                    <P style={styles.uploadingText}>Uploading...</P>
                  </View>
                ) : uploadedFile ? (
                  <View style={styles.fileContainer}>
                    <Icon name="file" size={24} color={colors.primary} />
                    <P style={styles.fileName} numberOfLines={1}>
                      {uploadedFile.name}
                    </P>
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => setUploadedFile(null)}
                    >
                      <Icon name="x" size={20} color="#888" />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.uploadPlaceholder}>
                    <SvgXml xml={svg.cloud} />
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 4,
                        marginTop: 16,
                      }}
                    >
                      <Link onPress={pickDocument}>Click to upload</Link>
                      <P>or drag and drop</P>
                    </View>
                    <P style={styles.uploadText}>
                      SVG, PNG, JPG or GIF (max. 800×400px)
                    </P>
                    <View style={styles.orCont}>
                      <View style={styles.dash} />
                      <P
                        style={{
                          fontSize: 12,
                          fontWeight: fonts.plusJSemibold,
                          color: colors.textSubtle,
                        }}
                      >
                        OR
                      </P>
                      <View style={styles.dash} />
                    </View>
                    <TouchableOpacity
                      style={styles.uploadButton}
                      onPress={pickDocument}
                    >
                      <P style={styles.uploadButtonText}>Browse Files</P>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </View>

            {uploadedFile && selectedDocType && (
              <View style={{ width: "90%", marginBottom: (5 * height) / 100 }}>
                <Button
                  btnText="Continue"
                  onPress={handleContinue}
                  loading={loading}
                />
              </View>
            )}
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
    width: "100%",
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  dropdownContainer: {
    marginBottom: 20,
    position: "relative",
    zIndex: 10,
  },
  dropdownSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    padding: 16,
  },
  dropdownText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textAsh,
  },
  dropdownOptions: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    marginTop: 4,
    zIndex: 20,
  },
  dropdownItem: {
    padding: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  dropdownItemText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textAsh,
  },
  uploadContainer: {
    width: "100%",
    minHeight: 264,
    borderWidth: 1,
    borderStyle: "dashed",
    borderColor: "#CCCCCC",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
    padding: 16,
  },
  uploadPlaceholder: {
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  uploadText: {
    fontSize: 12,
    color: colors.textAsh,
    marginTop: 2,
    fontFamily: fonts.plusJRegular,
    marginBottom: 16,
  },
  uploadButton: {
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  uploadButtonText: {
    color: colors.black,
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
  },
  uploadingContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  uploadingText: {
    fontSize: 16,
    color: colors.textAsh,
  },
  fileContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 12,
    width: "90%",
  },
  fileName: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    color: colors.textBlack,
  },
  removeButton: {
    padding: 4,
  },
  dash: {
    width: "40%",
    height: 1,
    backgroundColor: "#D7D5D9",
  },
  orCont: {
    width: "100%",
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
    marginBottom: 16,
  },
});
