import React from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import P from "../../Components/P";
import H4 from "../../Components/H4";

const { width, height } = Dimensions.get("window");

export type PaymentStatus = "processing" | "success" | "failed";

interface PaymentStatusScreenProps {
  navigation: any;
  route: {
    params: {
      status: PaymentStatus;
      errorMessage?: string;
    };
  };
}

export default function PaymentStatusScreen({
  navigation,
  route,
}: PaymentStatusScreenProps) {
  const { status, errorMessage = "Insufficient Wallet Balance" } = route.params;

  const handleButtonPress = () => {
    if (status === "success") {
      // Navigate to location selection screen (placeholder for now)
      navigation.navigate("CardScreen");
    } else if (status === "failed") {
      // Navigate back to order card screen
      navigation.navigate("OrderPhysicalCardScreen");
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <View style={styles.contentContainer}>
          {status === "processing" && (
            <>
              <View style={styles.iconContainer}>
                <Image
                  source={require("../../assets/payment-processing.png")}
                  style={styles.statusIcon}
                />
              </View>
              <H4 style={styles.statusTitle}>Payment Processing</H4>
            </>
          )}

          {status === "success" && (
            <>
              <View style={styles.iconContainer}>
                <Image
                  source={require("../../assets/payment-success.png")}
                  style={styles.statusIcon}
                />
              </View>
              <H4 style={styles.statusTitle}>Payment Successful</H4>
              <P style={styles.statusMessage}>
                Proceed to to select your pick-up location
              </P>
              <View style={styles.buttonContainer}>
                <Button
                  btnText="Select Pick-up Location →"
                  onPress={handleButtonPress}
                />
              </View>
            </>
          )}

          {status === "failed" && (
            <>
              <View style={styles.iconContainer}>
                <Image
                  source={require("../../assets/payment-failed.png")}
                  style={styles.statusIcon}
                />
              </View>
              <H4 style={styles.statusTitle}>Payment Failed</H4>
              <P style={styles.statusMessage}>{errorMessage}</P>
              <View style={styles.buttonContainer}>
                <Button
                  btnText="Fund your NGN wallet →"
                  onPress={handleButtonPress}
                />
              </View>
            </>
          )}
        </View>
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  iconContainer: {
    marginBottom: 24,
  },
  statusIcon: {
    width: 100,
    height: 100,
    resizeMode: "contain",
  },
  statusTitle: {
    fontSize: 24,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    marginBottom: 12,
    textAlign: "center",
  },
  statusMessage: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    textAlign: "center",
    marginBottom: 32,
  },
  buttonContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
});
