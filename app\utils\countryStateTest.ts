import { countries } from '../data/countries';
import { getStatesByCountry } from '../data/states';

// Test function to see which countries have states
export const testCountriesWithStates = () => {
  console.log('=== Countries with States/Provinces ===');
  
  const countriesWithStates = countries
    .map(country => ({
      ...country,
      stateCount: getStatesByCountry(country.isoCode).length
    }))
    .filter(country => country.stateCount > 0)
    .sort((a, b) => b.stateCount - a.stateCount);

  console.log(`Found ${countriesWithStates.length} countries with states/provinces:`);
  
  countriesWithStates.forEach(country => {
    console.log(`${country.label} (${country.isoCode}): ${country.stateCount} states`);
  });

  return countriesWithStates;
};

// Test function to get states for specific countries
export const testSpecificCountries = () => {
  const testCountries = ['US', 'CA', 'NG', 'IN', 'AU', 'BR', 'DE', 'FR', 'GB'];
  
  console.log('\n=== Testing Specific Countries ===');
  
  testCountries.forEach(countryCode => {
    const states = getStatesByCountry(countryCode);
    const country = countries.find(c => c.isoCode === countryCode);
    console.log(`\n${country?.label || countryCode} (${countryCode}):`);
    
    if (states.length > 0) {
      console.log(`  ${states.length} states found:`);
      states.slice(0, 5).forEach(state => {
        console.log(`    - ${state.label}`);
      });
      if (states.length > 5) {
        console.log(`    ... and ${states.length - 5} more`);
      }
    } else {
      console.log('  No states found');
    }
  });
};

// Run tests (you can call this in your app to see the results)
export const runCountryStateTests = () => {
  testCountriesWithStates();
  testSpecificCountries();
};
