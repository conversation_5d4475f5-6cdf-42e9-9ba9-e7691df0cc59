import { useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';

/**
 * A safe wrapper around useFocusEffect that prevents memory leaks and 
 * "Cannot read property 'current' of undefined" errors.
 * 
 * This hook ensures that:
 * 1. State updates only happen when the component is still mounted
 * 2. Async operations are properly cancelled when the screen loses focus
 * 3. Cleanup is handled automatically
 * 
 * @param effect - The effect function to run when the screen comes into focus
 * @param deps - Dependencies array (similar to useEffect)
 */
export const useSafeFocusEffect = (
  effect: (isMounted: () => boolean) => void | (() => void),
  deps: React.DependencyList = []
) => {
  useFocusEffect(
    useCallback(() => {
      let isMounted = true;
      
      // Create a function to check if component is still mounted
      const checkIsMounted = () => isMounted;
      
      // Call the effect with the isMounted checker
      const cleanup = effect(checkIsMounted);
      
      // Return cleanup function
      return () => {
        isMounted = false;
        if (cleanup && typeof cleanup === 'function') {
          cleanup();
        }
      };
    }, deps)
  );
};

/**
 * A specialized hook for API calls on screen focus.
 * Handles loading states and prevents state updates after unmount.
 * 
 * @param apiCall - The async function to call
 * @param setLoading - Function to set loading state
 * @param setError - Function to set error state (optional)
 * @param deps - Dependencies array
 */
export const useFocusEffectAPI = (
  apiCall: () => Promise<any>,
  setLoading: (loading: boolean) => void,
  setError?: (error: any) => void,
  deps: React.DependencyList = []
) => {
  useSafeFocusEffect((isMounted) => {
    const loadData = async () => {
      if (!isMounted()) return;
      
      try {
        if (isMounted()) setLoading(true);
        if (setError && isMounted()) setError(null);
        
        await apiCall();
      } catch (error) {
        console.error('API Error:', error);
        if (setError && isMounted()) {
          setError(error);
        }
      } finally {
        if (isMounted()) setLoading(false);
      }
    };
    
    loadData();
  }, deps);
};
