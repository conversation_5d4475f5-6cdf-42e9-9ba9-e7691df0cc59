import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Switch,
  ScrollView,
} from "react-native";
import { colors } from "../../../../Config/colors";
import Div from "../../../../Components/Div";
import H4 from "../../../../Components/H4";
import P from "../../../../Components/P";
import { fonts } from "../../../../Config/Fonts";
import Button from "../../../../Components/Button";
import PageHeader from "../../../../Components/PageHeader";
import Icon from "react-native-vector-icons/Feather";
import CustomSwitch from "../../../../Components/CustomSwitch";

const { width, height } = Dimensions.get("window");

export default function PrivacyConsentScreen({ navigation }) {
  const [consents, setConsents] = useState({
    dataProcessing: false,
    biometricData: false,
    thirdParty: false,
  });

  const [allConsentsGiven, setAllConsentsGiven] = useState(false);

  useEffect(() => {
    const { dataProcessing, biometricData, thirdParty } = consents;
    setAllConsentsGiven(dataProcessing && biometricData && thirdParty);
  }, [consents]);

  const toggleConsent = (key) => {
    setConsents((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleContinue = () => {
    if (allConsentsGiven) {
      navigation.navigate("ForeignersKYCScreen1");
    }
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={2}
            totalPages={6}
            type="bars"
            onBack={() => navigation.pop()}
          />
          <ScrollView>
            <View style={styles.contentContainer}>
              <H4 style={styles.mainTitle}>Privacy Consent</H4>
              <P style={styles.subtitle}>
                Please review and accept the following consents to proceed with
                identity verification.
              </P>

              <View style={styles.consentContainer}>
                <View style={styles.consentHeader}>
                  <P style={styles.consentTitle}>Data Processing Consent</P>
                  <CustomSwitch
                    isOn={consents.dataProcessing}
                    onToggle={() => {
                      toggleConsent("dataProcessing");
                    }}
                  />
                </View>
                <P style={styles.consentDescription}>
                  I consent to the processing of my personal data for identity
                  verification purposes.
                </P>
              </View>

              <View style={styles.consentContainer}>
                <View style={styles.consentHeader}>
                  <P style={styles.consentTitle}>Biometric Data Usage</P>
                  <CustomSwitch
                    isOn={consents.biometricData}
                    onToggle={() => {
                      toggleConsent("biometricData");
                    }}
                  />
                </View>
                <P style={styles.consentDescription}>
                  I agree to the collection and processing of my biometric data.
                </P>
              </View>

              <View style={styles.consentContainer}>
                <View style={styles.consentHeader}>
                  <P style={styles.consentTitle}>Third-Party Verification</P>
                  <CustomSwitch
                    isOn={consents.thirdParty}
                    onToggle={() => {
                      toggleConsent("thirdParty");
                    }}
                  />
                </View>
                <P style={styles.consentDescription}>
                  I authorize verification of my documents through third-party
                  services.
                </P>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Button
                btnText="Continue"
                onPress={handleContinue}
                disabled={!allConsentsGiven}
                btnTextStyle={{ fontFamily: fonts.plusJRegular }}
              />
              <P style={styles.securityNote}>
                Your data is protected by industry-standard encryption
              </P>
            </View>
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 24,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.bGray_100,
    marginBottom: (3 * height) / 100,
  },
  consentContainer: {
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  consentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  consentTitle: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: "#111827",
  },
  consentDescription: {
    fontSize: 14,
    color: colors.textAsh,
  },
  buttonContainer: {
    width: "90%",
    marginTop: (5 * height) / 100,
    alignSelf: "center",
  },
  securityNote: {
    fontSize: 12,
    color: colors.textAsh,
    textAlign: "center",
    marginTop: 12,
  },
});
