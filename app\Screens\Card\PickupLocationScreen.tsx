import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
  SafeAreaView,
  Dimensions,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  ImageBackground,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import Header from "../../Components/Header";
import P from "../../Components/P";
import H4 from "../../Components/H4";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import Icon from "react-native-vector-icons/Feather";
import { GetPickUpLocations, SavePickupLocation } from "../../RequestHandler.tsx/Card";
import Loader from "../../Components/Loader";
import NetworkError from "../../Components/NetworkError";


const { width, height } = Dimensions.get("window");

interface PickupLocationScreenProps {
  navigation: any;
  route?: {
    params?: {
      cardType?: string;
      orderId?: string;
    };
  };
}

interface PickupLocation {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
}

// Location selection bottom sheet component
const LocationSelectionBottomSheet = ({
  isVisible,
  onClose,
  locations,
  selectedLocation,
  onSelectLocation,
}: {
  isVisible: boolean;
  onClose: () => void;
  locations: PickupLocation[];
  selectedLocation: string | null;
  onSelectLocation: (locationId: string) => void;
}) => {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <H4 style={styles.modalTitle}>Select Pick-up Location</H4>
              </View>

              <ScrollView style={styles.locationsList}>
                {locations.map((location, index) => (
                  <TouchableOpacity
                    key={location.id}
                    style={[
                      styles.locationItem,
                      selectedLocation === location.id &&
                        styles.selectedLocationItem,
                    ]}
                    onPress={() => {
                      onSelectLocation(location.id);
                      onClose();
                    }}
                  >
                    <View style={styles.locationDot} />
                    <P style={styles.locationText}>{location.name}</P>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default function PickupLocationScreen({
  navigation,
  route,
}: PickupLocationScreenProps) {
  const cardType = route?.params?.cardType || "ngn";
  const orderId = route?.params?.orderId;
  const [showLocationSelection, setShowLocationSelection] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [isLoadingLocations, setIsLoadingLocations] = useState(true);
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [locations, setLocations] = useState<PickupLocation[]>([]);

  const getPickupLocations = async () => {
    setIsLoadingLocations(true);
    setIsNetworkError(false);
    try {
      const res = await GetPickUpLocations();
      console.log(res);
      if (res?.data && Array.isArray(res.data)) {
        setLocations(res.data);
      }
    } catch (error) {
      console.log("Error fetching pickup locations:", error);
      if (error.isNetworkError) {
        setIsNetworkError(true);
      }
    } finally {
      setIsLoadingLocations(false);
    }
  };

  useEffect(() => {
    getPickupLocations();
  }, []);

  // Get selected location name
  const getSelectedLocationName = () => {
    if (!selectedLocation) return "Select a Pick-up Location";
    const location = locations.find((loc) => loc.id === selectedLocation);
    return location ? location.name : "Select a Pick-up Location";
  };

  // Handle save location
 // Handle save location
const handleSaveLocation = async () => {
  if (!selectedLocation || !orderId) return;

  setLoading(true);
  try {
    const body = {
      locationId: selectedLocation
    };
    
    const response = await SavePickupLocation(orderId, body);
    console.log("Pickup location saved:", response);
    
    // Get the selected location name
    const locationName = getSelectedLocationName();
    
    // Navigate to card activation screen
    navigation.navigate("CardActivationScreen", {
      cardType: cardType,
      location: locationName,
      orderId: orderId,
    });
  } catch (error) {
    console.log("Error saving pickup location:", error);
    // Handle error - you might want to show an error message to the user
  } finally {
    setLoading(false);
  }
};

  // Show network error if there's a network issue
  if (isNetworkError) {
    return (
      <SafeAreaView style={styles.container}>
        <Div>
          <Header
            title="Cards"
            contStyle={{ justifyContent: "flex-start", gap: 8 }}
            showNotification={false}
          />
          <NetworkError
            type="fullscreen"
            errorType="network"
            onRetry={getPickupLocations}
          />
        </Div>
      </SafeAreaView>
    );
  }

  // Show loading state while fetching locations
  if (isLoadingLocations) {
    return (
      <SafeAreaView style={styles.container}>
        <Div>
          <Header
            title="Cards"
            contStyle={{ justifyContent: "flex-start", gap: 8 }}
            showNotification={false}
          />
          <Loader type="overlay" size="large" />
        </Div>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header
          title="Cards"
          contStyle={{ justifyContent: "flex-start", gap: 8 }}
          showNotification={false}
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          style={{ width: "95%" }}
        >
          {/* Card Preview */}
          <View style={styles.cardPreviewContainer}>
            <View style={styles.cardPreview}>
              <ImageBackground
                source={require("../../assets/bg-background.png")}
                style={{
                  width: "60%",
                  height: "110%",
                  position: "absolute",
                  right: -90,  
                }}
                resizeMode="cover"
              ></ImageBackground>
              <View style={styles.cardHeader}>
                <View style={styles.flagContainer}>
                  <Image
                    source={{
                      uri: `https://flagcdn.com/w2560/ng.png`,
                    }}
                    style={styles.flagIcon}
                  />
                </View>
                <Text style={styles.cardBalance}>NGN 0.00</Text>
              </View>

              <Text style={styles.cardNumber}>1234 **** **** 1234</Text>
              <Text style={styles.cardExpiry}>VALID TILL 08/28</Text>
              <Text style={styles.cardHolder}>Anita Amadi</Text>

              <View style={styles.cardBrand}>
                <Image
                  source={require("../../assets/verve.png")}
                  style={{ width: 58, height: 35 }}
                />
              </View>
            </View>
          </View>

          {/* Pickup Instructions */}
          <View style={styles.pickupInstructions}>
            <P style={styles.pickupText}>
              Pick up and activate your card at any of our locations in Nigeria
            </P>
          </View>

          {/* Location Selection */}
          <TouchableOpacity
            style={styles.locationSelector}
            onPress={() => setShowLocationSelection(true)}
          >
            <P style={styles.locationText}>{getSelectedLocationName()}</P>
            <Icon name="chevron-down" size={20} color="#000" />
          </TouchableOpacity>

          {/* Save Button */}
          <View style={styles.saveButtonContainer}>
            <Button
              btnText="Save your card Pick-up Location"
              onPress={handleSaveLocation}
              disabled={!selectedLocation || !orderId}
              loading={loading}
            />
          </View>
        </ScrollView>

        {/* Location Selection Bottom Sheet */}
        <LocationSelectionBottomSheet
          isVisible={showLocationSelection}
          onClose={() => setShowLocationSelection(false)}
          locations={locations}
          selectedLocation={selectedLocation}
          onSelectLocation={setSelectedLocation}
        />
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  cardPreviewContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 30,
  },
  cardPreview: {
    width: "100%",
    height: 215,
    backgroundColor: "#DFDFF5",
    borderRadius: 12,
    padding: 16,
    position: "relative",
    overflow: "hidden"
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  flagContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    overflow: "hidden",
  },
  flagIcon: {
    width: 30,
    height: 30,
    objectFit: "fill",
  },
  cardBalance: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardNumber: {
    fontSize: 24,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
    marginBottom: 8,
  },
  cardExpiry: {
    fontSize: 12,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    marginBottom: 8,
  },
  cardHolder: {
    fontSize: 14,
    fontFamily: fonts.plusJBold,
    color: colors.textBlack,
  },
  cardBrand: {
    position: "absolute",
    bottom: 16,
    right: 16,
  },
  cardBrandText: {
    color: colors.white,
    fontSize: 12,
    fontFamily: fonts.plusJMedium,
  },
  pickupInstructions: {
    marginBottom: 20,
  },
  pickupText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    textAlign: "center",
    lineHeight: 24,
    paddingLeft: 20,
    paddingRight: 20,
  },
  locationSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#F2F2F2",
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  locationText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  saveButtonContainer: {
    marginTop: 20,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: "60%",
  },
  modalHeader: {
    marginBottom: 20,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  locationsList: {
    marginBottom: 20,
  },
  locationItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F2F2F2",
  },
  selectedLocationItem: {
    backgroundColor: "#F9F9F9",
  },
  locationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
    marginRight: 12,
  },
});
