import {
  Dimensions,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
} from "react-native";
import React, { CSSProperties } from "react";
import { Feather } from "@expo/vector-icons";
import P from "./P";
import { fonts } from "../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";

const { width, height } = Dimensions.get("window");

interface PProps {
  navigation?: any;
  text?: string;
  iconComp?: any;
  contStyle?: CSSProperties;
  navStyle?: CSSProperties;
  showBorder?: boolean;
  showBackArrow?: boolean;
  goHome?: boolean;
  cancel?: boolean;
  modalClose?: () => void; // Updated type for modalClose
  disabled?: boolean;
  goToScreen?: string;
}

export default function AppHeader({
  navigation,
  text,
  iconComp,
  contStyle,
  navStyle,
  showBorder,
  showBackArrow = true,
  goHome = false,
  cancel,
  disabled,
  modalClose,
  goToScreen,
}: PProps) {
  const handlePress = () => {
    if (cancel && modalClose) {
      modalClose(); // Call the modalClose function
    } else if (goHome) {
      navigation.reset({
        index: 0,
        routes: [{ name: "BottomTabNavigator" }],
      });
    } else if (goToScreen) {
      navigation.navigate(goToScreen);
    } else {
      navigation.pop();
    }
  };

  return (
    // @ts-ignore
    <View style={[styles.navCont, contStyle, showBorder && styles.navBorder]}>
      {/* @ts-ignore */}
      <View style={[styles.nav, navStyle]}>
        <TouchableOpacity
          onPress={handlePress}
          style={{ flexDirection: "row", alignItems: "center" }}
          disabled={disabled}
        >
          {showBackArrow && (
            <SvgXml xml={svg.goBack} style={{ marginRight: 12 }} />
          )}
          <P style={styles.navText}>{text}</P>
        </TouchableOpacity>
        <P style={styles.navText}> </P>
        {iconComp}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  navCont: {
    width,
    height: 24,
    alignItems: "center",
    marginTop: 24,
    marginBottom: 16,
  },
  nav: {
    width: "100%",
    flexDirection: "row",
    height: "100%",
    alignItems: "center",
    paddingTop: 2,
  },
  navText: {
    color: "#000",
    fontFamily: fonts.plusJMedium,
  },
  navBorder: {
    borderBottomWidth: 1,
    borderColor: "#313030",
  },
});
