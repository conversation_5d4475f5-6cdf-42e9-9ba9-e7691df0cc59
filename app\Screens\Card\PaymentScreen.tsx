import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  SafeAreaView,
  Dimensions,
  TextInput,
  TouchableOpacity,
  Image,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import Header from "../../Components/Header";
import P from "../../Components/P";
import H4 from "../../Components/H4";
import Icon from "react-native-vector-icons/Feather";
import PaymentStatusModal, {
  PaymentStatus,
} from "../../Components/PaymentStatusModal";

const { width, height } = Dimensions.get("window");

interface PaymentScreenProps {
  navigation: any;
  route: {
    params: {
      amount: number;
      currency: string;
      convertedAmount: number;
      convertedCurrency: string;
      fee: number;
      totalAmount: number;
    };
  };
}

export default function PaymentScreen({
  navigation,
  route,
}: PaymentScreenProps) {
  const {
    amount,
    currency,
    convertedAmount,
    convertedCurrency,
    fee,
    totalAmount,
  } = route.params;

  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [nameOnCard, setNameOnCard] = useState("");
  const [address, setAddress] = useState("");
  const [postalCode, setPostalCode] = useState("");
  const [selectedCard, setSelectedCard] = useState("");
  const [loading, setLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(
    null
  );
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Format card number with spaces
  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(" ");
    } else {
      return value;
    }
  };

  // Format expiry date (MM/YY)
  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    if (v.length > 2) {
      return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
    }
    return v;
  };

  // Handle payment submission
  const handlePayment = () => {
    // Basic validation
    if (
      !cardNumber ||
      !expiryDate ||
      !cvv ||
      !nameOnCard ||
      !address ||
      !postalCode
    ) {
      alert("Please fill in all fields");
      return;
    }
    setLoading(true);
    setPaymentStatus("processing");
    setShowStatusModal(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      // Randomly determine success or failure for demo purposes
      const isSuccess = Math.random() > 0.3; // 70% chance of success
      if (isSuccess) {
        setPaymentStatus("success");
      } else {
        setPaymentStatus("failed");
        setErrorMessage("Insufficient Wallet Balance");
      }
    }, 3000);
  };

  // Handle button press in the status modal
  const handleStatusButtonPress = () => {
    setShowStatusModal(false);
    if (paymentStatus === "success") {
      // Navigate to pickup location screen
      navigation.navigate("PickupLocationScreen", {
        cardType: currency === "N" ? "ngn" : "usd",
      });
    } else if (paymentStatus === "failed") {
      // Close modal and let user try again
      setShowStatusModal(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header
          title="Make Payment"
          contStyle={{ justifyContent: "flex-start", gap: 24 }}
          showNotification={false}
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          style={{ width: "95%" }}
        >
          {/* Price Information */}
          <View style={styles.priceInfoCard}>
            <View style={styles.priceRow}>
              <View>
                <P style={styles.priceLabel}>Price</P>
                <H4 style={styles.priceValue}>N{amount.toLocaleString()}</H4>
              </View>
              <View style={styles.priceValueContainer}>
                <View style={styles.currencyFlag}>
                  <Image
                    source={{
                      uri: `https://flagcdn.com/w2560/ng.png`,
                    }}
                    style={[styles.flagImage, { objectFit: "fill" }]}
                  />
                </View>
                <P style={styles.currencyText}>NGN</P>
              </View>
            </View>

            <View style={{ height: 1, backgroundColor: "#D7D5D9" }} />

            <View style={styles.priceRow}>
              <View>
                <P style={styles.priceLabel}>Pay</P>
                <H4 style={styles.priceValue}>$3.13</H4>
                <View style={styles.conversionRate}>
                  <P style={styles.conversionText}>
                    $1 = N1600
                  </P>
                </View>
              </View>
              <View style={styles.priceValueContainer}>
                <View style={styles.currencyFlag}>
                  <Image
                    source={{
                      uri: `https://flagcdn.com/w2560/us.png`,
                    }}
                    style={styles.flagImage}
                  />
                </View>
                <P style={styles.currencyText}>USD</P>
              </View>
            </View>
          </View>

          {/* Transaction Fee */}
          <View style={styles.feeContainer}>
            <P style={styles.feeLabel}>Transaction fee</P>
            <P style={styles.feeValue}>${fee}</P>
          </View>

          <View style={styles.divider} />

          {/* Total Amount */}
          <View style={styles.totalContainer}>
            <P style={styles.totalLabel}>Total amount charged</P>
            <P style={styles.totalValue}>$3.63</P>
          </View>

          {/* Payment Form */}
          <View style={styles.formContainer}>
            {/* Previous Card Selection */}
            <View style={styles.formGroup}>
              <P style={styles.formLabel}>Use Previous Card</P>
              <TouchableOpacity style={styles.selectInput}>
                <P style={styles.selectText}>Select Card</P>
                <Icon name="chevron-down" size={20} color="#000" />
              </TouchableOpacity>
            </View>

            {/* Name on Card */}
            <View style={styles.formGroup}>
              <P style={styles.formLabel}>Name on Card</P>
              <TextInput
                style={styles.input}
                placeholder="First name Last Name"
                placeholderTextColor="#D7D5D9"
                value={nameOnCard}
                onChangeText={setNameOnCard}
              />
            </View>
            {/* Card Details Section */}
            <P style={styles.sectionTitle}>Card Details</P>
            {/* Card Number */}
            <View style={styles.formGroup}>
              <TextInput
                style={styles.input}
                placeholder="1234 5678 9101"
                value={cardNumber}
                placeholderTextColor="#D7D5D9"
                onChangeText={(text) => setCardNumber(formatCardNumber(text))}
                keyboardType="numeric"
                maxLength={19} // 16 digits + 3 spaces
              />
            </View>

            {/* Expiry Date and CVV */}
            <View style={styles.rowInputs}>
              <View style={[styles.formGroup, { flex: 1, marginRight: 10 }]}>
                <TextInput
                  style={styles.input}
                  placeholder="MM/YY"
                  value={expiryDate}
                  placeholderTextColor="#D7D5D9"
                  onChangeText={(text) => setExpiryDate(formatExpiryDate(text))}
                  keyboardType="numeric"
                  maxLength={5} // MM/YY
                />
              </View>
              <View style={[styles.formGroup, { flex: 1 }]}>
                <TextInput
                  style={styles.input}
                  placeholder="CVC"
                  value={cvv}
                  onChangeText={setCvv}
                  placeholderTextColor="#D7D5D9"
                  keyboardType="numeric"
                  maxLength={3}
                  secureTextEntry
                />
              </View>
            </View>

            {/* Billing Details Section */}
            <P style={styles.sectionTitle}>Billing Details</P>

            {/* Address */}
            <View style={styles.formGroup}>
              <TextInput
                style={styles.input}
                placeholder="Address"
                value={address}
                placeholderTextColor="#D7D5D9"
                onChangeText={setAddress}
              />
            </View>

            {/* Postal Code */}
            <View style={styles.formGroup}>
              <TextInput
                style={styles.input}
                placeholder="Postal Code"
                value={postalCode}
                placeholderTextColor="#D7D5D9"
                onChangeText={setPostalCode}
              />
            </View>
          </View>

          {/* Pay Button */}
          <View style={styles.payButtonContainer}>
            <Button
              btnText="Pay Now"
              onPress={handlePayment}
              loading={loading}
            />
          </View>
        </ScrollView>

        {/* Payment Status Modal */}
        {paymentStatus && (
          <PaymentStatusModal
            visible={showStatusModal}
            status={paymentStatus}
            errorMessage={errorMessage}
            onButtonPress={handleStatusButtonPress}
            onClose={() => {
              setShowStatusModal(false);
            }}
          />
        )}
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  priceInfoCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginTop: 20,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  priceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
  },
  priceLabel: {
    fontSize: 14,
    color: colors.textAsh,
  },
  priceValueContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  priceValue: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    marginRight: 8,
  },
  currencyFlag: {
    width: 32,
    height: 32,
    borderRadius: 100,
    marginRight: 4,
    overflow: "hidden",
  },
  flagImage: {
    width: 32,
    height: 32,
    borderRadius: 10,
  },
  currencyText: {
    fontSize: 12,
    color: colors.textBlack,
    fontFamily: fonts.plusJSemibold,
  },
  conversionRate: {
    paddingTop: 12,
  },
  conversionText: {
    fontSize: 12,
    color: "#DF7400",
    fontFamily: fonts.plusJMedium,
  },
  feeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  feeLabel: {
    fontSize: 14,
    color: colors.textBlack,
  },
  feeValue: {
    fontSize: 14,
    color: colors.textBlack,
  },
  divider: {
    height: 1,
    borderWidth: 1.5,
    borderColor: "#D7D5D9",
    marginVertical: 12,
    borderStyle: "dashed",
  },
  totalContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  totalLabel: {
    fontSize: 14,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
  },
  totalValue: {
    fontSize: 14,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  formContainer: {
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 12,
    color: colors.textBlack,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 12,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
    marginBottom: 12,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 12,
    fontFamily: fonts.plusJRegular,
  },
  selectInput: {
    height: 48,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  selectText: {
    fontSize: 12,
    color: "#555059",
  },
  rowInputs: {
    flexDirection: "row",
  },
  payButtonContainer: {
    marginTop: 10,
  },
  placeHolderColor: {
    color: "#D7D5D9",
  },
});
