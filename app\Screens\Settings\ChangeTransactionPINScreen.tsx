import React from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Input from "../../Components/Input";
import Button from "../../Components/Button";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { Formik } from "formik";
import * as yup from "yup";

const validationSchema = yup.object().shape({
  newPin: yup
    .string()
    .required("New transaction PIN is required")
    .matches(/^\d{4}$/, "PIN must be 4 digits"),
  confirmPin: yup
    .string()
    .oneOf([yup.ref("newPin"), null], "PINs must match")
    .required("Please confirm your new transaction PIN"),
});

const ChangeTransactionPINScreen: React.FC = () => {
  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={{ paddingBottom: 32 }}>
        <Header title="Change Transaction PIN" />
        <View style={styles.content}>
          <Formik
            initialValues={{ newPin: "", confirmPin: "" }}
            validationSchema={validationSchema}
            onSubmit={(values) => {
              // handle PIN change
            }}
          >
            {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
              <View style={{ width: "100%" }}>
                <Input
                  label="New transaction PIN"
                  placeholder="Enter new transaction PIN"
                  value={values.newPin}
                  onChangeText={handleChange("newPin")}
                  onBlur={handleBlur("newPin")}
                  keyboardType="numeric"
                  error={!!errors.newPin && touched.newPin}
                  errorText={errors.newPin}
                  maxLenght={4}
                />
                <Input
                  label="Confirm new transaction PIN"
                  placeholder="Re-enter new transaction PIN"
                  value={values.confirmPin}
                  onChangeText={handleChange("confirmPin")}
                  onBlur={handleBlur("confirmPin")}
                  keyboardType="numeric"
                  error={!!errors.confirmPin && touched.confirmPin}
                  errorText={errors.confirmPin}
                  maxLenght={4}
                  contStyle={{marginTop: 16}}
                />
                <Button
                  btnText="Confirm"
                  onPress={handleSubmit}
                  style={styles.saveBtn}
                />
              </View>
            )}
          </Formik>
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  content: { flex: 1, padding: 20 },
  saveBtn: {
    marginTop: 40,
    backgroundColor: "#FBC94A",
    borderRadius: 8,
  },
});

export default ChangeTransactionPINScreen; 