import AsyncStorage from '@react-native-async-storage/async-storage';

// Key for storing user email in AsyncStorage
const USER_EMAIL_KEY = 'user_email';

/**
 * Stores the user's email in AsyncStorage
 * 
 * @param email - The email to store
 * @returns A promise that resolves when the email is stored
 * 
 * @example
 * ```
 * await storeUserEmail('<EMAIL>');
 * ```
 */
export const storeUserEmail = async (email: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(USER_EMAIL_KEY, email);
    console.log('User email stored successfully');
  } catch (error) {
    console.error('Error storing user email:', error);
    throw error;
  }
};

/**
 * Retrieves the user's email from AsyncStorage
 * 
 * @returns A promise that resolves with the user's email or null if not found
 * 
 * @example
 * ```
 * const email = await getUserEmail();
 * if (email) {
 *   // Use the email
 * }
 * ```
 */
export const getUserEmail = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(USER_EMAIL_KEY);
  } catch (error) {
    console.error('Error retrieving user email:', error);
    return null;
  }
};

/**
 * Removes the user's email from AsyncStorage
 * 
 * @returns A promise that resolves when the email is removed
 * 
 * @example
 * ```
 * await removeUserEmail();
 * ```
 */
export const removeUserEmail = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(USER_EMAIL_KEY);
    console.log('User email removed successfully');
  } catch (error) {
    console.error('Error removing user email:', error);
    throw error;
  }
};
