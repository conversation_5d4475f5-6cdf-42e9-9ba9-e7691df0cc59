import { useContext } from 'react';
import { CredentailsContext } from '../Context/CredentailsContext';
/**
 * Custom hook to update the nextStep in the stored credentials
 *
 * @returns A function to update the nextStep and optionally the currentOnboardingStatus
 *
 * @example
 * ```
 * const { updateUserNextStep } = useUpdateNextStep();
 *
 * // Update just the nextStep
 * updateUserNextStep('/auth/onboarding/kyc/verify');
 *
 * // Update both nextStep and currentOnboardingStatus
 * updateUserNextStep('Done', 'completed');
 * ```
 */
export const useUpdateNextStep = () => {
  const { updateNextStep } = useContext(CredentailsContext);

  /**
   * Updates the nextStep in the stored credentials
   *
   * @param nextStep - The new nextStep value
   */
  const updateUserNextStep = async (nextStep: string) => {
    await updateNextStep(nextStep);
  };

  return { updateUserNextStep };
};

/**
 * Maps a currentOnboardingStep to the corresponding nextStep
 *
 * @param currentStep - The current onboarding step
 * @returns The corresponding nextStep or null if no mapping exists
 *
 * @example
 * ```
 * const nextStep = mapOnboardingStepToNextStep('kyc_not_verified');
 * // nextStep will be '/auth/onboarding/kyc/verify'
 * ```
 */
export const mapOnboardingStepToNextStep = (currentStep: string): string | null => {
  const stepMapping: Record<string, string> = {
    'kyc_not_verified': '/auth/onboarding/kyc/verify',
    'documents_not_uploaded': '/auth/onboarding/documents/upload',
    'pin_pending': '/auth/onboarding/pin/create',
    'email_verified': '/auth/onboarding/kyc/verify',
    // Add more mappings as needed
  };

  return stepMapping[currentStep] || null;
};
