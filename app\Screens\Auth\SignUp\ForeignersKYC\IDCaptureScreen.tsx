import React, { useState, useRef, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  ScrollView,
} from "react-native";
import { colors } from "../../../../Config/colors";
import Div from "../../../../Components/Div";
import H4 from "../../../../Components/H4";
import P from "../../../../Components/P";
import { fonts } from "../../../../Config/Fonts";
import Icon from "react-native-vector-icons/Feather";
import { Camera, CameraView } from "expo-camera";
import PageHeader from "../../../../Components/PageHeader";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../../Config/Svg";

const { width, height } = Dimensions.get("window");

export default function IDCaptureScreen({ navigation }) {
  const [hasPermission, setHasPermission] = useState(null);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const cameraRef = useRef(null);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === "granted");
    })();
  }, []);

  const handleCameraReady = () => {
    setIsCameraReady(true);
  };

  const takePicture = async () => {
    if (cameraRef.current && isCameraReady) {
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.5,
        });
        // Navigate to next screen with the photo
        navigation.navigate("ForeignersSelfieScreen", { idPhoto: photo.uri });
      } catch (error) {
        console.log("Error taking picture:", error);
      }
    }
  };

  if (hasPermission === null) {
    return <View />;
  }

  if (hasPermission === false) {
    return (
      <View style={styles.permissionContainer}>
        <P style={styles.permissionText}>No access to camera</P>
      </View>
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={4}
            totalPages={6}
            type="bars"
            onBack={() => navigation.pop()}
          />
          <ScrollView>
            <CameraView
              ref={cameraRef}
              style={styles.camera}
              facing={"back"}
              ratio="1:1"
              onCameraReady={handleCameraReady}
            >
              <View style={styles.frameBorder} />
            </CameraView>
            <View style={styles.instructionsContainer}>
              <P style={styles.instructionTitle}>
                Position your ID within the frame
              </P>

              <P style={styles.tipsTitle}>Tips for best results:</P>
              <View style={styles.tipItem}>
                <SvgXml xml={svg.bulb} />
                <P style={styles.tipText}>Ensure good lighting</P>
              </View>
              <View style={styles.tipItem}>
                <SvgXml xml={svg.dimension} />
                <P style={styles.tipText}>Fit document within the frame</P>
              </View>
              <View style={styles.tipItem}>
                <SvgXml xml={svg.cursor} />
                <P style={styles.tipText}>Avoid glare and shadows</P>
              </View>

              <TouchableOpacity
                style={styles.captureButton}
                onPress={takePicture}
                disabled={!isCameraReady}
              >
                <View style={styles.captureOuterCircle}>
                  <View style={styles.captureInnerCircle} />
                </View>
                <View style={{flexDirection: "row", alignItems: "center", gap: 10}}>
                  <SvgXml xml={svg.cam}/>

                <P style={styles.captureText}>Take Photo</P>
                  </View>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.black,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  permissionContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  permissionText: {
    fontSize: 16,
    color: colors.textBlack,
  },
  camera: {
    width: "90%",
    height: (30 * height) / 100,
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
  },
  overlay: {
    flex: 1,
    backgroundColor: "transparent",
  },
  topBar: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    paddingTop: 40,
  },
  backButton: {
    padding: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: "#333333",
    borderRadius: 2,
    marginLeft: 16,
    flexDirection: "row",
  },
  progressFilled: {
    flex: 1,
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  progressEmpty: {
    flex: 2,
    backgroundColor: "transparent",
  },
  frameBorder: {
    width: "80%",
    height: "80%", // Passport aspect ratio
    alignSelf: "center",
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: "dashed",
    borderRadius: 8,
  },
  instructionsContainer: {
    // position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  instructionTitle: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    textAlign: "center",
    marginBottom: (4 * height) / 100,
  },
  tipsTitle: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginBottom: (2 * height) / 100,
  },
  tipItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: colors.textBlack,
    marginLeft: 8,
  },
  captureButton: {
    alignItems: "center",
    marginTop: (10 * height) / 100,
  },
  captureOuterCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 3,
    borderColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  captureInnerCircle: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: colors.primary,
  },
  captureText: {
    fontSize: 14,
    color: colors.textBlack,
  },
});
