import React from "react";
import { View, StyleSheet, TouchableOpacity, ScrollView } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import { useNavigation } from "@react-navigation/native";

const verificationItems = [
  {
    label: "Phone number verification",
    status: "Completed",
    icon: svg.phone, // Replace with your actual SVG
    completed: true,
  },
  {
    label: "Email Verification",
    status: "Completed",
    icon: svg.email, // Replace with your actual SVG
    completed: true,
  },
  {
    label: "Identity Verification",
    status: "Pending",
    icon: svg.profile1, // Replace with your actual SVG
    completed: false,
    onPress: () => {}, // Add navigation if needed
  },
  {
    label: "Job Details",
    status: "Pending",
    icon: svg.briefcase, // Replace with your actual SVG
    completed: false,
    onPress: () => {}, // Add navigation if needed
  },
  {
    label: "Address Verification",
    status: "Pending",
    icon: svg.location, // Replace with your actual SVG
    completed: false,
    onPress: () => {}, // Add navigation if needed
  },
];

const VerificationScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={{ paddingBottom: 32 }}>
        <Header title="Verification" />
        <View style={styles.box}>
          {verificationItems.map((item, idx) => (
            <TouchableOpacity
              key={item.label}
              style={[styles.row, idx === verificationItems.length - 1 && { borderBottomWidth: 0 }]}
              activeOpacity={item.onPress ? 0.7 : 1}
              onPress={item.onPress}
              disabled={!item.onPress}
            >
              <SvgXml xml={item.icon} width={22} height={22} style={styles.icon} />
              <View style={styles.textContainer}>
                <P style={[styles.status, { color: item.completed ? "#B6B6B6" : "#B6B6B6" }]}> 
                  {item.status}
                </P>
                <P style={styles.label}>{item.label}</P>
              </View>
              {item.completed ? (
                // TODO: Replace with checkCircle SVG
                <SvgXml xml={svg.checkCircle}/>
              ) : (
                // TODO: Replace with chevronRight SVG
                <SvgXml xml={svg.chevronRight} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  box: {
    borderRadius: 16,
    margin: 20,
    padding: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  icon: { marginRight: 16 },
  textContainer: { flex: 1 },
  status: {
    fontFamily: fonts.plusJMedium,
    fontSize: 13,
    color: "#B6B6B6",
  },
  label: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 15,
    color: colors.textBlack,
  },
});

export default VerificationScreen; 