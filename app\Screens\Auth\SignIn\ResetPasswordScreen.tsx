import React, { useState, useEffect } from "react";
import { Dimensions, StyleSheet, View, ScrollView, Keyboard } from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";
import Input from "../../../Components/Input";
import Icon from "react-native-vector-icons/Feather";
import { ForgotPasswordResetPassword } from "../../../RequestHandler.tsx/Auth";
import { useToast } from "../../../Context/ToastContext";

const { height } = Dimensions.get("window");

export default function ResetPasswordScreen({ navigation, route }) {
  const { email } = route.params || {};
  const { handleToast } = useToast();

  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isFormFilled, setIsFormFilled] = useState(false);
  const [loading, setLoading] = useState(false);
  const [passwordError, setPasswordError] = useState("");

  // Password validation states
  const [hasMinLength, setHasMinLength] = useState(false);
  const [hasUpperCase, setHasUpperCase] = useState(false);
  const [hasLowerCase, setHasLowerCase] = useState(false);
  const [hasNumber, setHasNumber] = useState(false);
  const [hasSpecialChar, setHasSpecialChar] = useState(false);

  // Check password requirements
  useEffect(() => {
    setHasMinLength(newPassword.length >= 8);
    setHasUpperCase(/[A-Z]/.test(newPassword));
    setHasLowerCase(/[a-z]/.test(newPassword));
    setHasNumber(/[0-9]/.test(newPassword));
    setHasSpecialChar(
      /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(newPassword)
    );

    // Check if form is filled and valid
    if (
      newPassword &&
      confirmPassword &&
      hasMinLength &&
      hasUpperCase &&
      hasLowerCase &&
      hasNumber
    ) {
      setIsFormFilled(true);
    } else {
      setIsFormFilled(false);
    }
  }, [newPassword, confirmPassword]);

  const handleNext = async () => {
    Keyboard.dismiss();
    // Validate passwords match
    if (newPassword !== confirmPassword) {
      setPasswordError("Passwords do not match");
      return;
    }
    setLoading(true);
    try {
      const body = {
        email,
        newPassword,
        confirmPassword
      };
      await ForgotPasswordResetPassword(body);
      handleToast("Password reset successful", "success");
      // Navigate to login screen
      navigation.navigate("LoginScreen");
    } catch (error) {
      console.log(error);
      handleToast(error.message || "Failed to reset password", "error");
    } finally {
      setLoading(false);
    }
  };

  // Render validation item with icon
  const ValidationItem = ({ text, isValid }) => (
    <View style={styles.validationItem}>
      <Icon
        name={isValid ? "check" : "x"}
        size={16}
        color={isValid ? colors.success : colors.error}
      />
      <P
    // @ts-ignore
        style={[
          styles.validationText,
          { color: colors.textAsh},
        ]}
      >
        {text}
      </P>
    </View>
  );

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader onBack={() => navigation.pop()} />
          <ScrollView
            automaticallyAdjustContentInsets={true}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              alignItems: "center",
              minHeight: "90%",
            }}
          >
            <View style={styles.contentContainer}>
              <H4 style={styles.mainTitle}>Enter a new password</H4>
              <P style={styles.subtitle}>
                Reset password. Your new password must be different from the
                previous one
              </P>

              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Input
                    label={"New password"}
                    placeholder="Enter your new password"
                    value={newPassword}
                    onChangeText={setNewPassword}
                    type={"password"}
                    autoCapitalize="none"
                    errorText={passwordError}
                  />

                  {newPassword.length > 0 && (
                    <View style={styles.validationContainer}>
                      <ValidationItem
                        text="Minimum of 8 characters"
                        isValid={hasMinLength}
                      />
                      <ValidationItem
                        text="At least one uppercase letter"
                        isValid={hasUpperCase}
                      />
                      <ValidationItem
                        text="At least one lowercase letter"
                        isValid={hasLowerCase}
                      />
                      <ValidationItem
                        text="At least one number"
                        isValid={hasNumber}
                      />
                      <ValidationItem
                        text="At least one special character"
                        isValid={hasSpecialChar}
                      />
                    </View>
                  )}
                </View>

                <View style={styles.inputGroup}>
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label={"Confirm new password"}
                    placeholder="Re-enter your new password"
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    type={"password"}
                    autoCapitalize="none"
                    error={passwordError? true: false}
                    errorText={passwordError}
                  />
                </View>
              </View>
            </View>

            {isFormFilled && (
              <View style={{ width: "90%", marginBottom: (5 * height) / 100 }}>
                <Button btnText="Next" onPress={handleNext} loading={loading} />
              </View>
            )}
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  formContainer: {
    width: "100%",
  },
  inputGroup: {
    marginBottom: 16,
  },
  validationContainer: {
    marginTop: 12,
    marginBottom: 8,
  },
  validationItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  validationText: {
    fontFamily: fonts.plusJLight,
    fontSize: 12,
    marginLeft: 8,
  },
});
