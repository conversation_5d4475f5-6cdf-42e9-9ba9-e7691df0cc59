import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  SafeAreaView,
  StatusBar,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import { SvgXml } from "react-native-svg";
import MenuItem from "../Components/MenuItem";
import TransactionItem from "../Components/TransactionItem";
import Div from "../Components/Div";
import Loader from "../Components/Loader";
import NetworkError from "../Components/NetworkError";
import { svg } from "../Config/Svg";
import { GetUser, GetWallet } from "../RequestHandler.tsx/User";

export default function HomeScreen({ navigation }) {
  const [hasTransactions, setHasTransactions] = useState(true);
  const [lastName, setLastName] = useState("...");
  const [avatar, setAvatar] = useState(require("../assets/avatar.png"));
  const [loading, setLoading] = useState(false);
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [isTimeoutError, setIsTimeoutError] = useState(false);

  const getUser = async () => {
    setLoading(true);
    setIsNetworkError(false);
    setIsTimeoutError(false);
    try {
      const user = await GetUser();
      console.log(user);
      
      if (user.data) {
        setLastName(user?.data?.lastName);
      }
      if (
        user.data.avatar !== "" &&
        user.data.avatar !== null &&
        user.data.avatar !== undefined
      ) {
        setAvatar({ uri: user?.data?.avatar });
      } else {
        setAvatar(require("../assets/avatar.png"));
      }
    } catch (error) {
      console.log(error);

      // Check for timeout errors
      if (error?.message?.includes('timeout') || error?.message?.includes('Request timeout')) {
        setIsTimeoutError(true);
      }
      // Check for network errors
      else if (error.isNetworkError) {
        setIsNetworkError(true);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getUser();
  }, []);

  // Sample transactions data
  const transactions = [
    // {
    //   id: 1,
    //   name: "Beauty Emporium Ltd.",
    //   status: "Successful",
    //   amount: 200,
    //   isDebit: true,
    // },
    // {
    //   id: 2,
    //   name: "Ronald Balium",
    //   status: "Successful",
    //   amount: 2000,
    //   isDebit: false,
    // },
    // {
    //   id: 3,
    //   name: "USDC",
    //   status: "Declined",
    //   amount: 3000,
    //   isDebit: true,
    // },
  ];

  // Show network error if there's a network issue
  if (isNetworkError) {
    return (
      <Div>
        <NetworkError type="fullscreen" errorType="network" onRetry={getUser} />
      </Div>
    );
  }

  // Show timeout error if request timed out
  if (isTimeoutError) {
    return (
      <Div>
        <NetworkError type="fullscreen" errorType="timeout" onRetry={getUser} />
      </Div>
    );
  }

  if (loading) {
    return <Loader type="overlay" size="large" style={{ marginLeft: 8 }} />;
  }
  return (
    <Div>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.userInfo}>
            <Image source={avatar} style={styles.avatar} />
            <Text style={styles.welcomeText}>Welcome {lastName}</Text>
            {/* {loading && (

            )} */}
          </View>
          <TouchableOpacity style={styles.notificationButton}>
            <SvgXml xml={svg.notification} />
          </TouchableOpacity>
        </View>

        {/* Menu Grid */}
        <View style={styles.menuGrid}>
          <View style={styles.menuRow}>
            <MenuItem
              icon={svg.bCard}
              label="Cards"
              onPress={() => navigation.navigate("Card")}
            />
            <MenuItem
              icon={svg.homeTHub}
              label="Transaction Hub"
              onPress={() => navigation.navigate("WalletsScreen")}
            />
          </View>
          <View style={styles.menuRow}>
            <MenuItem
              icon={svg.payment}
              label="Payments"
              onPress={() => navigation.navigate("PaymentsScreen")}
            />
            <MenuItem
              icon={svg.simcard}
              label="E-Sims"
              onPress={() => navigation.navigate("ESimsScreen")}
            />
          </View>
        </View>

        {/* Recent Transactions */}
        <View style={styles.transactionsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Transactions</Text>
            <TouchableOpacity>
              <Text style={styles.seeMoreText}>See More</Text>
            </TouchableOpacity>
          </View>

          {transactions.length > 0 ? (
            <View style={styles.transactionsList}>
              {transactions.map((transaction) => (
                <TouchableOpacity
                  key={transaction.id}
                  onPress={() =>
                    navigation.navigate("TransactionDetailsScreen", {
                      transactionData: transaction,
                    })
                  }
                >
                  <TransactionItem
                    name={transaction.name}
                    status={transaction.status}
                    amount={transaction.amount}
                    isDebit={transaction.isDebit}
                  />
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.emptyTransactions}>
              <Image
                source={require("../assets/empty-transactions.png")}
                style={styles.emptyTransactionsImage}
              />
              <Text style={styles.emptyTransactionsTitle}>
                Manage your transactions
              </Text>
              <Text style={styles.emptyTransactionsSubtitle}>
                Get a card and start{"\n"}managing your transactions
              </Text>
              <TouchableOpacity
                style={styles.getCardButton}
                onPress={() => {
                  navigation.navigate("Card");
                }}
              >
                <Text style={styles.getCardButtonText}>Get your card</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </Div>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    width: "100%",
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  welcomeText: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
  },
  notificationButton: {
    padding: 8,
    backgroundColor: "#FDECC8",
    borderRadius: 100,
  },
  menuGrid: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  menuRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  transactionsSection: {
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
    color: colors.textBlack,
  },
  seeMoreText: {
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
    color: colors.deepPrimary,
  },
  transactionsList: {
    marginBottom: 20,
  },
  emptyTransactions: {
    alignItems: "center",
    paddingVertical: 24,
  },
  emptyTransactionsImage: {
    width: 130,
    height: 98,
    marginBottom: 16,
  },
  emptyTransactionsTitle: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 8,
  },
  emptyTransactionsSubtitle: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: colors.textAsh,
    textAlign: "center",
    marginBottom: 24,
  },
  getCardButton: {
    backgroundColor: "#FFCB45",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  getCardButtonText: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
    color: colors.textBlack,
  },
});
