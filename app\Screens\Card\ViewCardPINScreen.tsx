import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import P from "../../Components/P";

const { width, height } = Dimensions.get("window");

interface ViewCardPINScreenProps {
  navigation: any;
}

export default function ViewCardPINScreen({
  navigation,
}: ViewCardPINScreenProps) {
  // Demo PIN
  const cardPIN = "4696";

  // Handle reset PIN
  const handleResetPIN = () => {
    navigation.navigate("VerifyPINScreen", { action: "change" });
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header title="Card PIN" showNotification={true} />

        <View style={styles.content}>
          <Text style={styles.pinTitle}>Your Card PIN</Text>

          <View style={styles.pinContainer}>
            {cardPIN.split("").map((digit, index) => (
              <View key={index} style={styles.pinDigitContainer}>
                <Text style={styles.pinDigit}>{digit}</Text>
              </View>
            ))}
          </View>

          <TouchableOpacity 
            style={styles.resetPinButton}
            onPress={handleResetPIN}
          >
            <P style={styles.resetPinText}>Reset your card PIN</P>
          </TouchableOpacity>
        </View>
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 40,
    alignItems: "center",
  },
  pinTitle: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    marginBottom: 20,
  },
  pinContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 40,
  },
  pinDigitContainer: {
    marginHorizontal: 10,
  },
  pinDigit: {
    fontSize: 36,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  resetPinButton: {
    // marginTop: 20,
  },
  resetPinText: {
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.deepPrimary,
  },
});
