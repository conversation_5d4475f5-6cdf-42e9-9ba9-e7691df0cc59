import React, { useCallback, useState } from "react";
import { View, StyleSheet, TouchableOpacity, Modal, Dimensions, Animated, ScrollView } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Input from "../../Components/Input";
import Button from "../../Components/Button";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { GetUser } from "../../RequestHandler.tsx/User";
import Loader from "../../Components/Loader";
import NetworkError from "../../Components/NetworkError";
import { getCountryInfo } from "../../utils/countryUtils";

const { height } = Dimensions.get("window");

const AccountInfoScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const slideAnim = React.useRef(new Animated.Value(height)).current;
  const [fullName, setFullName] = useState("")
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState<any>([])
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [isTimeoutError, setIsTimeoutError] = useState(false);

  // Placeholder user data
  const user = {
    name: "Anita Amadi Balikuliama",
    email: "<EMAIL>",
    phone: "+234 678 1279777",
    country: "Nigeria",
    nationality: "Nigerian",
    dob: "1994 - 10 - 15",
    address: "34, kingston Road, Abakaliki, Nigeria",
  };

  const openModal = () => {
    setIsModalVisible(true);
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeModal = () => {
    Animated.timing(slideAnim, {
      toValue: height,
      duration: 300,
      useNativeDriver: true,
    }).start(() => setIsModalVisible(false));
  };

  const getUser = async () => {
    setLoading(true);
    try {
      const user = await GetUser();
      console.log(user);
      if (user.data) {
        setUserData(user.data);
      }
    } catch (error) {
      if (error?.message?.includes('timeout') || error?.message?.includes('Request timeout')) {
        setIsTimeoutError(true);
      }
      else if (error.isNetworkError) {
        setIsNetworkError(true);
      }
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(useCallback(() => {
    getUser()
  }, []))

  if (isNetworkError || isTimeoutError) {
    return (
      <Div>
        <NetworkError type="fullscreen" errorType={isNetworkError ? "network" : "timeout"} onRetry={getUser} />
      </Div>
    );
  }
  if (loading) {
    return <Loader type="overlay" size="large" style={{ marginLeft: 8 }} />;
  }
  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={{ paddingBottom: 32 }}>
        <Header title="Account Information" />
        <View style={styles.infoBox}>
          <P style={styles.label}>Name</P>
          <P style={styles.value}>{userData?.firstName + " " + userData?.lastName}</P>
          <P style={styles.label}>Email</P>
          <P style={styles.value}>{userData.email}</P>
          <P style={styles.label}>Phone Number</P>
          <P style={styles.value}>{userData?.phone || "Not provided"}</P>
          <P style={styles.label}>Country</P>
          <P style={styles.value}>{getCountryInfo(userData.country).country}</P>
          <P style={styles.label}>Nationality</P>
          <P style={styles.value}>{getCountryInfo(userData.country).nationality}</P>
          <P style={styles.label}>Date of Birth</P>
          <P style={styles.value}>{userData?.profile?.dob}</P>
          <P style={styles.label}>Address</P>
          <P style={styles.value}>{userData?.address || "Not provided"}</P>
        </View>
        <TouchableOpacity style={styles.editBtn} onPress={openModal}>
          <P style={styles.editBtnText}>Edit</P>
        </TouchableOpacity>
      </ScrollView>
      {/* Bottom Sheet Modal */}
      <Modal
        transparent
        visible={isModalVisible}
        animationType="none"
        onRequestClose={closeModal}
        statusBarTranslucent
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity style={styles.backdrop} activeOpacity={1} onPress={closeModal} />
          <Animated.View style={[styles.bottomSheet, { transform: [{ translateY: slideAnim }] }]}>
            <View style={styles.sheetHandle} />
            <P style={styles.sheetTitle}>Edit Account Information</P>
            <P style={styles.sheetMessage}>
              Sorry you cannot change your account details because your information has been verified. To make any changes, please send an email to
            </P>
            <P style={styles.sheetEmail}><EMAIL></P>
          </Animated.View>
        </View>
      </Modal>
    </Div>
  );
};

const styles = StyleSheet.create({
  infoBox: {
    borderRadius: 12,
    marginLeft: 24,
    padding: 12,
  },
  label: {
    fontFamily: fonts.plusJMedium,
    fontSize: 13,
    color: "#888",
    marginTop: 10,
  },
  value: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 15,
    color: colors.textBlack,
    marginTop: 2,
  },
  editBtn: {
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 32,
    alignSelf: "flex-start",
    marginTop: 33,
    marginLeft: 24,
  },
  editBtnText: {
    color: colors.textBlack,
    fontFamily: fonts.plusJBold,
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bottomSheet: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 32,
    minHeight: 180,
    alignItems: "center",
  },
  sheetHandle: {
    width: 50,
    height: 5,
    borderRadius: 3,
    backgroundColor: colors.textBlack,
    alignSelf: "center",
    marginBottom: 12,
  },
  sheetTitle: {
    fontFamily: fonts.plusJBold,
    fontSize: 18,
    color: colors.textBlack,
    marginBottom: 12,
    textAlign: "center",
  },
  sheetMessage: {
    fontFamily: fonts.plusJRegular,
    fontSize: 15,
    color: colors.textBlack,
    textAlign: "center",
    marginBottom: 8,
  },
  sheetEmail: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
    textAlign: "center",
  },
});

export default AccountInfoScreen; 