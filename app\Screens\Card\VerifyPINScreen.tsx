import React, { useState, useEffect, useRef } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  TextInput,
  Alert,
  ScrollView,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Button from "../../Components/Button";
import P from "../../Components/P";

const { width, height } = Dimensions.get("window");

interface VerifyPINScreenProps {
  navigation: any;
  route?: {
    params?: {
      action?: "view" | "change";
    };
  };
}

export default function VerifyPINScreen({
  navigation,
  route,
}: VerifyPINScreenProps) {
  const action = route?.params?.action || "view";
  const [pin, setPin] = useState<string[]>(["", "", "", ""]);
  // Refs for input fields
  const pinInputRefs = useRef([]);

  // Initialize refs
  useEffect(() => {
    pinInputRefs.current = pinInputRefs.current.slice(0, 4);

    // Focus the first input when component mounts
    setTimeout(() => {
      if (pinInputRefs.current[0]) {
        pinInputRefs.current[0].focus();
      }
    }, 100);
  }, []);

  const handlePinChange = (text: string, index: number) => {
    // Only allow numeric input and limit to 1 digit
    if (/^[0-9]*$/.test(text)) {
      const newPin = [...pin];

      // If pasting multiple digits
      if (text.length > 1) {
        const digits = text.split('').slice(0, 4);

        // Fill current and subsequent inputs
        for (let i = 0; i < digits.length && i + index < 4; i++) {
          newPin[index + i] = digits[i];
        }

        setPin(newPin);

        // Focus the next empty input or the last input
        const nextIndex = Math.min(index + text.length, 3);
        if (pinInputRefs.current[nextIndex]) {
          pinInputRefs.current[nextIndex].focus();
        }
      } else {
        // Normal single digit input
        newPin[index] = text;
        setPin(newPin);

        // Auto-focus next input
        if (text !== "" && index < 3) {
          pinInputRefs.current[index + 1].focus();
        }
      }
    }
  };

  // Handle backspace key press
  const handleKeyPress = (e: any, index: number, isPinInput: boolean) => {
    if (e.nativeEvent.key === "Backspace") {
      if (isPinInput) {
        // If current input is empty and not the first input, move to previous input
        if (pin[index] === "" && index > 0) {
          const newPin = [...pin];
          newPin[index - 1] = ""; // Clear the previous input
          setPin(newPin);
          pinInputRefs.current[index - 1].focus();
        } else if (pin[index] !== "") {
          // If current input has a value, clear it but stay focused
          const newPin = [...pin];
          newPin[index] = "";
          setPin(newPin);
        }
      }
    }
  };

  // Handle verify PIN
  const handleVerifyPIN = () => {
    const enteredPin = pin.join("");

    // Check if PIN is complete
    if (enteredPin.length !== 4) {
      Alert.alert("Error", "Please enter a complete 4-digit PIN");
      return;
    }

    // For demo purposes, we'll use "1234" as the correct PIN
    // In a real app, this would be verified against an API
    const correctPin = "1234";

    // Simulate API verification with a slight delay for better UX
    setTimeout(() => {
      if (enteredPin === correctPin) {
        // Navigate based on action
        if (action === "view") {
          navigation.navigate("ViewCardPINScreen");
        } else if (action === "change") {
          navigation.navigate("ChangeCardPINScreen");
        } else {
          // Default fallback if action is not specified
          navigation.goBack();
        }
      } else {
        Alert.alert(
          "Incorrect PIN",
          "The PIN you entered is incorrect. Please try again.",
          [
            {
              text: "OK",
              onPress: () => {
                // Reset PIN
                setPin(["", "", "", ""]);
                // Focus the first input after reset
                setTimeout(() => {
                  if (pinInputRefs.current[0]) {
                    pinInputRefs.current[0].focus();
                  }
                }, 100);
              }
            }
          ]
        );
      }
    }, 500);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header title="Verify Payment PIN" showNotification={false} />
        <ScrollView style={{ width: "95%" }}>
          <View style={styles.content}>
            <View style={styles.pinContainer}>
              <View style={styles.pinItemWrap}>
                <P style={styles.pinLabel}>Please Enter Payment PIN</P>
                <View style={styles.pinInputContainer}>
                  {[0, 1, 2, 3].map((index) => {
                    const isFilled = pin[index] !== "";
                    return (
                      <TextInput
                        key={`pin-${index}`}
                        ref={(ref) => (pinInputRefs.current[index] = ref)}
                        style={[
                          styles.pinInput,
                        ]}
                        value={pin[index]}
                        onChangeText={(text) => handlePinChange(text, index)}
                        keyboardType="numeric"
                        maxLength={1}
                        secureTextEntry={true}
                        onKeyPress={(e) => handleKeyPress(e, index, true)}
                        selectionColor={colors.primary}
                      />
                    );
                  })}
                </View>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Button btnText="Verify" onPress={handleVerifyPIN} />
            </View>
          </View>
        </ScrollView>
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 40,
  },
  pinContainer: {
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    paddingTop: 21,
    paddingBottom: 35,
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
  },
  pinItemWrap: {
    width: "60%",
  },
  pinLabel: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: "#555059",
    marginBottom: 16,
    textAlign: "center",
  },
  pinInputContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  pinInput: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderColor: "#8E8693",
    borderRadius: 4,
    textAlign: "center",
    fontSize: 16,
    color: colors.textAsh,
    fontFamily: fonts.plusJMedium,
    backgroundColor: colors.white,
  },
  pinInputError: {
    borderColor: colors.error || "red",
  },
  pinInputActive: {
    borderColor: colors.primary,
    borderWidth: 2,
  },
  pinInputFilled: {
    borderColor: colors.primary,
    backgroundColor: colors.primarySubtle,
  },
  buttonContainer: {
    marginTop: 45,
  },
});
