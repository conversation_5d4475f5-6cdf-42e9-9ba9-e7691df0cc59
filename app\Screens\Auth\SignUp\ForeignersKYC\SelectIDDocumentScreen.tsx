import React, { useEffect, useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  ScrollView,
} from "react-native";
import { colors } from "../../../../Config/colors";
import Div from "../../../../Components/Div";
import H4 from "../../../../Components/H4";
import P from "../../../../Components/P";
import { fonts } from "../../../../Config/Fonts";
import Button from "../../../../Components/Button";
import PageHeader from "../../../../Components/PageHeader";
import Icon from "react-native-vector-icons/Feather";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../../Config/Svg";

const { width, height } = Dimensions.get("window");

export default function SelectIDDocumentScreen({ navigation }) {
  const [selectedDocument, setSelectedDocument] = useState("");

  const handleContinue = () => {
    navigation.navigate("IDCaptureScreen");
  };


  useEffect(()=>{
    console.log("eren");
    
  },[])
  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={3}
            totalPages={6}
            onBack={() => navigation.pop()}
            type="bars"
          />
          <ScrollView>
            <View style={styles.contentContainer}>
              <H4 style={styles.mainTitle}>Select ID Document</H4>
              <P style={styles.subtitle}>
                Choose the type of document you'd like to use for verification
              </P>

              <TouchableOpacity
                style={styles.documentOption}
                onPress={() => setSelectedDocument("passport")}
              >
                <View style={styles.documentIconContainer}>
                  <SvgXml xml={svg.passport} />
                </View>
                <View style={styles.documentInfo}>
                  <P style={styles.documentTitle}>Passport</P>
                  <P style={styles.documentDescription}>
                    Valid international passport with clear photo and text
                  </P>
                </View>
                <View style={styles.radioContainer}>
                  <View
                    style={[
                      styles.radioOuter,
                      selectedDocument === "passport" &&
                        styles.radioOuterSelected,
                    ]}
                  >
                    {selectedDocument === "passport" && (
                      <View style={styles.radioInner} />
                    )}
                  </View>
                </View>
              </TouchableOpacity>

              <View style={styles.warningContainer}>
                <SvgXml xml={svg.infoBold} />
                <P style={styles.warningText}>
                  Make sure your document is valid and not expired. The document
                  should be original, not a copy.
                </P>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Button
                btnText="Continue"
                onPress={handleContinue}
                disabled={!selectedDocument}
                btnTextStyle={{ fontFamily: fonts.plusJRegular }}
              />
            </View>
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 24,
  },
  documentOption: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#EEEEEE",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  documentIconContainer: {
    width: 45,
    height: 48,
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
    color: colors.textAsh,
  },
  radioContainer: {
    position: "absolute",
    top: 16,
    right: 16,
  },
  radioOuter: {
    width: 16,
    height: 16,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: colors.black,
    justifyContent: "center",
    alignItems: "center",
  },
  radioOuterSelected: {
    borderColor: colors.primary,
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  warningContainer: {
    flexDirection: "row",
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  warningText: {
    fontSize: 14,
    color: colors.textAsh,
    marginLeft: 12,
    flex: 1,
  },
  buttonContainer: {
    width: "90%",
    marginTop: 32,
    alignSelf: "center",
  },
});
