import '@react-navigation/native';
import '@react-navigation/bottom-tabs';
import '@react-navigation/stack';

// Extend the NavigationContainer type to make id optional
declare module '@react-navigation/native' {
  export interface NavigationContainerProps {
    id?: undefined;
  }
}

// Extend the Navigator type to make id optional
declare module '@react-navigation/bottom-tabs' {
  export interface BottomTabNavigationConfig {
    id?: undefined;
  }
}

// Extend the Navigator type to make id optional
declare module '@react-navigation/stack' {
  export interface StackNavigationConfig {
    id?: undefined;
  }
}
