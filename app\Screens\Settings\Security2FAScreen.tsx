import React from "react";
import { View, StyleSheet, Image, ScrollView, Dimensions } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Button from "../../Components/Button";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { useNavigation } from "@react-navigation/native";

const { width } = Dimensions.get("window");

const Security2FAScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  return (
    <Div>
      <ScrollView
        style={{ width: "100%" }}
        contentContainerStyle={{ paddingBottom: 32, flexGrow: 1 }}
      >
        <Header title="Security" />
        <View style={styles.content}>
          <P style={styles.infoText}>
            2FA adds an extra layer of security to your account by requiring a
            code in addition to your password and PIN before logging in.
          </P>
          <Image
            source={require("../../assets/shield-check.png")}
            style={styles.shieldIcon}
          />
          <View style={{ width: "100%" }}>
            <Button
              btnText="Activate 2FA"
              onPress={() => navigation.navigate("Setup2FAScreen")}
              style={styles.activateBtn}
            />
          </View>
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: "center",
    // justifyContent: "center",
    padding: 20,
  },
  infoText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 15,
    color: "#888",
    textAlign: "center",
    marginBottom: 32,
  },
  shieldIcon: {
    width: (50 * width) / 100,
    height: (50 * width) / 100,
    resizeMode: "contain",
    marginBottom: 40,
  },
  activateBtn: {
    width: "100%",
    backgroundColor: "#FBC94A",
    borderRadius: 8,
  },
});

export default Security2FAScreen;
