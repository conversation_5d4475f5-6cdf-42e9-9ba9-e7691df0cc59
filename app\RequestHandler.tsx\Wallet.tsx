import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "./Request";

const request = new RequestHandler();

// Function to get auth token from AsyncStorage
export async function getAuthToken(): Promise<string> {
  try {
    const cookiesString = await AsyncStorage.getItem("cookies");
    if (cookiesString) {
      const cookies = JSON.parse(cookiesString);
      if (
        (cookies && cookies.data && cookies.data.token) ||
        (cookies && cookies.token)
      ) {
        return cookies.data.token ? cookies.data.token : cookies.token;
      }
    }
    return "";
  } catch (error) {
    console.error("Error getting auth token:", error);
    return "";
  }
}

const decodeJwt = (token: string): string | undefined => {
  if (token) {
    try {
      const payload = token.split(".")[1];
      const decodedPayload = JSON.parse(atob(payload));
      return decodedPayload.id;
    } catch (error) {
      console.error("Error decoding JWT:", error);
      return undefined;
    }
  }
  return undefined;
};

export async function CreateWalletForNigerains(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("api/v1/elevate-api/onboard-getly", body, token);
}
export async function GetWalletBalance(accountId: string): Promise<any> {
  const token = await getAuthToken();
  return request.get(`api/v1/elevate-api/getly/user/${accountId}/account-balance`, token);
}
export async function GetAccountDetails(accountId: string): Promise<any> {
  const token = await getAuthToken();
  return request.get(`api/v1/elevate-api/getly/user/account/${accountId}`, token);
}
export async function GetAccountTrsansactions(accountId: string, page: number, limit: number): Promise<any> {
  const token = await getAuthToken();
  return request.get(`api/v1/elevate-api/getly/user/account/${accountId}/transactions?${page}&${limit}`, token);
}
export async function CreateWalletForForigners(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("api/v1/elevate-api/onboard-sudo", body, token);
}
