import { useFocusEffect } from "@react-navigation/native";
import React, { useState, useEffect, useCallback } from "react";
import { View, TouchableOpacity, Animated, StyleSheet } from "react-native";
import { colors } from "../Config/colors";

interface PProps {
  onToggle?: (state: boolean) => void;
  disabled?: boolean;
  isOn?: boolean; // Accept the initial switch state from the parent
}

const CustomSwitch = ({ onToggle, disabled, isOn = false }: PProps) => {
  const [isEnabled, setIsEnabled] = useState(isOn);
  const [animationValue] = useState(new Animated.Value(isOn ? 1 : 0));

  useFocusEffect(
    useCallback(() => {
      // Update the state when the isOn prop changes
      setIsEnabled(isOn);
      Animated.timing(animationValue, {
        toValue: isOn ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }, [isOn])
  );

  const toggleSwitch = () => {
    const toValue = isEnabled ? 0 : 1;
    Animated.timing(animationValue, {
      toValue,
      duration: 200,
      useNativeDriver: false,
    }).start();
    setIsEnabled(!isEnabled);
    // Call the onToggle function with the new state
    if (onToggle) {
      onToggle(!isEnabled);
    }
  };
  const translateX = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: [2, 22], // Adjust these values to match the size of your thumb button
  });

  const trackColor = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["#E6E6E6", colors.primary],
  });

  const thumbColor = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["#FFFFFF", "#FFFFFF"],
  });

  return (
    <TouchableOpacity
      style={styles.container}
      disabled={disabled}
      onPress={toggleSwitch}
    >
      <Animated.View style={[styles.track, { backgroundColor: trackColor }]}>
        <Animated.View
          style={[
            styles.thumb,
            { transform: [{ translateX }], backgroundColor: thumbColor },
          ]}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 48,
    height: 24,
    justifyContent: "center",
  },
  track: {
    width: 48,
    height: 24,
    borderRadius: 100,
    padding: 4,
  },
  thumb: {
    width: 16,
    height: 16,
    borderRadius: 100,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
  },
});

export default CustomSwitch;
