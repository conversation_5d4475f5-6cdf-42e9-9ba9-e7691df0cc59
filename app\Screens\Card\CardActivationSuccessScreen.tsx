import React from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ScrollView,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Button from "../../Components/Button";
import Div from "../../Components/Div";

const { width, height } = Dimensions.get("window");

interface CardActivationSuccessScreenProps {
  navigation: any;
}

export default function CardActivationSuccessScreen({
  navigation,
}: CardActivationSuccessScreenProps) {
  // Navigate to cards screen
  const navigateToCards = () => {
    navigation.pop(8);
  };

  return (
    <View style={styles.container}>
      <Div>
        <ScrollView style={{ width: "100%", minHeight: "100%" }}>
          <View style={styles.content}>
            {/* Success Message */}
            <View style={styles.messageContainer}>
              <Text style={styles.title}>Card Activated!</Text>
              <Text style={styles.subtitle}>
                Please keep your card PIN safe and do not loose it.
              </Text>
            </View>

            {/* Card Images */}
            <View style={styles.cardsContainer}>
              <Image
                source={require("../../assets/onImg2.png")}
                style={[
                  styles.mockupImage,
                  {
                    objectFit: "contain",
                  },
                ]}
              />
              {/* <View style={[styles.cardImage, styles.navyCard]}>
                <Text style={styles.virtualCardText}>Virtual Card</Text>
                <Text style={styles.cardNumberMasked}>**** **** **** ****</Text>
              </View>
              <View style={[styles.cardImage, styles.goldCard]}>
                <Text style={styles.virtualCardText}>Virtual Card</Text>
                <Text style={styles.cardNumberMasked}>**** **** **** ****</Text>
              </View> */}
            </View>

            {/* View Cards Button */}
            <TouchableOpacity
              style={styles.viewCardsButton}
              onPress={navigateToCards}
            >
              <Text style={styles.viewCardsText}>View Cards</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    alignItems: "center",
    paddingHorizontal: 16,
  },
  messageContainer: {
    alignItems: "center",
    marginBottom: 40,
    marginTop: 30,
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    textAlign: "center",
    maxWidth: "80%",
  },
  cardsContainer: {
    position: "relative",
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 40,
  },
  mockupImage: {
    width: "100%",
    borderRadius: 20,
    height: (40 * height) / 100,
    objectFit: "contain",
  },
  cardImage: {
    width: width * 0.8,
    height: 180,
    position: "absolute",
    borderRadius: 12,
    padding: 16,
    justifyContent: "space-between",
  },
  navyCard: {
    top: 0,
    left: "5%",
    backgroundColor: "#0A1128",
    transform: [{ rotate: "-5deg" }],
    zIndex: 1,
  },
  goldCard: {
    top: 40,
    right: "5%",
    backgroundColor: "#FFCC66",
    transform: [{ rotate: "5deg" }],
    zIndex: 2,
  },
  virtualCardText: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.white,
    marginBottom: 40,
  },
  cardNumberMasked: {
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.white,
    letterSpacing: 2,
  },
  viewCardsButton: {
    width: "100%",
    height: 56,
    backgroundColor: colors.primary,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  viewCardsText: {
    fontSize: 16,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  bottomIndicator: {
    width: 40,
    height: 5,
    backgroundColor: "#D1D5DB",
    borderRadius: 2.5,
    position: "absolute",
    bottom: 20,
  },
});
