import React, { useState, useRef, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  ScrollView,
} from "react-native";
import { colors } from "../../../../Config/colors";
import Div from "../../../../Components/Div";
import H4 from "../../../../Components/H4";
import P from "../../../../Components/P";
import { fonts } from "../../../../Config/Fonts";
import Button from "../../../../Components/Button";
import Icon from "react-native-vector-icons/Feather";
import { Camera, CameraView } from "expo-camera";
import PageHeader from "../../../../Components/PageHeader";

const { width, height } = Dimensions.get("window");

export default function ForeignersSelfieScreen({ navigation, route }) {
  const { idPhoto } = route.params || {};
  const [hasPermission, setHasPermission] = useState(null);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [selfiePhoto, setSelfiePhoto] = useState(null);
  const [faceDetected, setFaceDetected] = useState(false);
  const [headTurnProgress, setHeadTurnProgress] = useState(30); // 0-100 progress
  const cameraRef = useRef(null);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === "granted");
    })();
  }, []);

  const handleCameraReady = () => {
    setIsCameraReady(true);
  };

  const takeSelfie = async () => {
    if (cameraRef.current && isCameraReady) {
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
        });
        setSelfiePhoto(photo.uri);
      } catch (error) {
        console.log("Error taking selfie:", error);
      }
    }
  };

  const handleContinue = () => {
    navigation.navigate("AdditionalInformationScreen", {
      idPhoto,
      selfiePhoto,
    });
  };

  const handleRetake = () => {
    setSelfiePhoto(null);
  };

  if (hasPermission === null) {
    return <View />;
  }

  if (hasPermission === false) {
    return (
      <View style={styles.permissionContainer}>
        <P style={styles.permissionText}>No access to camera</P>
      </View>
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={5}
            totalPages={6}
            type="bars"
            onBack={() => navigation.pop()}
          />
          <ScrollView>
            <View style={styles.cameraContainer}>
              {!selfiePhoto ? (
                <CameraView
                  ref={cameraRef}
                  style={styles.camera}
                  facing={"front"}
                  ratio="1:1"
                  onCameraReady={handleCameraReady}
                >
                  <View style={styles.cameraOverlay}>
                    <View style={styles.circleOutline}>
                      <View style={styles.circleDashed} />
                    </View>
                  </View>
                </CameraView>
              ) : (
                <View style={styles.previewContainer}>
                  <Image
                    source={{ uri: selfiePhoto }}
                    style={styles.previewImage}
                  />
                </View>
              )}
            </View>

            <View style={styles.instructionsContainer}>
              <View
                style={{
                  width: "100%",
                  backgroundColor: colors.primarySubtle,
                  borderRadius: 12,
                  padding: 16,
                  marginBottom: 16,
                }}
              >
                <View style={styles.turnInstructionContainer}>
                  <View style={styles.turnIcon}>
                    <Icon
                      name="refresh-cw"
                      size={16}
                      color={colors.textBlack}
                    />
                  </View>
                  <P style={styles.turnInstruction}>
                    Turn your head slowly left to right
                  </P>
                </View>

                <View style={styles.progressBarContainer}>
                  <View style={styles.headTurnProgressBar}>
                    <View
                      style={[
                        styles.headTurnProgress,
                        { width: `${headTurnProgress}%` },
                      ]}
                    />
                  </View>
                </View>
              </View>

              <View style={styles.tipsContainer}>
                <View style={styles.tipItem}>
                  <Icon name="check" size={16} color={colors.textBlack} />
                  <P style={styles.tipText}>Ensure good lighting</P>
                </View>
                <View style={styles.tipItem}>
                  <Icon name="check" size={16} color={colors.textBlack} />
                  <P style={styles.tipText}>Remove glasses or face coverings</P>
                </View>
                <View style={styles.tipItem}>
                  <Icon name="check" size={16} color={colors.textBlack} />
                  <P style={styles.tipText}>Keep a neutral expression</P>
                </View>
              </View>

              {!selfiePhoto ? (
                <Button
                  btnText="Continue"
                  onPress={takeSelfie}
                  disabled={!isCameraReady}
                  btnTextStyle={{fontFamily: fonts.plusJRegular}}
                />
              ) : (
                <View style={styles.buttonGroup}>
                  <Button btnText="Continue" onPress={handleContinue} />
                  <Button
                    btnText="Retake Photo"
                    onPress={handleRetake}
                    style={styles.retakeButton}
                    btnTextStyle={styles.retakeButtonText}
                  />
                </View>
              )}
            </View>
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    paddingTop: 40,
  },
  backButton: {
    padding: 8,
  },
  progressContainer: {
    flex: 1,
    marginLeft: 8,
  },
  progressText: {
    fontSize: 14,
    color: colors.textAsh,
    textAlign: "right",
    marginBottom: 4,
  },
  progressBar: {
    height: 4,
    backgroundColor: "#EEEEEE",
    borderRadius: 2,
    flexDirection: "row",
  },
  progressFilled: {
    flex: 5,
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  progressEmpty: {
    flex: 1,
    backgroundColor: "transparent",
  },
  permissionContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  permissionText: {
    fontSize: 16,
    color: colors.textBlack,
  },
  cameraContainer: {
    width: width - 40,
    height: width - 40,
    alignSelf: "center",
    borderRadius: 8,
    overflow: "hidden",
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  circleOutline: {
    width: width * 0.6,
    height: width * 0.6,
    borderRadius: width * 0.3,
    borderWidth: 2,
    borderColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  circleDashed: {
    width: width * 0.7,
    height: width * 0.7,
    borderRadius: width * 0.35,
    borderWidth: 1,
    borderColor: "#CCCCCC",
    borderStyle: "dashed",
  },
  previewContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  previewImage: {
    width: "100%",
    height: "100%",
    borderRadius: 8,
  },
  instructionsContainer: {
    flex: 1,
    padding: 20,
    paddingTop: 30,
  },
  turnInstructionContainer: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 12,
  },
  turnIcon: {
    width: 32,
    height: 32,
    borderRadius: 15,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  turnInstruction: {
    fontSize: 14,
    color: colors.textBlack,
  },
  progressBarContainer: {
    // marginBottom: 24,
  },
  headTurnProgressBar: {
    height: 6,
    backgroundColor: "#EEEEEE",
    borderRadius: 3,
  },
  headTurnProgress: {
    height: 6,
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  tipsContainer: {
    marginBottom: 30,
  },
  tipItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: colors.textBlack,
    marginLeft: 8,
  },
  buttonGroup: {
    gap: 12,
  },
  retakeButton: {
    backgroundColor: "#EEEEEE",
  },
  retakeButtonText: {
    color: colors.textBlack,
  },
});
