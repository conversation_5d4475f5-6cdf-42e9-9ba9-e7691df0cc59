import AsyncStorage from '@react-native-async-storage/async-storage';
import { removeUserEmail } from './userEmail';

/**
 * Logs out the user by clearing all relevant data from AsyncStorage
 * 
 * @returns A promise that resolves when the logout is complete
 * 
 * @example
 * ```
 * await logoutUser();
 * navigation.navigate('LoginScreen');
 * ```
 */
export const logoutUser = async (): Promise<void> => {
  try {
    // Remove the auth token/credentials
    await AsyncStorage.removeItem('cookies');
    // Remove the user's email
    await removeUserEmail();
    console.log('User logged out successfully');
  } catch (error) {
    console.error('Error logging out user:', error);
    throw error;
  }
};
