import React, { useState, useRef } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  Keyboard,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";
import Input from "../../../Components/Input";
import Icon from "react-native-vector-icons/Feather";
import BottomSheetDropdown from "../../../Components/BottomSheetDropdown";
import { Formik } from "formik";
import * as yup from "yup";

const { width, height } = Dimensions.get("window");

export default function IDInformationScreen({ navigation, route }) {
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const { country } = route.params || {};

  const idTypes = [
    { label: "BVN", value: "bvn" },
    { label: "NIN", value: "nin" },
  ];

  // Define validation schema
  const idValidationSchema = yup.object().shape({
    idType: yup.string().required("Please select an ID type"),
    idNumber: yup
      .string()
      .required("ID number is required")
      .matches(/^\d+$/, "ID number must contain only digits")
      .length(11, "ID number must be exactly 11 digits"),
  });

  const getSubtitleText = (idType) => {
    if (!idType) {
      return "Please enter your BVN or NIN number. We need this to confirm who you are";
    } else {
      return `Please enter your ${idType} number. We need this to confirm who you are`;
    }
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            idType: "",
            idNumber: "",
          }}
          validationSchema={idValidationSchema}
          onSubmit={(values, { setSubmitting }) => {
            Keyboard.dismiss();
            setLoading(true);

            // Simulate API verification
            setTimeout(() => {
              setLoading(false);
              setSubmitting(false);
              // Navigate to next screen
              navigation.navigate("SelfieScreen", {
                data: {
                  idType: values.idType,
                  idNumber: values.idNumber,
                  country: country.alpha_code3
                },
              });
            }, 1500);
          }}
        >
          {(formikProps) => {
            // Check if form has values (not necessarily valid)
            const hasValues =
              formikProps.values.idType !== "" &&
              formikProps.values.idNumber !== "";

            return (
              <View style={styles.container}>
                <PageHeader
                  currentPage={3}
                  totalPages={6}
                  onBack={() => navigation.pop()}
                />
                <ScrollView
                  automaticallyAdjustContentInsets={true}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={{
                    alignItems: "center",
                    minHeight: "90%",
                  }}
                  keyboardShouldPersistTaps="handled"
                >
                  <View style={styles.contentContainer}>
                    <H4 style={styles.mainTitle}>Enter ID information</H4>
                    <P style={styles.subtitle}>
                      {getSubtitleText(formikProps.values.idType)}
                    </P>

                    <View style={styles.formContainer}>
                      {/* Dropdown selector */}
                      <View style={styles.dropdownContainer}>
                        <TouchableOpacity
                          style={styles.dropdownSelector}
                          onPress={() => {
                            Keyboard.dismiss();
                            setIsBottomSheetVisible(true);
                          }}
                        >
                          <P
                            style={[
                              styles.dropdownText,
                              formikProps.values.idType
                                ? styles.selectedDropdownText
                                : {},
                            ]}
                          >
                            {formikProps.values.idType
                              ? formikProps?.values?.idType?.toUpperCase()
                              : "Select an identification number"}
                          </P>
                          <Icon name="chevron-down" size={20} color="#888" />
                        </TouchableOpacity>
                      </View>

                      {/* ID Number Input */}
                      <View style={styles.inputGroup}>
                        <Input
                          label={"Enter Identification Number"}
                          placeholder="Enter your identification number"
                          value={formikProps.values.idNumber}
                          onChangeText={(text) => {
                            // Only allow numeric input and limit to 11 digits
                            const numericText = text.replace(/[^0-9]/g, "");
                            const limitedText = numericText.slice(0, 11);
                            formikProps.setFieldValue("idNumber", limitedText);
                          }}
                          keyboardType="numeric"
                          maxLenght={11}
                          error={
                            formikProps.errors.idNumber &&
                            formikProps.touched.idNumber
                          }
                          errorText={formikProps.errors.idNumber}
                        />

                        {/* Character count indicator */}
                        <View style={styles.characterCountContainer}>
                          <P style={styles.characterCountText}>
                            {formikProps.values.idNumber.length}/11 digits
                          </P>
                        </View>
                      </View>

                      {/* Info text */}
                      {/* {formikProps.values.idType && (
                        <View style={styles.infoContainer}>
                          <Icon
                            name="info"
                            size={16}
                            color={colors.textAsh}
                            style={styles.infoIcon}
                          />
                          <P style={styles.infoText}>
                            {formikProps.values.idType === "BVN"
                              ? "Your Bank Verification Number (BVN) is an 11-digit number issued by your bank."
                              : "Your National Identification Number (NIN) is an 11-digit number issued by NIMC."}
                          </P>
                        </View>
                      )} */}
                    </View>
                  </View>

                  {hasValues && (
                    <View
                      style={{ width: "90%", marginBottom: (5 * height) / 100 }}
                    >
                      <Button
                        btnText="Submit"
                        onPress={formikProps.handleSubmit}
                        loading={loading}
                      />
                    </View>
                  )}
                </ScrollView>

                {/* Bottom Sheet Dropdown */}
                <BottomSheetDropdown
                  isVisible={isBottomSheetVisible}
                  onClose={() => setIsBottomSheetVisible(false)}
                  title="Select ID Type"
                  items={idTypes}
                  onSelectItem={(item) => {
                    formikProps.setFieldValue("idType", item.value);
                    setIsBottomSheetVisible(false);
                  }}
                  selectedValue={formikProps.values.idType}
                />
              </View>
            );
          }}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
    width: "100%",
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  formContainer: {
    width: "100%",
  },
  dropdownContainer: {
    marginBottom: 20,
    zIndex: 10,
  },
  dropdownSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    padding: 16,
  },
  dropdownText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textAsh,
  },
  selectedDropdownText: {
    color: colors.textBlack,
    fontFamily: fonts.plusJMedium,
  },
  inputGroup: {
    marginBottom: 16,
  },
  characterCountContainer: {
    alignItems: "flex-end",
    marginTop: 4,
  },
  characterCountText: {
    fontSize: 12,
    color: colors.textAsh,
    fontFamily: fonts.plusJRegular,
  },
  infoContainer: {
    flexDirection: "row",
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
  },
  infoIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  infoText: {
    fontSize: 14,
    color: colors.textAsh,
    flex: 1,
    fontFamily: fonts.plusJRegular,
  },
});
