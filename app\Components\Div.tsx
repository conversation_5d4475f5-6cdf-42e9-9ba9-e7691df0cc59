import React, { ReactNode } from "react";
import {
  SafeAreaView,
  StyleSheet,
  StatusBar,
  Dimensions,
  ViewStyle,
} from "react-native";
import Constants from "expo-constants";
import { View } from "react-native-reanimated/lib/typescript/Animated";
import { colors } from "../Config/colors";

interface PProps {
  children: ReactNode;
  style?: ViewStyle | ViewStyle[];
}

const { width, height } = Dimensions.get("window");
const MAX_WIDTH = 500;

function Div({ children, style }: PProps) {
  return (
    <>
      <SafeAreaView style={[styles.screen, style]}>{children}</SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screen: {
    paddingTop: Constants.statusBarHeight,
    flex: 1,
    alignItems: "center",
    width: Math.min(width, MAX_WIDTH),
    height: height,
    backgroundColor: colors.white,
  },
});

export default Div;
