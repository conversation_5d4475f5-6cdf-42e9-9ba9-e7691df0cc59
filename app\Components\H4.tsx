import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TextStyle,
  StyleProp,
} from "react-native";
import React from "react";
import { CSSProperties } from "react";
import { fonts } from "../Config/Fonts";
import { colors } from "../Config/colors";
const { width, height } = Dimensions.get("window");

interface PProps {
  children: any;
  style?: StyleProp<TextStyle> | CSSProperties;
}

const baseWidth = 360;
const baseHeight = 800;
export default function H4({ children, style }: PProps) {
  return (
    <Text
      style={[
        {
          fontFamily: fonts.plusJMedium,
          fontSize: 24,
          color: colors.textBlack,
          letterSpacing: 0,
        },
        // @ts-ignore
        style,
      ]}
    >
      {children}
    </Text>
  );
}

const styles = StyleSheet.create({});
