import React from 'react';
import { Modal, View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '../Config/colors';
import { fonts } from '../Config/Fonts';
import H4 from './H4';
import P from './P';

interface SessionExpiredModalProps {
  visible: boolean;
  onClose: () => void;
}

const SessionExpiredModal = ({ visible, onClose }: SessionExpiredModalProps) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <H4 style={styles.title}>Session Expired</H4>
          <P style={styles.message}>
            Your session has expired for security reasons. Please log in again to continue.
          </P>
          <TouchableOpacity
            style={styles.button}
            onPress={onClose}
          >
            <Text style={styles.buttonText}>Log In</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '85%',
  },
  title: {
    marginBottom: 16,
    textAlign: 'center',
    fontFamily: fonts.plusJSemibold,
    fontSize: 20,
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
    color: colors.textAsh,
    lineHeight: 22,
  },
  button: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    width: '100%',
    alignItems: 'center',
  },
  buttonText: {
    color: colors.textBlack,
    fontFamily: fonts.plusJSemibold,
    fontSize: 16,
  },
});

export default SessionExpiredModal;
