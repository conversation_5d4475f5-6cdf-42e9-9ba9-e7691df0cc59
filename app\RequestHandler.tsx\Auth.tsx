import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "./Request";

const request = new RequestHandler();

// Function to get auth token from AsyncStorage
export async function getAuthToken(): Promise<string> {
  try {
    const cookiesString = await AsyncStorage.getItem("cookies");
    if (cookiesString) {
      const cookies = JSON.parse(cookiesString);
      if (
        (cookies && cookies.data && cookies.data.token) ||
        (cookies && cookies.token)
      ) {
        return cookies.data ? cookies.data.token : cookies.token;
      }
    }
    return "";
  } catch (error) {
    console.error("Error getting auth token:", error);
    return "";
  }
}

export function Login(body: object): Promise<any> {
  return request.post("api/v1/auth/login", body, "");
}

export function CreateUser(body: object): Promise<any> {
  return request.post("api/v1/auth/onboarding/start", body, "");
}

export function ResendOtp(body: object): Promise<any> {
  return request.post("api/v1/auth/onboarding/resend-otp", body, "");
}
export function VerifyOtp(body: object): Promise<any> {
  return request.post("api/v1/auth/onboarding/verify-otp", body, "");
}
export async function NGKyc(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("api/v1/auth/onboarding/kyc/verify", body, token);
}
export async function NGDocumentUpload(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("api/v1/auth/onboarding/documents/upload", body, token);
}

export function ValidateGoogleToken(body: object): Promise<any> {
  return request.post("user/google/auth", body, "");
}

export function ForgotPassword(body: object): Promise<any> {
  return request.post(
    "api/v1/auth/onboarding/forgot-password/request",
    body,
    ""
  );
}
export function ForgotPasswordResendOtp(body: object): Promise<any> {
  return request.post(
    "api/v1/auth/onboarding/forgot-password/resend-otp",
    body,
    ""
  );
}
export function ForgotPasswordVerifyOtp(body: object): Promise<any> {
  return request.post(
    "api/v1/auth/onboarding/forgot-password/verify-otp",
    body,
    ""
  );
}
export function ForgotPasswordResetPassword(body: object): Promise<any> {
  return request.post("api/v1/auth/onboarding/reset-password", body, "");
}
export async function CreatePin(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("api/v1/auth/onboarding/pin/create", body, token);
}
export async function ResumeKYC(): Promise<any> {
  const token = await getAuthToken();
  return request.post("api/v1/auth/onboarding/kyc/foreign/resume", {}, token);
}
export async function ForeignersKYC(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("api/v1/auth/onboarding/kyc/foreign/launch", body, token);
}
export async function CheckKYCStatus(): Promise<any> {
  const token = await getAuthToken();
  return request.get("api/v1/auth/onboarding/kyc/foreign/status", token);
}
export function GetUserProfile(token: string): Promise<any> {
  return request.get("user/profile", token);
}

export function UpdateProfile(body: object, token: string): Promise<any> {
  return request.put("user/profile", body, token);
}

export function LogoutUser(token: string): Promise<any> {
  return request.post("user/logout", {}, token);
}
