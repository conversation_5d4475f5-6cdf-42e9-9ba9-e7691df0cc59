import AsyncStorage from '@react-native-async-storage/async-storage';
import { StoredCredentials } from '../Context/CredentailsContext';

/**
 * Parses a JWT token and extracts the payload
 *
 * @param token - The JWT token to parse
 * @returns The decoded payload or null if the token is invalid
 */
export const parseJwt = (token: string): any | null => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error parsing JWT token:', error);
    return null;
  }
};

/**
 * Checks if the user's login has expired based on JWT token expiration
 *
 * @returns A promise that resolves to a boolean indicating if the login has expired
 */
export const isLoginExpired = async (): Promise<boolean> => {
  try {
    const cookiesString = await AsyncStorage.getItem('cookies');
    if (!cookiesString) {
      return true; // No login data found
    }

    const cookies: StoredCredentials = JSON.parse(cookiesString);

    // Get the token from the cookies
    const token = cookies.data?.token || cookies.token;

    if (!token) {
      return true; // No token found
    }

    // Parse the JWT token
    const decodedToken = parseJwt(token);

    if (!decodedToken || !decodedToken.exp) {
      // If we can't decode the token or it doesn't have an expiration,
      // consider the token as valid (this should rarely happen with proper JWT tokens)
      return false;
    }

    // Check if the token has expired
    const currentTime = Math.floor(Date.now() / 1000); // Convert to seconds
    return decodedToken.exp < currentTime;
  } catch (error) {
    console.error('Error checking login expiry:', error);
    return true; // In case of error, consider login as expired for security
  }
};


