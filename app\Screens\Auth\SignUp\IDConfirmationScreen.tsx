import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  View,
  Image,
  ActivityIndicator,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";

const { width, height } = Dimensions.get("window");

// Mock API call to get NIN details
const fetchNINDetails = async () => {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 1500));

  // Mock data
  return {
    name: "BLESSING AJUNWA",
    phoneNumber: "+234 ************",
    ninNumber: "91045783926662",
    // In a real app, you'd get this from the API
    photoUrl: "https://randomuser.me/api/portraits/women/44.jpg",
  };
};

export default function IDConfirmationScreen({ navigation, route }) {
  const { selfieImage } = route.params || {};
  const { data, idInfo } = route.params || {};
  const [ninDetails, setNinDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [kycData, setKycData] = useState<any>([]);

  useEffect(() => {
    const getNINDetails = async () => {
      try {
        const details = await fetchNINDetails();
        setNinDetails(details);
      } catch (error) {
        console.error("Error fetching NIN details:", error);
      } finally {
        setLoading(false);
      }
    };
    getNINDetails();
  }, []);
  const handleConfirm = () => {
    // Navigate to next screen in the flow
    navigation.navigate("DocumentUploadScreen1");
  };
  const handleDeny = () => {
    navigation.goBack();
  };
  useEffect(() => {
    if (data) {
      setKycData(data?.data?.identity);
    }
  }, []);

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={3}
            totalPages={6}
            onBack={() => navigation.pop()}
          />

          <View style={styles.contentContainer}>
            <H4 style={styles.mainTitle}>Is this NIN yours?</H4>
            <P style={styles.subtitle}>
              Confirm the NIN details shown below belong you
            </P>

            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <P style={styles.loadingText}>Fetching your details...</P>
              </View>
            ) : (
              <View style={styles.detailsCard}>
                <Image
                  source={{
                    uri: kycData?.photo
                      ? kycData.photo.startsWith("data:")
                        ? kycData.photo
                        : `data:image/jpeg;base64,${kycData.photo}`
                      : kycData?.image
                      ? kycData?.image.startsWith("data:")
                        ? kycData.image
                        : `data:image/jpeg;base64,${kycData.image}`
                      : null,
                  }}
                  style={styles.profileImage}
                />
                <P style={styles.nameText}>
                  {kycData?.first_name?.toUpperCase()}{" "}
                  {kycData?.last_name?.toUpperCase()}
                </P>
                <P style={styles.phoneText}>{kycData?.phone_number}</P>
                {/* <View style={styles.divider} /> */}
                <P style={styles.ninLabel}>
                  {idInfo?.idType === "bvn"
                    ? "Bank Verification Number (BVN)"
                    : "National Identification Number (NIN)"}
                </P>
                <P style={styles.ninNumber}>{idInfo?.idNumber}</P>
              </View>
            )}
            <View style={styles.buttonsContainer}>
              <Button
                btnText="No, it is not"
                type="alt"
                onPress={handleDeny}
                style={styles.denyButton}
              />
              <Button
                btnText="Yes, it is"
                onPress={handleConfirm}
                style={styles.confirmButton}
              />
            </View>
          </View>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textAsh,
  },
  detailsCard: {
    backgroundColor: "#FFF9EA",
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
    width: "100%",
  },
  profileImage: {
    width: 114,
    height: 114,
    borderRadius: 4,
    marginBottom: 16,
  },
  nameText: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 4,
  },
  phoneText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 12,
    color: colors.textAsh,
    marginBottom: 16,
  },
  divider: {
    height: 1,
    backgroundColor: "#E5E5E5",
    width: "100%",
    marginBottom: 16,
  },
  ninLabel: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: colors.textAsh,
    marginBottom: 4,
  },
  ninNumber: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
    color: colors.textBlack,
  },
  buttonsContainer: {
    width: "100%",
    marginTop: "auto",
    bottom: (10 * height) / 100,
  },
  denyButton: {
    marginBottom: 12,
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: colors.primary,
  },
  confirmButton: {
    backgroundColor: colors.primary,
  },
});
