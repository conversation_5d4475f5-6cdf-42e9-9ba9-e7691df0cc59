import React from "react";
import { View, StyleSheet, TouchableOpacity, ScrollView } from "react-native";
import Header from "../Components/Header";
import P from "../Components/P";
import Div from "../Components/Div";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import { useRoute } from "@react-navigation/native";

const TransactionDetailsScreen: React.FC = () => {
  // Placeholder: In a real app, get transaction data from route params
  // const route = useRoute();
  // const { transaction } = route.params;
  const transaction = {
    receiver: "Getly",
    amount: "NGN 5,000",
    debited: "USD",
    fee: "$0.5",
    finalAmount: "$4.5",
    type: "Wallet",
    time: "17 May 2025, 11:03AM WAT",
    reference: "tran44frg55667554gdhdghkkj8761",
    sessionId: "09087653453534113314231432",
  };

  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={{ paddingBottom: 32 }}>
        <Header title="Transaction Details" />
        <View style={styles.card}>
          <View style={styles.rowBetween}>
            <P style={styles.label}>Receiver</P>
            <P style={styles.value}>{transaction.receiver}</P>
          </View>
          <View style={styles.rowBetween}>
            <P style={styles.label}>Amount</P>
            <P style={styles.value}>{transaction.amount}</P>
          </View>
          <View style={styles.dashedLine} />
          <View style={styles.rowBetween}>
            <P style={styles.label}>Debited</P>
            <P style={styles.value}>{transaction.debited}</P>
          </View>
          <View style={styles.rowBetween}>
            <P style={styles.label}>Fee</P>
            <P style={styles.value}>{transaction.fee}</P>
          </View>
          <View style={styles.rowBetween}>
            <P style={styles.label}>Amount</P>
            <P style={styles.value}>{transaction.finalAmount}</P>
          </View>
        </View>
        <View style={styles.card}>
          <P style={styles.detailsTitle}>Details</P>
          <View style={styles.dashedLine} />
          <View style={styles.rowBetween}>
            <P style={styles.label}>Type</P>
            <P style={styles.value}>{transaction.type}</P>
          </View>
          <View style={styles.rowBetween}>
            <P style={styles.label}>Time</P>
            <P style={styles.value}>{transaction.time}</P>
          </View>
          <View style={styles.rowBetween}>
            <P style={styles.label}>Reference</P>
            <P style={styles.value}>{transaction.reference}</P>
          </View>
          <View style={styles.rowBetween}>
            <P style={styles.label}>Session ID</P>
            <P style={styles.value}>{transaction.sessionId}</P>
          </View>
        </View>
        <TouchableOpacity style={styles.actionBtn}>
          <P style={styles.actionBtnText}>Download Receipt</P>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.actionBtn, styles.reportBtn]}>
          <P style={[styles.actionBtnText, styles.reportBtnText]}>Report Transaction</P>
        </TouchableOpacity>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#FAFAFA",
    borderRadius: 12,
    marginHorizontal: 20,
    marginTop: 24,
    padding: 20,
    elevation: 1,
  },
  rowBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  label: {
    fontFamily: fonts.plusJRegular,
    fontSize: 15,
    color: "#888",
  },
  value: {
    fontFamily: fonts.plusJMedium,
    fontSize: 15,
    color: colors.textBlack,
  },
  dashedLine: {
    borderBottomWidth: 1,
    borderStyle: "dashed",
    borderColor: "#E0E0E0",
    marginVertical: 10,
  },
  detailsTitle: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 15,
    color: colors.textBlack,
    marginBottom: 8,
  },
  actionBtn: {
    marginHorizontal: 20,
    marginTop: 24,
    backgroundColor: "#FFF7E6",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#FBC94A",
    alignItems: "center",
    paddingVertical: 16,
  },
  actionBtnText: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
  },
  reportBtn: {
    backgroundColor: colors.white,
    marginTop: 12,
  },
  reportBtnText: {
    color: "#FBC94A",
  },
});

export default TransactionDetailsScreen; 