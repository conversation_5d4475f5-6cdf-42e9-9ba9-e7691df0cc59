import React, { useState, useRef, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  ScrollView,
  ActivityIndicator,
  BackHandler,
  Alert,
} from "react-native";
import { colors } from "../../../../Config/colors";
import Div from "../../../../Components/Div";
import H4 from "../../../../Components/H4";
import P from "../../../../Components/P";
import { fonts } from "../../../../Config/Fonts";
import Icon from "react-native-vector-icons/Feather";
import { Camera, CameraView } from "expo-camera";
import PageHeader from "../../../../Components/PageHeader";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../../Config/Svg";
import {
  ForeignersKYC,
  ResumeKYC,
  CheckKYCStatus,
  getAuthToken,
} from "../../../../RequestHandler.tsx/Auth";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Button from "../../../../Components/Button";
import { WebView } from "react-native-webview";
import { useUpdateNextStep } from "../../../../utils/updateNextStep";
import { useToast } from "../../../../Context/ToastContext";

const { width, height } = Dimensions.get("window");

export default function ForeignersKYCScreen({ navigation }) {
  const [loading, setLoading] = useState(false);
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [kycUrl, setKycUrl] = useState("");
  const [showWebView, setShowWebView] = useState(false);
  const [kycCompleted, setKycCompleted] = useState(false);
  const [callbackDetected, setCallbackDetected] = useState(false);
  const webViewRef = useRef(null);
  const { updateUserNextStep } = useUpdateNextStep();
  const { handleToast } = useToast();

  const decodeJwt = (token: string): string | undefined => {
    if (token) {
      try {
        const payload = token.split(".")[1];
        const decodedPayload = JSON.parse(atob(payload));
        return decodedPayload.id;
      } catch (error) {
        console.error("Error decoding JWT:", error);
        return undefined;
      }
    }
    return undefined;
  };
  const getUserId = async () => {
    try {
      const res = await AsyncStorage.getItem("cookies");
      if (res !== null && typeof res === "string") {
        const loginOBject = JSON.parse(res);
        const token = loginOBject.data
          ? loginOBject.data.token
          : loginOBject.token;
        return decodeJwt(token);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // const resumeKYC = async () => {
  //   setLoading(true);
  //   try {
  //     const res = await ResumeKYC();
  //     // Check if the response contains a URL
  //     if (res && res.data && res.data.url) {
  //       setKycUrl(res.data.url);
  //       setShowWebView(true);
  //       // Update the nextStep in AsyncStorage if it's provided in the response
  //       if (res.nextStep) {
  //         updateUserNextStep(res.nextStep);
  //       }
  //     } else {
  //       // If no URL in response, try the foreignersKYC endpoint
  //       foreignersKYC();
  //     }
  //   } catch (error) {
  //     if (error.isNetworkError) {
  //       setIsNetworkError(true);
  //     } else {
  //       foreignersKYC();
  //     }
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  useEffect(() => {
    foreignersKYC();
  }, []);

  const foreignersKYC = async () => {
    setLoading(true);
    const body = {
      data: {
        attributes: {
          "inquiry-template-id": "itmpl_ZWaVGxhsKr74Yxdbw1mssjwjSUkm",
        },
      },
      meta: {
        "auto-create-account": true,
        "auto-create-account-reference-id": await getUserId(),
      },
    };
    try {
      const res = await ForeignersKYC(body);
      console.log(res);
      // Check if the response contains a URL
      if (res && res.data && res.data.url) {
        setKycUrl(res.data.url);
        setShowWebView(true);

        // Update the nextStep in AsyncStorage if it's provided in the response
        if (res.nextStep) {
          updateUserNextStep(res.nextStep);
        }
      }
    } catch (error) {
      console.log("hhhh", error);
      if (error.isNetworkError) {
        setIsNetworkError(true);
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to check KYC status
  const checkKycStatus = async () => {
    setLoading(true);

    try {
      // Make the API call to check KYC status using the existing function
      const data = await CheckKYCStatus();
      console.log("KYC status check response:", data);
      // Check if KYC is completed successfully
      if (
        data.nextStep === "/auth/onboarding/pin/create"
      ) {
        setKycCompleted(true);
        setShowWebView(false); // Hide WebView to prevent further navigation
        // Update next step in AsyncStorage
        updateUserNextStep("/auth/onboarding/pin/create");
        // Show success message
        handleToast(data.message || "KYC completed successfully", "success");
        // Navigate to the next screen after a short delay to ensure state updates
        setTimeout(() => {
          navigation.navigate("TransactionPinScreen");
        }, 500);

        return true; // Return true to indicate success
      } else {
        // If KYC is not yet completed but we got a callback, we might need to wait
        console.log("KYC verification in progress. Waiting for completion...");

        // If we're at the callback URL but KYC isn't complete, we might need to check again
        if (callbackDetected) {
          // Try again after a short delay
          setTimeout(() => {
            checkKycStatus();
          }, 3000);
        }
      }

      return false; // Return false to indicate KYC not yet completed
    } catch (error) {
      handleToast(error.message || "Failed to verify KYC status", "error");
      console.error("Error checking KYC status:", error);
      navigation.pop();
      return false;
    } finally {
      setLoading(false);
    }
  };
  // Handle back button press in WebView
  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      () => {
        if (showWebView && webViewRef.current) {
          webViewRef.current.goBack();
          return true;
        }
        return false;
      }
    );

    return () => backHandler.remove();
  }, [showWebView]);

  // Reset callback detection when WebView URL changes
  useEffect(() => {
    if (showWebView && kycUrl) {
      setCallbackDetected(false);
    }
  }, [kycUrl]);

  return (
    <View style={styles.mainContainer}>
      <Div>
        {loading ? (
          <View
            style={{ flex: 1, alignItems: "center", justifyContent: "center" }}
          >
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : isNetworkError ? (
          <View
            style={{
              flex: 1,
              alignItems: "center",
              justifyContent: "center",
              width: "80%",
            }}
          >
            <SvgXml xml={svg.conE} />

            <P
              style={{
                marginTop: 24,
                fontSize: 18,
                fontFamily: fonts.plusJRegular,
              }}
            >
              Connection timed out
            </P>
            <P style={{ fontSize: 14, textAlign: "center", marginTop: 8 }}>
              The request is taking too long to complete. This could be due to a
              slow internet connection or our servers might be busy. Please try
              again."
            </P>
            <View style={{ width: "100%" }}>
              <Button
                btnText="Retry"
                btnTextStyle={{ fontFamily: fonts.plusJRegular }}
                onPress={foreignersKYC}
                style={{ marginTop: 32 }}
              />
            </View>
          </View>
        ) : showWebView && kycUrl && !kycCompleted ? (
          <View style={styles.webViewContainer}>
            <WebView
              ref={webViewRef}
              source={{ uri: kycUrl }}
              style={styles.webView}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={true}
              onError={(syntheticEvent) => {
                const { nativeEvent } = syntheticEvent;
                console.error('WebView error:', nativeEvent);
              }}
              onNavigationStateChange={(navState) => {
                // Check if the URL contains the callback URL
                console.log("url", navState.url);
                if (
                  !callbackDetected &&
                  navState.url.includes(
                    "https://getly-api.onrender.com/api/v1/auth/onboarding/kyc/foreign/callback?"
                  )
                ) {
                  console.log("Detected callback URL:", navState.url);
                  setCallbackDetected(true);
                  // Stop the WebView from loading further
                  webViewRef.current?.stopLoading();
                  // Hide WebView to prevent further navigation attempts
                  setShowWebView(false);
                  // Check KYC status immediately
                  setTimeout(() => {
                    checkKycStatus();
                  }, 500);
                }
              }}
              renderLoading={() => (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={colors.primary} />
                </View>
              )}
            />
          </View>
        ) : kycCompleted ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <P style={{ marginTop: 20, textAlign: 'center' }}>
              KYC verification completed successfully. Redirecting...
            </P>
          </View>
        ) : (
          <View style={styles.emptyContainer}></View>
        )}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  webViewContainer: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  webView: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  loadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 16,
    color: colors.textAsh,
  },
});
