import React from "react";
import {
  Dimensions,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";

const { height } = Dimensions.get("window");

function VerificationSuccessScreen({ navigation }) {
  return (
    <View>
      <Div>
        <View style={styles.cont}>
          <View
            style={{
              width: "100%",
              alignItems: "center",
              marginTop: (20 * height) / 100,
            }}
          >
            <Image
              source={require("../../../assets/Done.png")}
              style={{ objectFit: "contain", width: 200, height: 200 }}
            />
            <H4>Successful!</H4>
            <P style={{ marginTop: 8, textAlign: "center" }}>
              Your email address has been{"\n"}verified
            </P>

            <TouchableOpacity
              style={{
                paddingVertical: 12,
                paddingHorizontal: 38,
                borderRadius: 8,
                borderWidth: 1,
                borderColor: colors.primary,
                marginTop: 32,
              }}
              onPress={() => {
                // navigation.navigate("IDInformationScreen");
                navigation.navigate("CountrySelectionScreen");
              }}
            >
              <P style={{ fontFamily: fonts.plusJBold }}>Continue</P>
            </TouchableOpacity>
          </View>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  cont: {
    width: "100%",
    height,
    alignItems: "center",
    // justifyContent: "center",
    backgroundColor: "#FFFFFF",
  },
});

export default VerificationSuccessScreen;
