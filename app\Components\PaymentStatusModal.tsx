import React from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  Modal,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import Button from "./Button";
import P from "./P";
import H4 from "./H4";

const { width, height } = Dimensions.get("window");

export type PaymentStatus = "processing" | "success" | "failed";

interface PaymentStatusModalProps {
  visible: boolean;
  status: PaymentStatus;
  errorMessage?: string;
  onButtonPress: () => void;
  onClose?: () => void;
}

const PaymentStatusModal: React.FC<PaymentStatusModalProps> = ({
  visible,
  status,
  errorMessage = "Insufficient Wallet Balance",
  onButtonPress,
  onClose,
}) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {status === "processing" && (
            <>
              <View style={styles.iconContainer}>
                <ActivityIndicator
                  size={60}
                  color={colors.primary}
                  style={{ width: 100, height: 100 }}
                />
              </View>
              <H4 style={styles.statusTitle}>Payment Processing</H4>
            </>
          )}

          {status === "success" && (
            <>
              <View style={styles.iconContainer}>
                <Image
                  source={require("../assets/Done.png")}
                  style={{ width: 150, height: 150 }}
                />
              </View>
              <H4 style={styles.statusTitle}>Payment Successful</H4>
              <P style={styles.statusMessage}>
                Proceed to to select your pick-up location
              </P>
              <View style={styles.buttonContainer}>
                <Button
                  btnText="Select Pick-up Location →"
                  onPress={onButtonPress}
                />
              </View>
            </>
          )}

          {status === "failed" && (
            <>
              <View style={styles.iconContainer}>
                <Image
                  source={require("../assets/Error.png")}
                  style={{ width: 150, height: 150 }}
                />
              </View>
              <H4 style={styles.statusTitle}>Payment Failed</H4>
              <P style={styles.statusMessage}>{errorMessage}</P>
              <View style={styles.buttonContainer}>
                <Button
                  btnText="Continue"
                  onPress={onButtonPress}
                />
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  iconContainer: {
    marginBottom: 24,
    width: 100,
    height: 100,
    justifyContent: "center",
    alignItems: "center",
  },
  // Processing icon styles
  processingIconContainer: {
    width: 80,
    height: 80,
    position: "relative",
  },
  spinnerDot: {
    position: "absolute",
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#FDBE33",
  },
  spinnerDot1: {
    top: 0,
    left: 35,
    backgroundColor: "#FDBE33",
  },
  spinnerDot2: {
    top: 15,
    right: 10,
    backgroundColor: "#FDBE33",
    opacity: 0.8,
  },
  spinnerDot3: {
    bottom: 15,
    right: 10,
    backgroundColor: "#FDBE33",
    opacity: 0.6,
  },
  spinnerDot4: {
    bottom: 0,
    left: 35,
    backgroundColor: "#FDBE33",
    opacity: 0.4,
  },
  spinnerDot5: {
    bottom: 15,
    left: 10,
    backgroundColor: "#FDBE33",
    opacity: 0.2,
  },
  spinnerDot6: {
    top: 15,
    left: 10,
    backgroundColor: "#FDBE33",
    opacity: 0.1,
  },

  // Success icon styles
  successIconContainer: {
    width: 80,
    height: 80,
    position: "relative",
  },
  successCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: "#10B981",
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    top: 10,
    left: 10,
  },
  checkmark: {
    width: 30,
    height: 15,
    borderLeftWidth: 2,
    borderBottomWidth: 2,
    borderColor: "#10B981",
    transform: [{ rotate: "-45deg" }],
    marginTop: -5,
  },
  successStar1: {
    position: "absolute",
    top: 0,
    right: 10,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#D1FAE5",
  },
  successStar2: {
    position: "absolute",
    bottom: 5,
    left: 5,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#D1FAE5",
  },

  // Failed icon styles
  failedIconContainer: {
    width: 80,
    height: 80,
    position: "relative",
  },
  failedCloud: {
    width: 60,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#FEF2F2",
    borderWidth: 2,
    borderColor: "#EF4444",
    position: "absolute",
    top: 20,
    left: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  failedFace: {
    width: 30,
    height: 20,
    position: "relative",
  },
  failedEye1: {
    position: "absolute",
    top: 5,
    left: 5,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#EF4444",
  },
  failedEye2: {
    position: "absolute",
    top: 5,
    right: 5,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#EF4444",
  },
  failedMouth: {
    position: "absolute",
    bottom: 2,
    left: 10,
    width: 10,
    height: 5,
    borderBottomWidth: 2,
    borderBottomColor: "#EF4444",
    borderRadius: 5,
  },
  failedX1: {
    position: "absolute",
    top: 5,
    right: 5,
    width: 10,
    height: 2,
    backgroundColor: "#EF4444",
    transform: [{ rotate: "45deg" }],
  },
  failedX2: {
    position: "absolute",
    top: 5,
    right: 5,
    width: 10,
    height: 2,
    backgroundColor: "#EF4444",
    transform: [{ rotate: "-45deg" }],
  },
  failedDot1: {
    position: "absolute",
    bottom: 10,
    right: 15,
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: "#FEE2E2",
  },
  failedDot2: {
    position: "absolute",
    top: 15,
    left: 5,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#FEE2E2",
  },

  statusTitle: {
    fontSize: 18,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
    marginBottom: 12,
    textAlign: "center",
  },
  statusMessage: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    textAlign: "center",
    marginBottom: 32,
  },
  buttonContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
});

export default PaymentStatusModal;
