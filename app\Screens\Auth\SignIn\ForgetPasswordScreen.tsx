import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Keyboard,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";
import Input from "../../../Components/Input";
import { ForgotPassword } from "../../../RequestHandler.tsx/Auth";
import { useToast } from "../../../Context/ToastContext";

const { height } = Dimensions.get("window");

export default function ForgotPasswordScreen({ navigation }) {
  const [email, setEmail] = useState("");
  const [isFormFilled, setIsFormFilled] = useState(false);
  const [loading, setLoading] = useState(false);
  const [emailError, setEmailError] = useState("");
  const { handleToast } = useToast();

  const handleInputChange = (text: string) => {
    setEmail(text);
    setEmailError("");

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (text && emailRegex.test(text)) {
      setIsFormFilled(true);
    } else {
      setIsFormFilled(false);
    }
  };

  const handleSubmit = async () => {
    Keyboard.dismiss();
    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email address");
      return;
    }
    setLoading(true);
    try {
      const body = { email };
      const res = await ForgotPassword(body);
      console.log(res);
      if (res.message) {
        navigation.navigate("ForgotPasswordEmailVerifyScreen", { email });
      } else {
        handleToast("User not found", "error");
      }
    } catch (error) {
      console.log(error);
      handleToast(error.message || "Failed to send verification code", "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader onBack={() => navigation.pop()} />
          <ScrollView
            automaticallyAdjustContentInsets={true}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              alignItems: "center",
              minHeight: "90%",
            }}
          >
            <View style={styles.contentContainer}>
              <H4 style={styles.mainTitle}>Forgot password?</H4>
              <P style={styles.subtitle}>
                Enter the email address you registered with, you’ll receive an
                authentication code now
              </P>

              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Input
                    label={"Email address"}
                    placeholder="<EMAIL>"
                    value={email}
                    onChangeText={handleInputChange}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    errorText={emailError}
                  />
                </View>
              </View>
            </View>
            {isFormFilled ? (
              <View style={{ width: "90%", marginBottom: (5 * height) / 100 }}>
                <Button
                  btnText="Submit"
                  onPress={handleSubmit}
                  loading={loading}
                />
              </View>
            ) : (
              <View style={styles.footerContainer}>
                <View style={styles.loginContainer}>
                  <Text style={styles.accountText}>Don’t have an account?</Text>
                  <TouchableOpacity
                    onPress={() => {
                      navigation.navigate("SignUpScreen1");
                    }}
                  >
                    <Text style={styles.loginText}>Register</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  formContainer: {
    width: "100%",
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#E5E5E5",
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
  },
  filledInput: {
    borderColor: colors.primary,
    borderWidth: 1,
  },
  footerContainer: {
    width: "100%",
    alignItems: "center",
  },
  loginContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 45,
  },
  accountText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: colors.textAsh,
  },
  loginText: {
    fontFamily: fonts.plusJBold,
    fontSize: 14,
    color: colors.black,
    marginLeft: 5,
  },
});
