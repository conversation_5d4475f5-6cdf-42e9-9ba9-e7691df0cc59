import React from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
  SafeAreaView,
  Dimensions,
  TouchableOpacity,
  ImageBackground,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import Header from "../../Components/Header";
import P from "../../Components/P";
import H4 from "../../Components/H4";

const { width, height } = Dimensions.get("window");

interface CardActivationScreenProps {
  navigation: any;
  route?: {
    params?: {
      cardType?: string;
      location?: string;
      activationId?: string;
    };
  };
}

export default function CardActivationScreen({
  navigation,
  route,
}: CardActivationScreenProps) {
  const cardType = route?.params?.cardType || "ngn";
  const location =
    route?.params?.location || "Nnamdi Azikwe International Airport, Abuja";

  // Handle activate card
  const handleActivateCard = () => {
    // This would typically involve an API call
    // For demo purposes, navigate back to card screen
    navigation.navigate("ActivateCardScreen");
  };

  // Handle change location
  const handleChangeLocation = () => {
    navigation.goBack(); // Go back to pickup location screen
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header
          title="Cards"
          contStyle={{ justifyContent: "flex-start", gap: 8 }}
          showNotification={false}
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          style={{ width: "95%" }}
        >
          {/* Card Preview */}
          <View style={styles.cardPreviewContainer}>
            <View style={styles.cardPreview}>
              <ImageBackground
                source={require("../../assets/bg-background.png")}
                style={{
                  width: "60%",
                  height: "110%",
                  position: "absolute",
                  right: -90,
                }}
                resizeMode="cover"
              ></ImageBackground>
              <View style={styles.cardHeader}>
                <View style={styles.flagContainer}>
                  <Image
                    source={{
                      uri: `https://flagcdn.com/w2560/ng.png`,
                    }}
                    style={styles.flagIcon}
                  />
                </View>
                <Text style={styles.cardBalance}>NGN 0.00</Text>
              </View>

              <Text style={styles.cardNumber}>1234 **** **** 1234</Text>
              <Text style={styles.cardExpiry}>VALID TILL 08/28</Text>
              <Text style={styles.cardHolder}>Anita Amadi</Text>

              <View style={styles.cardBrand}>
                <Image
                  source={require("../../assets/verve.png")}
                  style={{ width: 58, height: 35 }}
                />
              </View>
            </View>
          </View>

          {/* Pickup Instructions */}
          <View style={styles.pickupInstructions}>
            <P style={styles.pickupText}>
              Pick up and activate your card at the Getly store within your
              selected location below:
            </P>
          </View>

          {/* Selected Location */}
          <View style={styles.selectedLocationContainer}>
            <P style={styles.selectedLocationText}>{location}</P>
          </View>

          {/* Activate Button */}
          <View style={styles.activateButtonContainer}>
            <Button btnText="Activate Your Card" onPress={handleActivateCard} />
          </View>

          {/* Change Location Link */}
          <View style={styles.changeLocationContainer}>
            <P style={styles.changeLocationText}>
              Change your card pick-up location?
            </P>
            <TouchableOpacity onPress={handleChangeLocation}>
              <P style={styles.changeLocationLink}>Change Location</P>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  cardPreviewContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 30,
  },
  cardPreview: {
    width: "100%",
    minHeight: 215,
    backgroundColor: "#E6E6F2",
    borderRadius: 12,
    padding: 16,
    position: "relative",
    overflow: "hidden",
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  flagContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    overflow: "hidden",
  },
  flagIcon: {
    width: 30,
    height: 30,
    objectFit: "fill",
  },
  cardBalance: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardNumber: {
    fontSize: 24,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
    marginBottom: 8,
  },
  cardExpiry: {
    fontSize: 12,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    marginBottom: 8,
  },
  cardHolder: {
    fontSize: 14,
    fontFamily: fonts.plusJBold,
    color: colors.textBlack,
  },
  cardBrand: {
    position: "absolute",
    bottom: 16,
    right: 16,
  },
  cardBrandText: {
    color: colors.white,
    fontSize: 12,
    fontFamily: fonts.plusJMedium,
  },
  pickupInstructions: {
    marginBottom: 20,
  },
  pickupText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    textAlign: "center",
    lineHeight: 24,
    paddingLeft: 10,
    paddingRight: 10,
  },
  selectedLocationContainer: {
    marginBottom: 30,
  },
  selectedLocationText: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    textAlign: "center",
  },
  activateButtonContainer: {
    marginBottom: 20,
  },
  changeLocationContainer: {
    alignItems: "center",
  },
  changeLocationText: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    marginBottom: 4,
  },
  changeLocationLink: {
    fontSize: 12,
    fontFamily: fonts.plusJSemibold,
    color: colors.deepPrimary,
  },
});
