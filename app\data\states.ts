import { State as CSCState } from 'country-state-city';

export interface State {
  label: string;
  value: string;
  countryCode: string;
  isoCode: string;
}

// Function to get states by country code using country-state-city library
export const getStatesByCountry = (countryCode: string): State[] => {
  try {
    const states = CSCState.getStatesOfCountry(countryCode);
    return states.map(state => ({
      label: state.name,
      value: state.isoCode,
      countryCode: state.countryCode,
      isoCode: state.isoCode
    })).sort((a, b) => a.label.localeCompare(b.label));
  } catch (error) {
    console.error('Error fetching states for country:', countryCode, error);
    return [];
  }
};

// Function to get all states (if needed)
export const getAllStates = (): State[] => {
  try {
    const allStates = CSCState.getAllStates();
    return allStates.map(state => ({
      label: state.name,
      value: state.isoCode,
      countryCode: state.countryCode,
      isoCode: state.isoCode
    })).sort((a, b) => a.label.localeCompare(b.label));
  } catch (error) {
    console.error('Error fetching all states:', error);
    return [];
  }
};

// Function to get state by value and country
export const getStateByValue = (value: string, countryCode: string): State | undefined => {
  const states = getStatesByCountry(countryCode);
  return states.find(state => state.value === value);
};

// Function to get state by name and country
export const getStateByName = (name: string, countryCode: string): State | undefined => {
  const states = getStatesByCountry(countryCode);
  return states.find(state => state.label.toLowerCase() === name.toLowerCase());
};
