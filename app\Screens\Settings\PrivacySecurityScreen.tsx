import React from "react";
import { View, StyleSheet, TouchableOpacity, ScrollView } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import { useNavigation } from "@react-navigation/native";

const PrivacySecurityScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  const handleNavigate = (screen: string) => () => navigation.navigate(screen);

  const options = [
    {
      label: "Change password",
      icon: svg.passwordCheck, // TODO: Replace with password SVG
      onPress: handleNavigate("ChangePasswordScreen"),
    },
    {
      label: "Change transaction PIN",
      icon: svg.key, // TODO: Replace with key SVG
      onPress: handleNavigate("ChangeTransactionPINScreen"),
    },
    {
      label: "Two-Factor authentication",
      icon: svg.shield, // TODO: Replace with shield SVG
      onPress: handleNavigate("Security2FAScreen"),
    },
  ];

  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={{ paddingBottom: 32 }}>
        <Header title="Privacy & Security" />
        <View style={styles.list}>
          {options.map((item, idx) => (
            <TouchableOpacity
              key={item.label}
              style={styles.row}
              activeOpacity={0.7}
              onPress={item.onPress}
            >
              <SvgXml xml={item.icon} width={20} height={20} style={styles.icon} />
              <P style={styles.label}>{item.label}</P>
              <SvgXml xml={svg.chevronRight} width={22} height={22} />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  list: { marginTop: 24 },
  row: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 18,
    paddingHorizontal: 24,
  },
  icon: { marginRight: 12 },
  label: {
    flex: 1,
    fontSize: 16,
    color: colors.textBlack,
    fontFamily: fonts.plusJMedium,
  },
});

export default PrivacySecurityScreen; 