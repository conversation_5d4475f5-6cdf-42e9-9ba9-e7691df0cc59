import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Dimensions,
  TextInput,
  Image,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import P from "./P";
import Icon from "react-native-vector-icons/Feather";

interface SelectOption {
  label: string;
  value: string;
  flagUrl?: string;
}

interface SelectInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  options: SelectOption[];
  onSelect: (option: SelectOption) => void;
  error?: boolean;
  errorText?: string;
  contStyle?: any;
  disabled?: boolean;
  searchable?: boolean;
  labelStyle?: any;
  showFlags?: boolean;
}

const { width, height } = Dimensions.get("window");

const SelectInput: React.FC<SelectInputProps> = ({
  label,
  placeholder = "Select an option",
  value,
  options,
  onSelect,
  error = false,
  errorText,
  contStyle,
  disabled = false,
  searchable = true,
  labelStyle,
  showFlags = false,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Filter options based on search query
  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelect = (option: SelectOption) => {
    onSelect(option);
    setIsModalVisible(false);
    setSearchQuery("");
  };

  const selectedOption = options.find((option) => option.value === value);

  const renderOption = ({ item }: { item: SelectOption }) => (
    <TouchableOpacity
      style={styles.optionItem}
      onPress={() => handleSelect(item)}
    >
      <View style={styles.optionContent}>
        {showFlags && item.flagUrl && (
          <Image
            source={{ uri: item.flagUrl }}
            style={[
              styles.flagImage,
              { objectFit: item.value === "NG" ? "fill" : "cover" }
            ]}
          />
        )}
        <Text style={[styles.optionText, showFlags && styles.optionTextWithFlag]}>
          {item.label}
        </Text>
      </View>
      {item.value === value && (
        <Icon name="check" size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, contStyle]}>
      {label && <P style={[styles.label, labelStyle]}>{label}</P>}

      <TouchableOpacity
        style={[
          styles.selectButton,
          {
            borderColor: error
              ? colors.red
              : disabled
              ? colors.lightAsh
              : colors.stroke,
            backgroundColor: disabled ? colors.grayLight : colors.white,
          },
        ]}
        onPress={() => !disabled && setIsModalVisible(true)}
        disabled={disabled}
      >
        <View style={styles.selectButtonContent}>
          {showFlags && selectedOption?.flagUrl && (
            <Image
              source={{ uri: selectedOption.flagUrl }}
              style={[
                styles.selectedFlagImage,
                { objectFit: selectedOption.value === "NG" ? "fill" : "cover" }
              ]}
            />
          )}
          <Text
            style={[
              styles.selectButtonText,
              {
                color: selectedOption
                  ? colors.textBlack
                  : disabled
                  ? colors.textSubtle
                  : colors.lightAsh,
              },
              showFlags && selectedOption && styles.selectButtonTextWithFlag,
            ]}
          >
            {selectedOption ? selectedOption.label : placeholder}
          </Text>
        </View>
        <Icon
          name="chevron-down"
          size={20}
          color={disabled ? colors.textSubtle : colors.textAsh}
        />
      </TouchableOpacity>

      {error && errorText && <P style={styles.errorText}>{errorText}</P>}

      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
        statusBarTranslucent
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
        >
          <TouchableOpacity
            style={styles.modalContent}
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {label || "Select an option"}
              </Text>
              <TouchableOpacity
                onPress={() => setIsModalVisible(false)}
                style={styles.closeButton}
              >
                <Icon name="x" size={24} color={colors.textBlack} />
              </TouchableOpacity>
            </View>

            {searchable && (
              <View style={styles.searchContainer}>
                <Icon
                  name="search"
                  size={20}
                  color={colors.textSubtle}
                  style={styles.searchIcon}
                />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  placeholderTextColor={colors.textSubtle}
                />
              </View>
            )}

            <FlatList
              data={filteredOptions}
              renderItem={renderOption}
              keyExtractor={(item) => item.value}
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>No options found</Text>
                </View>
              }
            />
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  label: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    lineHeight: 18,
    marginBottom: 6,
    color: colors.textBlack,
  },
  selectButton: {
    width: "100%",
    height: 59,
    borderWidth: 1,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 14,
  },
  selectButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  selectButtonText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    flex: 1,
  },
  selectButtonTextWithFlag: {
    marginLeft: 12,
  },
  selectedFlagImage: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  flagImage: {
    width: 30,
    height: 30,
    borderRadius: 15,
  },
  errorText: {
    fontSize: 14,
    color: colors.red,
    fontFamily: fonts.plusJRegular,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.8,
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.grayLight,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    margin: 20,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  optionsList: {
    paddingHorizontal: 20,
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.grayLight,
  },
  optionContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  optionText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    flex: 1,
  },
  optionTextWithFlag: {
    marginLeft: 12,
  },
  emptyContainer: {
    padding: 20,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textSubtle,
  },
});

export default SelectInput;
