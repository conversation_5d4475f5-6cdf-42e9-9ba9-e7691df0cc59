import React, { useState, useEffect, useRef } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  Alert,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { Camera, CameraType } from "expo-camera";
import * as FileSystem from "expo-file-system";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";
import Icon from "react-native-vector-icons/Feather";
import { NGKyc } from "../../../RequestHandler.tsx/Auth";
import { CameraView } from "expo-camera";
import { useUpdateNextStep } from "../../../utils/updateNextStep";
import { useToast } from "../../../Context/ToastContext";

const { width, height } = Dimensions.get("window");
const CAMERA_SIZE = width * 0.9;

export default function SelfieScreen({ navigation, route }) {
  const [hasPermission, setHasPermission] = useState(null);
  const [capturedImage, setCapturedImage] = useState<{
    uri: string;
    base64?: string;
  } | null>(null);
  const [isCapturing, setIsCapturing] = useState(false);
  const camera = useRef(null);
  const { data } = route.params || {};
  const [loading, setLoading] = useState(false);
  const [facing, setFacing] = useState<CameraType>("front");
  const { updateUserNextStep } = useUpdateNextStep();
  const { handleToast } = useToast();

  // Request camera permission
  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === "granted");

      if (status !== "granted") {
        Alert.alert(
          "Camera Permission",
          "We need camera permission to take your selfie for verification.",
          [{ text: "OK" }]
        );
      }
    })();
  }, []);

  // Take picture function
  const takePicture = async () => {
    if (camera.current && !isCapturing) {
      setIsCapturing(true);
      try {
        // Take the picture with reduced quality
        const photo = await camera.current.takePictureAsync({
          quality: 0.5, // Reduce quality to 50%
          base64: false, // Don't generate base64 directly to save memory
        });

        // Convert to base64 after taking the photo with reduced quality
        const base64Data = await FileSystem.readAsStringAsync(photo.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        setCapturedImage({
          uri: photo.uri,
          base64: base64Data,
        });
      } catch (error) {
        console.log("Error taking picture:", error);
        Alert.alert("Error", "Failed to take picture. Please try again.");
      } finally {
        setIsCapturing(false);
      }
    }
  };

  const retakePicture = () => {
    setCapturedImage(null);
  };

  const verifyKYC = async () => {
    setLoading(true);
    const body = {
      id: data.idNumber,
      type: data.idType,
      country: "NGA",
      image: capturedImage.base64,
    };
    try {
      const res = await NGKyc(body);
      console.log(res);
      navigation.navigate("IDConfirmationScreen", {
        data: res,
        idInfo: data,
      });
      if (res.nextStep) {
        updateUserNextStep(res.nextStep);
      }
    } catch (error) {
      console.log(error);
      handleToast(error.message, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    // Navigate to ID confirmation screen with the captured image
    verifyKYC();
  };

  // Loading view while camera initializes
  if (hasPermission === null) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <P style={{ marginTop: 20 }}>Initializing camera...</P>
      </View>
    );
  }

  // Permission denied view
  if (hasPermission === false) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <P style={{ marginBottom: 20 }}>
          Camera permission is required to take a selfie
        </P>
        <Button
          btnText="Grant Permission"
          onPress={async () => {
            const { status } = await Camera.requestCameraPermissionsAsync();
            setHasPermission(status === "granted");
          }}
        />
      </View>
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={3}
            totalPages={6}
            onBack={() => navigation.pop()}
          />
          <ScrollView>
            <View style={styles.contentContainer}>
              <H4 style={styles.mainTitle}>Take a selfie</H4>
              <P style={styles.subtitle}>
                Please make sure you're in a bright space and position your face
                within the frame. Your selfie shouldn't be blurry.
              </P>

              <View style={styles.cameraContainer}>
                {capturedImage ? (
                  // Show captured image
                  <Image
                    source={{ uri: capturedImage.uri }}
                    style={{ width: "100%", height: "100%" }}
                  />
                ) : (
                  // Show camera
                  <View style={styles.camera}>
                    <CameraView
                      ref={camera}
                      style={{ flex: 1 }}
                      facing={facing}
                      ratio="1:1"
                    >
                      <View style={styles.cameraOverlay}>
                        <View style={styles.cornerTopLeft} />
                        <View style={styles.cornerTopRight} />
                        <View style={styles.centerCircle} />
                        <View style={styles.cornerBottomLeft} />
                        <View style={styles.cornerBottomRight} />
                      </View>
                    </CameraView>
                  </View>
                )}
              </View>

              {!capturedImage ? (
                <TouchableOpacity
                  style={styles.takePictureButton}
                  onPress={takePicture}
                  disabled={isCapturing}
                >
                  {isCapturing ? (
                    <ActivityIndicator size="small" color={colors.primary} />
                  ) : (
                    <>
                      <Icon name="camera" size={20} color={colors.primary} />
                      <P style={styles.takePictureText}>Take a selfie</P>
                    </>
                  )}
                </TouchableOpacity>
              ) : (
                <View style={styles.buttonsContainer}>
                  <Button
                    btnText="Retake Selfie"
                    type="alt"
                    onPress={retakePicture}
                    style={styles.retakeButton}
                  />
                  <Button
                    btnText="Submit"
                    onPress={handleSubmit}
                    style={styles.submitButton}
                    loading={loading}
                  />
                </View>
              )}
            </View>
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  centerContent: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
    alignItems: "center",
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
    alignSelf: "flex-start",
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
    alignSelf: "flex-start",
  },
  cameraContainer: {
    width: CAMERA_SIZE,
    height: CAMERA_SIZE,
    borderRadius: 8,
    overflow: "hidden",
    backgroundColor: "#FFF9EA",
    justifyContent: "center",
    alignItems: "center",
  },
  camera: {
    width: "100%",
    height: "100%",
  },
  cameraPreview: {
    width: "100%",
    height: "100%",
  },
  cameraOverlay: {
    position: "absolute",
    width: "70%",
    height: "70%",
    top: "15%",
    left: "15%",
    alignSelf: "center",
  },
  cornerTopLeft: {
    position: "absolute",
    top: 0,
    left: 0,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderColor: colors.primary,
  },
  cornerTopRight: {
    position: "absolute",
    top: 0,
    right: 0,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderColor: colors.primary,
  },
  cornerBottomLeft: {
    position: "absolute",
    bottom: 0,
    left: 0,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderColor: colors.primary,
  },
  cornerBottomRight: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderColor: colors.primary,
  },
  centerCircle: {
    position: "absolute",
    top: "50%",
    left: "50%",
    width: 42,
    height: 42,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
    transform: [{ translateX: -21 }, { translateY: -21 }],
  },
  takePictureButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 40,
  },
  takePictureText: {
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
    color: colors.textAsh,
    marginLeft: 8,
  },
  buttonsContainer: {
    width: "100%",
    marginTop: 40,
  },
  retakeButton: {
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  submitButton: {
    backgroundColor: colors.primary,
  },
});
