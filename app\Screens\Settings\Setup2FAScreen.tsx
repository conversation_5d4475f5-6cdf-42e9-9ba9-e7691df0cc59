import React from "react";
import {
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Input from "../../Components/Input";
import Button from "../../Components/Button";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import { Formik } from "formik";
import * as yup from "yup";

const codeString = "N4EGHJAKHDJAJGDJAJHD";

const validationSchema = yup.object().shape({
  code: yup.string().required("Code is required"),
});

const Setup2FAScreen: React.FC = () => {
  const handleCopy = () => {
    // Implement copy to clipboard logic if needed
  };

  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={{ paddingBottom: 32 }}>
        <Header title="Set up 2 FA" />
        <View style={styles.content}>
          <P style={styles.subtitle}>
            Please download Google Authenticator app on iOS App or Google Play
            Store and scan the code below to activate your 2FA
          </P>
          <View style={styles.qrContainer}>
            {/* Replace with actual QR code if available */}
            <Image
              source={{
                uri: "https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=2FA-SETUP",
              }}
              style={styles.qr}
            />
          </View>
          <View style={styles.codeRow}>
            <P style={styles.code}>{codeString}</P>
            <TouchableOpacity onPress={handleCopy} style={styles.copyIcon}>
              <SvgXml xml={svg.copy} />
            </TouchableOpacity>
          </View>
          <Formik
            initialValues={{ code: "" }}
            validationSchema={validationSchema}
            onSubmit={(values) => {
              // handle 2FA code submit
            }}
          >
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
            }) => (
              <View style={{ width: "100%" }}>
                <Input
                  label="Enter Code"
                  placeholder="Enter code"
                  value={values.code}
                  onChangeText={handleChange("code")}
                  onBlur={handleBlur("code")}
                  error={!!errors.code && touched.code}
                  errorText={errors.code}
                />
                <Button
                  btnText="Submit"
                  onPress={handleSubmit}
                  style={styles.submitBtn}
                />
              </View>
            )}
          </Formik>
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  content: { flex: 1, alignItems: "center", padding: 20 },
  subtitle: {
    fontFamily: fonts.plusJRegular,
    fontSize: 15,
    color: "#888",
    textAlign: "center",
    marginBottom: 24,
  },
  qrContainer: {
    alignItems: "center",
    marginBottom: 16,
  },
  qr: {
    width: 180,
    height: 180,
    marginBottom: 8,
  },
  codeRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 40,
  },
  code: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
    marginRight: 8,
  },
  copyIcon: {
    padding: 4,
  },
  submitBtn: {
    marginTop: 40,
    backgroundColor: "#FBC94A",
    borderRadius: 8,
  },
});

export default Setup2FAScreen;
