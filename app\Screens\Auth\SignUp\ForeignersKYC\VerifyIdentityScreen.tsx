import React from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  ScrollView,
} from "react-native";
import { colors } from "../../../../Config/colors";
import Div from "../../../../Components/Div";
import H4 from "../../../../Components/H4";
import P from "../../../../Components/P";
import { fonts } from "../../../../Config/Fonts";
import Button from "../../../../Components/Button";
import PageHeader from "../../../../Components/PageHeader";
import Icon from "react-native-vector-icons/Feather";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../../Config/Svg";

const { width, height } = Dimensions.get("window");

export default function VerifyIdentityScreen({ navigation }) {
  const handleBeginVerification = () => {
    navigation.navigate("PrivacyConsentScreen");
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            type="bars"
            currentPage={1}
            totalPages={6}
            showBackButton={false}
          />
          <ScrollView>
            <View style={styles.contentContainer}>
              <View style={styles.iconContainer}>
                <Image
                  source={require("../../../../assets/shield-check.png")}
                  style={styles.shieldIcon}
                />
              </View>

              <H4 style={styles.mainTitle}>Verify Your{"\n"}Identity</H4>
              <P style={styles.subtitle}>
                To ensure the security of your account and protect against
                fraud, we need to verify your identity.
              </P>

              <View style={styles.infoContainer}>
                <View style={styles.infoItem}>
                  <SvgXml xml={svg.clock} />
                  <P style={styles.infoText}>Takes 3–5 minutes to complete</P>
                </View>
                <View style={styles.infoItem}>
                  <SvgXml xml={svg.info} />
                  <P style={styles.infoText}>Secure & encrypted process</P>
                </View>
              </View>

              <View style={styles.requirementsContainer}>
                <P style={styles.requirementsTitle}>You'll need:</P>
                <View style={styles.requirementItem}>
                  <SvgXml xml={svg.idCard} />
                  <P style={styles.requirementText}>
                    A valid International Passport
                  </P>
                </View>
                <View style={styles.requirementItem}>
                  <SvgXml xml={svg.cam} />
                  <P style={styles.requirementText}>
                    Your device's camera for a selfie
                  </P>
                </View>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Button
                btnText="Begin Verification"
                onPress={handleBeginVerification}
                btnTextStyle={{ fontFamily: fonts.plusJRegular }}
              />
              <P style={styles.termsText}>
                By continuing, you agree to our{" "}
                <P style={styles.termsLink}>Terms & Privacy Policy</P>
              </P>
            </View>
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
    alignItems: "center",
  },
  iconContainer: {},
  shieldIcon: {
    width: 180,
    height: 180,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: (2.5 * height) / 100,
    color: colors.textBlack,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: (3 * height) / 100,
    fontFamily: fonts.plusJRegular,
    textAlign: "center",
  },
  infoContainer: {
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    padding: 16,
    width: "100%",
    marginBottom: (3 * height) / 100,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  infoText: {
    marginLeft: 12,
    fontSize: 14,
    color: colors.textBlack,
  },
  requirementsContainer: {
    width: "100%",
  },
  requirementsTitle: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginBottom: 12,
  },
  requirementItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  requirementText: {
    marginLeft: 12,
    fontSize: 14,
    color: colors.textBlack,
  },
  buttonContainer: {
    width: "90%",
    marginTop: (8 * height) / 100,
    alignSelf: "center",
  },
  termsText: {
    fontSize: 12,
    color: colors.bGray,
    textAlign: "center",
    marginTop: 12,
  },
  termsLink: {
    color: colors.bGray,
    fontSize: 12,
    fontFamily: fonts.plusJRegular,
    textDecorationLine: "underline",
  },
});
