import React, { useState } from "react";
import { View, StyleSheet } from "react-native";
import Loader from "./Loader";
import NetworkError from "./NetworkError";
import Button from "./Button";
import { colors } from "../Config/colors";

// Example component showing how to use Loader and NetworkError components
const LoaderAndErrorExample: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [showNetworkError, setShowNetworkError] = useState(false);
  const [showTimeoutError, setShowTimeoutError] = useState(false);
  const [showServerError, setShowServerError] = useState(false);

  const simulateApiCall = async (errorType?: 'network' | 'timeout' | 'server') => {
    setLoading(true);
    setShowNetworkError(false);
    setShowTimeoutError(false);
    setShowServerError(false);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate different error types
      if (errorType === 'network') {
        throw { isNetworkError: true, message: 'Network request failed' };
      } else if (errorType === 'timeout') {
        throw { message: 'Request timeout' };
      } else if (errorType === 'server') {
        throw { status: 500, message: 'Internal server error' };
      }
      
      // Success case
      console.log('API call successful!');
    } catch (error: any) {
      console.error('API Error:', error);
      
      // Handle different error types
      if (error?.message?.includes('timeout')) {
        setShowTimeoutError(true);
      } else if (error.isNetworkError) {
        setShowNetworkError(true);
      } else if (error?.status >= 500) {
        setShowServerError(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const resetErrors = () => {
    setShowNetworkError(false);
    setShowTimeoutError(false);
    setShowServerError(false);
  };

  // Show different error types
  if (showNetworkError) {
    return (
      <NetworkError
        type="fullscreen"
        errorType="network"
        onRetry={() => simulateApiCall()}
        onClose={resetErrors}
        showCloseButton={true}
      />
    );
  }

  if (showTimeoutError) {
    return (
      <NetworkError
        type="fullscreen"
        errorType="timeout"
        onRetry={() => simulateApiCall()}
        onClose={resetErrors}
        showCloseButton={true}
      />
    );
  }

  if (showServerError) {
    return (
      <NetworkError
        type="fullscreen"
        errorType="server"
        onRetry={() => simulateApiCall()}
        onClose={resetErrors}
        showCloseButton={true}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* Overlay Loader */}
      <Loader
        type="overlay"
        visible={loading}
        message="Loading user data..."
        size="large"
      />

      {/* Demo Buttons */}
      <View style={styles.buttonContainer}>
        <Button
          btnText="Simulate Success"
          onPress={() => simulateApiCall()}
          style={styles.button}
        />
        
        <Button
          btnText="Simulate Network Error"
          onPress={() => simulateApiCall('network')}
          style={[styles.button, styles.errorButton]}
        />
        
        <Button
          btnText="Simulate Timeout Error"
          onPress={() => simulateApiCall('timeout')}
          style={[styles.button, styles.errorButton]}
        />
        
        <Button
          btnText="Simulate Server Error"
          onPress={() => simulateApiCall('server')}
          style={[styles.button, styles.errorButton]}
        />
      </View>

      {/* Inline Loaders Examples */}
      <View style={styles.inlineExamples}>
        <Loader type="spinner" size="small" message="Small spinner" />
        <Loader type="dots" size="medium" message="Medium dots" />
        <Loader type="spinner" size="large" message="Large spinner" />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: colors.white,
  },
  buttonContainer: {
    gap: 16,
    marginBottom: 32,
  },
  button: {
    backgroundColor: colors.primary,
  },
  errorButton: {
    backgroundColor: colors.red,
  },
  inlineExamples: {
    gap: 24,
    paddingVertical: 20,
  },
});

export default LoaderAndErrorExample;
