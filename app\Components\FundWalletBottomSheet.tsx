import React from "react";
import { View, StyleSheet, TouchableOpacity, } from "react-native";
import P from "./P";

import { SvgXml } from "react-native-svg";
// Assuming svg has a copy icon
import Clipboard from '@react-native-clipboard/clipboard'; // Assuming this library is available or can be installed
import { fonts } from "../Config/Fonts";
import { colors } from "../Config/colors";
import { svg } from "../Config/Svg";

interface FundWalletBottomSheetProps {
  accountHolder: string;
  bankName: string;
  accountNumber: string;
  text2?: string;
  text1?: string
}

const FundWalletBottomSheet: React.FC<FundWalletBottomSheetProps> = ({
  accountHolder,
  bankName,
  accountNumber,
  text2,
  text1
}) => {

  const copyToClipboard = (text: string) => {
    Clipboard.setString(text);
    // Optionally add a toast or notification here
    console.log('Copied to clipboard:', text);
  };

  return (
    <View style={styles.container}>
      <P style={[styles.title, {marginBottom: text2 ? 8 : 24}]}>{text1}</P>
      {text2 && <P style={styles.description}>
        {text2}
      </P>}

      <View style={styles.detailsContainer}>
        <View style={styles.detailRow}>
          <View>
            <P style={styles.label}>Account Holder</P>
            <P style={styles.value}>{accountHolder}</P>
          </View>
          <TouchableOpacity onPress={() => copyToClipboard(accountHolder)}>
            <SvgXml xml={svg.copy || svg.settings} width={24} height={24} />{/* Use actual copy SVG if available */}
          </TouchableOpacity>
        </View>

        <View style={styles.detailRow}>
          <View>
            <P style={styles.label}>Bank Name</P>
            <P style={styles.value}>{bankName}</P>
          </View>
          <TouchableOpacity onPress={() => copyToClipboard(bankName)}>
            <SvgXml xml={svg.copy || svg.settings} width={24} height={24} />{/* Use actual copy SVG if available */}
          </TouchableOpacity>
        </View>

        <View style={styles.detailRow}>
          <View>
            <P style={styles.label}>Account Number</P>
            <P style={styles.value}>{accountNumber}</P>
          </View>
          <TouchableOpacity onPress={() => copyToClipboard(accountNumber)}>
            <SvgXml xml={svg.copy} width={24} height={24} />{/* Use actual copy SVG if available */}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 32,
    minHeight: 200,
    alignItems: "center",
  },
  title: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 18,
    color: colors.textBlack,
    marginBottom: 8,
    textAlign: "center",
  },
  description: {
    fontFamily: fonts.plusJRegular,
    fontSize: 15,
    color: "#888",
    textAlign: "center",
    marginBottom: 24,
  },
  detailsContainer: {
    width: '100%',
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    padding: 12
  },
  label: {
    fontFamily: fonts.plusJRegular,
    fontSize: 13,
    color: "#888",
    marginBottom: 4,
  },
  value: {
    fontFamily: fonts.plusJMedium,
    fontSize: 15,
    color: colors.textBlack,
  },
});

export default FundWalletBottomSheet; 