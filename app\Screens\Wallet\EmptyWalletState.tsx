import React, { useEffect } from "react";
import {
  StyleSheet,
  Text,
  Image,
  TouchableOpacity,
  View,
  Dimensions,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import { GetUser } from "../../RequestHandler.tsx/User";

interface EmptyWalletStateProps {
  navigation: any;
  isCardCreated?: boolean
  country?: string
}

const { width, height } = Dimensions.get("window");

const EmptyWalletState = ({ navigation, isCardCreated, country }: EmptyWalletStateProps) => {
  const onCreateWallet = () => {
    if (country == "NGA") {
      navigation.navigate("ActivateTransactionHubScreen", { country: country });
    } else if (country !== "NGA" && !isCardCreated) {
      navigation.navigate("Card");
    } else {
      navigation.navigate("ActivateTransactionHubScreen", { country: country });
    }
  };



  useEffect(() => {
    console.log("country", country, isCardCreated);
  }, [])


  return (
    <View style={styles.container}>
      <Image
        source={require("../../assets/wallet-empty.png")}
        style={styles.image}
        resizeMode="contain"
      />
      <Text style={styles.title}>Activate your Transaction Hub</Text>
      <Text style={styles.description}>
        Complete your KYC process and instantly start making transactions in
        Naira.
      </Text>
      <View style={{ width: "100%" }}>
        <Button
          btnText="Activate your Transaction Hub"
          onPress={onCreateWallet}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    marginTop: (10 * height) / 100,
  },
  image: {
    width: 160,
    height: 160,
    marginBottom: 24,
  },
  title: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 24,
    color: colors.textBlack,
    marginBottom: 12,
    textAlign: "center",
  },
  description: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textAsh,
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 24,
  },
  button: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    width: "100%",
    alignItems: "center",
  },
  buttonText: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 16,
    color: colors.textBlack,
  },
});

export default EmptyWalletState;
