import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";
const request = new RequestHandler();

// Function to get auth token from AsyncStorage
export async function getAuthToken(): Promise<string> {
  try {
    const cookiesString = await AsyncStorage.getItem("cookies");
    if (cookiesString) {
      const cookies = JSON.parse(cookiesString);
      if (
        (cookies && cookies.data && cookies.data.token) ||
        (cookies && cookies.token)
      ) {
        return cookies.data.token ? cookies.data.token : cookies.token;
      }
    }
    return "";
  } catch (error) {
    console.error("Error getting auth token:", error);
    return "";
  }
}

const decodeJwt = (token: string): string | undefined => {
  if (token) {
    try {
      const payload = token.split(".")[1];
      const decodedPayload = JSON.parse(atob(payload));
      return decodedPayload.id;
    } catch (error) {
      console.error("Error decoding JWT:", error);
      return undefined;
    }
  }
  return undefined;
};

export async function OrderACard(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("api/v1/elevate-api/getly/cards/order", body, token);
}
export async function PayForCard(body: object, orderId): Promise<any> {
  const token = await getAuthToken();
  return request.post(`api/v1/elevate-api/getly/cards/${orderId}/payment`, body, token);
}
export async function ActivateCard(body: object, orderId): Promise<any> {
  const token = await getAuthToken();
  return request.post(`api/v1/elevate-api/getly/cards/${orderId}/map-card`, body, token);
}
export async function GetUserCards(userId): Promise<any> {
  const token = await getAuthToken();
  return request.get(`api/v1/elevate-api/cards/customer/${userId}?page=1&limit=10`, token);
}
export async function GetOrderById(orderID: string): Promise<any> {
  const token = await getAuthToken();
  return request.get(`api/v1/elevate-api/getly/cards/order/${orderID}`, token);
}
export async function GetAllOrderByIds(): Promise<any> {
  const token = await getAuthToken();
  const userId = decodeJwt(token);
  return request.get(`api/v1/elevate-api/getly/user/${userId}/orders`, token);
}
export async function GetPickUpLocations(): Promise<any> {
  const token = await getAuthToken();
  return request.get(`api/v1/elevate-api/getly/cards/pickup-locations`, token);
}
export async function SavePickupLocation(orderId: string, body: object): Promise<any> {
  const token = await getAuthToken();
  return request.put(`api/v1/elevate-api/getly/cards/${orderId}/pickup`, body, token);
}