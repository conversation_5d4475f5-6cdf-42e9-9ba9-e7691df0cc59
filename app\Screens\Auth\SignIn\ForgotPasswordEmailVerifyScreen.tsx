import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import { OtpInput } from "react-native-otp-entry";
import { useToast } from "../../../Context/ToastContext";
import PageHeader from "../../../Components/PageHeader";
import Button from "../../../Components/Button";
import { ForgotPasswordResendOtp, ForgotPasswordVerifyOtp } from "../../../RequestHandler.tsx/Auth";
const { width, height } = Dimensions.get("window");

export default function ForgotPasswordEmailScreen({ navigation, route }) {
  // Get email from previous screen
  const { email } = route.params || {};
  const { handleToast } = useToast();

  // State for OTP inputs
  const [otpCode, setOtpCode] = useState("");
  const [timer, setTimer] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const [otpError, setOtpError] = useState("");
  const [isOtpFilled, setIsOtpFilled] = useState(false);
  const [loading, setLoading] = useState(false);


  // Timer effect
  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setIsResendDisabled(false);
    }
  }, [timer]);

  const resendOtp = async () => {
    if (!isResendDisabled) {
      // Reset OTP field
      setOtpCode("");
      // Reset timer
      setTimer(60);
      setIsResendDisabled(true);
      setOtpError("");

      try {
        const body = { email };
        await ForgotPasswordResendOtp(body);
      } catch (error) {
        console.log(error);
        handleToast(error.message || "Failed to resend verification code", "error");
      }
    }
  };
  // Handle OTP changes
  const handleOtpChange = (text: string) => {
    setOtpCode(text);
    setOtpError("");
  };

  // Handle OTP filled
  const handleOtpFilled = () => {
    setIsOtpFilled(true);
  };

  //handle submit
  const handleSubmit = async () => {
    if (otpCode.length !== 6) {
      setOtpError("Please enter a valid 6-digit code");
      return;
    }
    setLoading(true);
    try {
      const body = {
        email,
        otp: otpCode
      };
      await ForgotPasswordVerifyOtp(body);
      // Navigate to reset password screen with email
      navigation.navigate("ResetPasswordScreen", { email });
    } catch (error) {
      console.log(error);
      setOtpError("Invalid verification code");
      handleToast(error.message || "Failed to verify code", "error");
    } finally {
      setLoading(false);
    }
  };
  return (
    <View style={styles.mainContainer}>
      <Div>
        <PageHeader onBack={() => navigation.pop()} />
        <View style={styles.container}>
          {/* Back Button */}

          <H4 style={styles.mainTitle}>Verify email address</H4>
          <P style={styles.subtitle}>
            Enter the 6 digit code that was sent to your email address
          </P>

          {/* OTP Input */}
          <View style={styles.otpWrapper}>
            <OtpInput
              numberOfDigits={6}
              focusColor={colors.primary}
              autoFocus={true}
              hideStick={true}
              placeholder=""
              blurOnFilled={true}
              disabled={false}
              type="numeric"
              secureTextEntry={false}
              focusStickBlinkingDuration={500}
              onFocus={() => console.log("Focused")}
              onBlur={() => console.log("Blurred")}
              onTextChange={handleOtpChange}
              onFilled={handleOtpFilled}
              textInputProps={{
                accessibilityLabel: "Email Verification Code",
                caretHidden: false,
              }}
              theme={{
                containerStyle: styles.otpContainer,
                pinCodeContainerStyle: otpError
                  ? styles.otpInputError
                  : styles.otpInput,
                pinCodeTextStyle: styles.otpText,
                focusedPinCodeContainerStyle: styles.otpInputFocused,
                placeholderTextStyle: styles.otpPlaceholder,
                filledPinCodeContainerStyle: styles.otpInputFilled,
                disabledPinCodeContainerStyle: styles.otpInputDisabled,
              }}
            />
          </View>

          {/* Resend Code Button */}
          <View style={styles.resendContainer}>
            <>
              <P
                // @ts-ignore
                style={[
                  styles.resendText,
                  {
                    marginRight: 8,
                  },
                ]}
              >
                Didn’t get any code?
              </P>
              <TouchableOpacity onPress={resendOtp}>
                <P
                  // @ts-ignore
                  style={[
                    styles.resendText2,
                    isResendDisabled && styles.disabledText,
                  ]}
                >
                  Resend
                </P>
              </TouchableOpacity>
            </>
          </View>
          {isResendDisabled && (
            <>
              <>
                <P style={styles.timerText}>
                  {`${timer < 10 ? "00:0" : "00:"}${timer}`}
                </P>
              </>
            </>
          )}
        </View>
        {isOtpFilled ? (
          <View style={{ width: "90%", marginBottom: (10 * height) / 100 }}>
            <Button btnText="Next" onPress={handleSubmit} loading={loading} />
          </View>
        ) : (
          <View style={styles.footerContainer}>
            <View style={styles.loginContainer}>
              <Text style={styles.accountText}>Don’t have an account?</Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("SignUpScreen1");
                }}
              >
                <Text style={styles.loginText}>Register</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    backgroundColor: colors.white,
  },
  backButton: {
    alignSelf: "flex-start",
    padding: 10,
    marginTop: 20,
  },
  backButtonText: {
    fontSize: 28,
    fontFamily: fonts.plusJBold,
  },
  title: {
    fontFamily: fonts.plusJBold,
    fontSize: 24,
    alignSelf: "flex-start",
    marginTop: 20,
    marginBottom: 18,
  },
  description: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textBlack,
    alignSelf: "flex-start",
    marginBottom: 29,
    lineHeight: 22,
  },
  emailText: {
    fontFamily: fonts.plusJMedium,
  },
  otpWrapper: {
    width: "100%",
  },
  otpContainer: {
    width: "100%",
    justifyContent: "space-between",
  },
  otpInput: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 4,
    marginHorizontal: 0,
  },
  otpInputError: {
    width: width * 0.15,
    height: width * 0.15,
    borderWidth: 1,
    borderColor: colors.red,
    borderRadius: 10,
    marginHorizontal: 0,
  },
  otpText: {
    color: colors.textAsh,
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
  },
  otpInputFocused: {
    borderColor: colors.stroke,
    backgroundColor: colors.white,
  },
  otpPlaceholder: {
    color: colors.textBlack,
    fontSize: 20,
    fontFamily: fonts.plusJRegular,
  },
  otpInputFilled: {
    borderColor: colors.stroke,
    backgroundColor: colors.white,
  },
  otpInputDisabled: {
    borderColor: colors.stroke,
    backgroundColor: "#F5F5F5",
  },
  resendContainer: {
    flexDirection: "row",
    marginTop: 16,
    marginBottom: 40,
  },
  resendText: {
    fontFamily: fonts.plusJLight,
    fontSize: 14,
    color: colors.textAsh,
  },
  resendText2: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textBlack,
  },
  disabledText: {
    color: colors.textAsh,
  },
  timerText: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 20,
    color: colors.textBlack,
    marginLeft: 8,
    textAlign: "center",
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  footerContainer: {
    width: "100%",
    alignItems: "center",
  },
  loginContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 45,
  },
  accountText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: "#666",
  },
  loginText: {
    fontFamily: fonts.plusJBold,
    fontSize: 14,
    color: colors.black,
    marginLeft: 5,
  },
  pointer: {
    color: "red",
  },
});
