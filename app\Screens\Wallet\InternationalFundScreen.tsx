import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  SafeAreaView,
  Dimensions,
  TextInput,
  TouchableOpacity,
  Image,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import Header from "../../Components/Header";
import P from "../../Components/P";
import H4 from "../../Components/H4";
import Icon from "react-native-vector-icons/Feather";
import PaymentStatusModal, {
  PaymentStatus,
} from "../../Components/PaymentStatusModal";

const { width, height } = Dimensions.get("window");

interface InternationalFundScreenProps {
  navigation: any;
  route: {
    params: {
      // Define any parameters needed for international funding here
      // For example: amount, currency, etc.
       amount: number;
       currency: string;
    };
  };
}

export default function InternationalFundScreen({
  navigation,
  route,
}: InternationalFundScreenProps) {
   const { amount, currency } = route.params; // Access params if needed

  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [nameOnCard, setNameOnCard] = useState("");
  const [address, setAddress] = useState("");
  const [postalCode, setPostalCode] = useState("");
  const [city, setCity] = useState("");
  const [selectedCard, setSelectedCard] = useState("");
  const [loading, setLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(
    null
  );
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Format card number with spaces
  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(" ");
    } else {
      return value;
    }
  };

  // Format expiry date (MM/YY)
  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    if (v.length > 2) {
      return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
    }
    return v;
  };

  // Handle payment submission
  const handlePayment = () => {
    // Basic validation
    if (
      !cardNumber ||
      !expiryDate ||
      !cvv ||
      !nameOnCard ||
      !address ||
      !postalCode
    ) {
      alert("Please fill in all fields");
      return;
    }
    setLoading(true);
    setPaymentStatus("processing");
    setShowStatusModal(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      // Randomly determine success or failure for demo purposes
      const isSuccess = Math.random() > 0.3; // 70% chance of success
      if (isSuccess) {
        setPaymentStatus("success");
      } else {
        setPaymentStatus("failed");
        setErrorMessage("Payment Failed"); // Generic error message for international
      }
    }, 3000);
  };

  // Handle button press in the status modal
  const handleStatusButtonPress = () => {
    setShowStatusModal(false);
    if (paymentStatus === "success") {
      // Navigate to a success screen or close modal
       console.log('International funding successful');
       // Example: navigation.navigate('FundingSuccessScreen');
    } else if (paymentStatus === "failed") {
      // Close modal and let user try again
      setShowStatusModal(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header
          title="Fund Wallet"
          contStyle={{ justifyContent: "flex-start", gap: 24 }}
          showNotification={false}
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          style={{ width: "95%" }}
        >
          {/* Price Information - Adjust as needed for international */}
          <View style={styles.priceInfoCard}>
             {/* Display amount and currency being funded */}
             <View style={styles.priceRow}>
               <View>
                 <P style={styles.priceLabel}>Amount to Fund</P>
                 <H4 style={styles.priceValue}>{`${currency} ${amount}`}</H4>
               </View>
               {/* Add currency flag/icon if needed */}
             </View>
          </View>

          <View style={styles.divider} /> {/* Add divider if needed */}

          {/* Payment Form */}
          <View style={styles.formContainer}>
            {/* Previous Card Selection - Keep or remove as needed */}
            <View style={styles.formGroup}>
              <P style={styles.formLabel}>Use Previous Card</P>
              <TouchableOpacity style={styles.selectInput}>
                <P style={styles.selectText}>Select Card</P>
                <Icon name="chevron-down" size={20} color="#000" />
              </TouchableOpacity>
            </View>

            {/* Name on Card */}
            <View style={styles.formGroup}>
              <P style={styles.formLabel}>Name on Card</P>
              <TextInput
                style={styles.input}
                placeholder="First name Last Name"
                placeholderTextColor="#D7D5D9"
                value={nameOnCard}
                onChangeText={setNameOnCard}
              />
            </View>
            {/* Card Details Section */}
            <P style={styles.sectionTitle}>Card Details</P>
            {/* Card Number */}
            <View style={styles.formGroup}>
              <TextInput
                style={styles.input}
                placeholder="1234 5678 9101"
                value={cardNumber}
                placeholderTextColor="#D7D5D9"
                onChangeText={(text) => setCardNumber(formatCardNumber(text))}
                keyboardType="numeric"
                maxLength={19} // 16 digits + 3 spaces
              />
            </View>

            {/* Expiry Date and CVV */}
            <View style={styles.rowInputs}>
              <View style={[styles.formGroup, { flex: 1, marginRight: 10 }]}>
                 <P style={styles.formLabel}>Expiry Date</P>
                 <TextInput
                   style={styles.input}
                   placeholder="MM/YY"
                   placeholderTextColor="#D7D5D9"
                   value={expiryDate}
                   onChangeText={(text) => setExpiryDate(formatExpiryDate(text))}
                   keyboardType="numeric"
                   maxLength={5} // MM/YY
                 />
              </View>
               <View style={[styles.formGroup, { flex: 1 }]}>
                  <P style={styles.formLabel}>CVV</P>
                  <TextInput
                    style={styles.input}
                    placeholder="123"
                    placeholderTextColor="#D7D5D9"
                    value={cvv}
                    onChangeText={setCvv}
                    keyboardType="numeric"
                    maxLength={4} // Most CVVs are 3-4 digits
                   />
              </View>
            </View>

            {/* Billing Address Section */}
            <P style={styles.sectionTitle}>Billing Address</P>
            {/* Address */}
            <View style={styles.formGroup}>
               <P style={styles.formLabel}>Address</P>
              <TextInput
                style={styles.input}
                placeholder="Billing Address"
                placeholderTextColor="#D7D5D9"
                value={address}
                onChangeText={setAddress}
               />
            </View>

             {/* City and Postal Code */}
            <View style={styles.rowInputs}>
              <View style={[styles.formGroup, { flex: 1, marginRight: 10 }]}>
                 <P style={styles.formLabel}>City</P>
                 <TextInput
                   style={styles.input}
                   placeholder="City"
                   placeholderTextColor="#D7D5D9"
                   value={city}
                   onChangeText={setCity}
                 />
              </View>
               <View style={[styles.formGroup, { flex: 1 }]}>
                   <P style={styles.formLabel}>Postal Code</P>
                   <TextInput
                     style={styles.input}
                     placeholder="Postal Code"
                     placeholderTextColor="#D7D5D9"
                     value={postalCode}
                     onChangeText={setPostalCode}
                      keyboardType="numeric"
                    />
               </View>
            </View>

          </View>

           {/* Pay Button */}
            <Button
               btnText="Pay"
               onPress={handlePayment}
               loading={loading}
               style={styles.payButton}
             />

        </ScrollView>
      </Div>

      {/* Payment Status Modal */}
      <PaymentStatusModal
        visible={showStatusModal}
        status={paymentStatus}
        errorMessage={paymentStatus === 'failed' ? errorMessage : ""}
        onClose={() => setShowStatusModal(false)}
        onButtonPress={handleStatusButtonPress}
      />

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
     paddingBottom: 20,
  },
   priceInfoCard: {
    backgroundColor: "#FAF3E7",
    borderRadius: 12,
    marginHorizontal: 16,
    padding: 16,
    marginTop: 20,
    marginBottom: 20,
  },
  priceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 10,
  },
  priceLabel: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: colors.textAsh,
  },
  priceValue: {
    fontFamily: fonts.plusJBold,
    fontSize: 20,
    color: colors.textBlack,
    marginTop: 4,
  },
  priceValueContainer: {
     flexDirection: "row",
    alignItems: "center",
  },
   currencyFlag: {
    width: 24,
    height: 24,
    borderRadius: 12,
    overflow: 'hidden',
    marginRight: 8,
  },
   flagImage: {
    width: '100%',
    height: '100%',
   },
  currencyText: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
    color: colors.textBlack,
  },
   divider: {
    height: 1,
    backgroundColor: "#D7D5D9",
    marginVertical: 10,
  },
  feeContainer: {
     flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginHorizontal: 16,
    marginBottom: 10,
  },
   feeLabel: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: colors.textAsh,
   },
   feeValue: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
    color: colors.textBlack,
   },
  totalContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginHorizontal: 16,
  },
   totalLabel: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 16,
    color: colors.textBlack,
   },
    totalValue: {
    fontFamily: fonts.plusJBold,
    fontSize: 18,
    color: colors.textBlack,
   },
  formContainer: {
    marginHorizontal: 16,
    marginTop: 20,
  },
   formGroup: {
    marginBottom: 16,
   },
   formLabel: {
    fontFamily: fonts.plusJMedium,
    fontSize: 15,
    color: colors.textBlack,
    marginBottom: 8,
   },
   selectInput: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    padding: 12,
   },
    selectText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textAsh,
   },
  input: {
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textBlack,
   },
  sectionTitle: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 16,
    color: colors.textBlack,
    marginTop: 20,
    marginBottom: 12,
   },
  rowInputs: {
    flexDirection: "row",
    justifyContent: "space-between",
   },
   payButton: {
    marginHorizontal: 16,
    marginTop: 32,
    backgroundColor: colors.primary,
    borderRadius: 8,
   }
}); 