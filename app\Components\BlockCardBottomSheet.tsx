import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
  Dimensions,
  Animated,
  Alert,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import Button from "./Button";
import Icon from "react-native-vector-icons/Feather";

const { width, height } = Dimensions.get("window");

interface BlockCardBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onBlockCard: () => void;
  onUnblockCard: () => void;
}

const BlockCardBottomSheet: React.FC<BlockCardBottomSheetProps> = ({
  isVisible,
  onClose,
  onBlockCard,
  onUnblockCard,
}) => {
  const [selectedOption, setSelectedOption] = useState<"block" | "unblock" | null>(null);
  const [loading, setLoading] = useState(false);

  // Animation value for bottom sheet
  const slideAnim = React.useRef(new Animated.Value(height)).current;

  React.useEffect(() => {
    if (isVisible) {
      // Reset selected option when opening
      setSelectedOption(null);
      
      // Slide up animation
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Slide down animation
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible, slideAnim]);

  // Handle block card
  const handleBlockCard = () => {
    if (!selectedOption) {
      Alert.alert("Error", "Please select an option");
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      
      if (selectedOption === "block") {
        onBlockCard();
      } else {
        onUnblockCard();
      }
      
      onClose();
    }, 1500);
  };

  // Handle backdrop press
  const handleBackdropPress = () => {
    onClose();
  };

  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={handleBackdropPress}
        />
        
        <Animated.View
          style={[
            styles.bottomSheet,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Block your card</Text>
          </View>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.optionItem,
                selectedOption === "block" && styles.selectedOption,
              ]}
              onPress={() => setSelectedOption("block")}
            >
              <View style={[styles.radioButton, selectedOption === "block" && styles.radioButtonSelected]}>
                {selectedOption === "block" && <View style={styles.radioButtonInner} />}
              </View>
              <Text style={styles.optionText}>Block Card</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.optionItem,
                selectedOption === "unblock" && styles.selectedOption,
              ]}
              onPress={() => setSelectedOption("unblock")}
            >
              <View style={[styles.radioButton, selectedOption === "unblock" && styles.radioButtonSelected]}>
                {selectedOption === "unblock" && <View style={styles.radioButtonInner} />}
              </View>
              <Text style={styles.optionText}>Unblock Card</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.buttonContainer}>
            <Button
              btnText={selectedOption === "block" ? "Block Card" : "Unblock Card"}
              onPress={handleBlockCard}
              loading={loading}
              disabled={!selectedOption}
            />
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bottomSheet: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingBottom: 30,
    paddingTop: 16,
    maxHeight: height * 0.7,
  },
  header: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  optionsContainer: {
    marginBottom: 30,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F2F2F2",
  },
  selectedOption: {
    backgroundColor: "#F9F9F9",
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#E5E7EB",
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  radioButtonSelected: {
    borderColor: colors.primary,
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.primary,
  },
  optionText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  buttonContainer: {
    marginTop: 10,
  },
});

export default BlockCardBottomSheet;
