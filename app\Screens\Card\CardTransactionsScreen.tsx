import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
  Image,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Icon from "react-native-vector-icons/Feather";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import TransactionItem from "../../Components/TransactionItem";

const { width, height } = Dimensions.get("window");

interface Transaction {
  id: string;
  merchant: string;
  amount: number;
  status: "successful" | "declined";
  date: string;
  type: "credit" | "debit";
}

interface CardTransactionsScreenProps {
  navigation: any;
}

export default function CardTransactionsScreen({
  navigation,
}: CardTransactionsScreenProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch transactions
    const fetchTransactions = async () => {
      // Mock data
      const mockTransactions: Transaction[] = [
        {
          id: "1",
          merchant: "Beauty Emporium Ltd.",
          amount: 200,
          status: "successful",
          date: "2023-05-15",
          type: "debit",
        },
        {
          id: "2",
          merchant: "Ronald Balium",
          amount: 2000,
          status: "successful",
          date: "2023-05-14",
          type: "credit",
        },
        {
          id: "3",
          merchant: "USDC",
          amount: 3000,
          status: "declined",
          date: "2023-05-13",
          type: "debit",
        },
      ];

      setTimeout(() => {
        setTransactions(mockTransactions);
        setLoading(false);
      }, 1000);
    };

    fetchTransactions();
  }, []);

  // Render transaction item
  const renderTransactionItem = ({ item }: { item: Transaction }) => {
    const isCredit = item.type === "credit";
    const amountText = isCredit ? `N${item.amount}` : `-N ${item.amount}`;
    const amountColor = isCredit ? colors.success : colors.error;

    return (
      <TransactionItem
        key={item.id}
        name={item.merchant}
        status={item.status}
        amount={item.amount}
        isDebit={item.type === "debit" ? true : false}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header title="Zoom Card" showNotification={true} />

        <View style={styles.content}>
          <View style={styles.transactionHistoryHeader}>
            <Text style={styles.transactionHistoryTitle}>
              Transaction History
            </Text>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size={"large"} color={colors.primary} />
            </View>
          ) : transactions.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Image
                source={require("../../assets/empty-transactions.png")}
                style={styles.emptyTransactionsImage}
              />
              <Text style={styles.emptyText}>No transactions found</Text>
            </View>
          ) : (
            <FlatList
              data={transactions}
              renderItem={renderTransactionItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.transactionsList}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primarySubtle,
  },
  content: {
    width: "90%",
    backgroundColor: colors.primarySubtle,
    flex: 1,
    paddingHorizontal: 16,
  },
  transactionHistoryHeader: {
    paddingVertical: 16,
  },
  transactionHistoryTitle: {
    fontSize: 16,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
  },
  transactionsList: {
    paddingBottom: 20,
  },
  transactionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
  },
  transactionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  merchantName: {
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
    marginBottom: 4,
  },
  transactionStatus: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
  },
  transactionAmount: {
    fontSize: 16,
    fontFamily: fonts.plusJSemibold,
  },
  emptyTransactionsImage: {
    width: 130,
    height: 98,
    marginBottom: 16,
  },
});
