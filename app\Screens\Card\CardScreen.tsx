import React, { useCallback, useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Linking,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import Header from "../../Components/Header";
import P from "../../Components/P";
import H4 from "../../Components/H4";
import CardStack from "../../Components/CardStack";
import { GetWallet } from "../../RequestHandler.tsx/User";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../Components/Loader";
import NetworkError from "../../Components/NetworkError";
import { GetAllOrderByIds } from "../../RequestHandler.tsx/Card";

const { height } = Dimensions.get("window");

const CardCurrencySelectionBottomSheet = ({
  isVisible,
  onClose,
  onSelectCurrency,
}) => {
  const [selectedCurrency, setSelectedCurrency] = useState(null);

  // Log when the modal visibility changes
  React.useEffect(() => {
    console.log("Modal isVisible changed to:", isVisible);
  }, [isVisible]);

  const currencies = [
    { label: "NGN/Naira Card", value: "ngn", code: "ng" },
    { label: "USD/Dollar Card", value: "usd", code: "us" },
  ];

  const handleContinue = () => {
    if (selectedCurrency) {
      console.log(selectedCurrency);
      if (selectedCurrency === "ngn") {
        onSelectCurrency(selectedCurrency);
      } else {
        Linking.openURL("https://getly.app/")
      }
      onClose();
    }
  };

  console.log("Rendering modal with isVisible:", isVisible);
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={() => {
        console.log("Modal onRequestClose triggered");
        onClose();
      }}
      statusBarTranslucent
    >
      <View style={styles.modalOverlay}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.modalBackdrop} />
        </TouchableWithoutFeedback>

        <View style={styles.modalContent}>
          {/* Modal Header with close button */}
          <View style={styles.modalHeader}>
            <View style={styles.modalHeaderContent}>
              <H4 style={styles.modalTitle}>Select Card Currency</H4>
              <P style={styles.modalSubtitle}>
                You will be able to make payments{"\n"}with the currency you
                select
              </P>
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Currency Options */}
          <View style={styles.currencyOptions}>
            {currencies.map((currency) => (
              <TouchableOpacity
                key={currency.value}
                style={[
                  styles.currencyOption,
                  selectedCurrency === currency.value &&
                  styles.selectedCurrencyOption,
                ]}
                onPress={() => setSelectedCurrency(currency.value)}
              >
                <Image
                  source={{
                    uri: `https://flagcdn.com/w2560/${currency.code.toLowerCase()}.png`,
                  }}
                  style={[
                    styles.currencyIcon,
                    {
                      objectFit: currency.code === "ng" ? "fill" : "cover",
                    },
                  ]}
                />
                <P style={styles.currencyLabel}>{currency.label}</P>

                {selectedCurrency === currency.value && (
                  <View style={styles.radioSelected}>
                    <View style={styles.radioInner} />
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Continue Button */}
          <View style={styles.bottomSheetButtonContainer}>
            <Button
              btnText="Continue"
              onPress={handleContinue}
              disabled={!selectedCurrency}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const CardSelectionModal = ({ isVisible, onClose, onSelectCard, completedOrders }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <View style={styles.modalOverlay}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.modalBackdrop} />
        </TouchableWithoutFeedback>

        <View style={styles.cardSelectionModalContent}>
          {/* Modal Header */}
          <View style={styles.cardSelectionModalHeader}>
            <H4 style={styles.cardSelectionModalTitle}>Select Card to Pick Up</H4>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Modal Content */}
          <View style={styles.cardSelectionModalBody}>
            <P style={styles.cardSelectionModalMessage}>
              You have multiple completed card orders without pickup locations.
              Please select which card you want to pick up:
            </P>

            {/* Card Options */}
            <View style={styles.cardOptions}>
              {completedOrders.map((order, index) => (
                <TouchableOpacity
                  key={order.orderId}
                  style={styles.cardOption}
                  onPress={() => onSelectCard(order.orderId)}
                >
                  <View style={styles.cardOptionContent}>
                    <Text style={styles.cardOptionTitle}>Card {index + 1}</Text>
                    <Text style={styles.cardOptionSubtitle}>
                      {order.currency} • Quantity: {order.quantity}
                    </Text>
                  </View>
                  <Text style={styles.cardOptionArrow}>→</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const PickupLocationModal = ({ isVisible, onClose, onContinue, orderId }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <View style={styles.centeredModalOverlay}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.modalBackdrop} />
        </TouchableWithoutFeedback>

        <View style={styles.pickupModalContent}>
          {/* Modal Header */}
          <View style={styles.pickupModalHeader}>
            <H4 style={styles.pickupModalTitle}>Card Order Complete!</H4>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Modal Content */}
          <View style={styles.pickupModalBody}>
            <Image
              source={require("../../assets/order-card.png")}
              style={styles.pickupModalImage}
              resizeMode="contain"
            />
            <P style={styles.pickupModalMessage}>
              Great news! Your card order has been completed successfully.
              However, you haven't selected a pickup location yet.
            </P>
            <P style={styles.pickupModalSubMessage}>
              Please select your preferred pickup location to proceed with card collection.
            </P>
          </View>

          {/* Action Buttons */}
          <View style={styles.pickupModalButtons}>
            <Button
              btnText="Continue to Pickup Location"
              onPress={() => onContinue(orderId)}
              style={styles.pickupContinueButton}
            />
            <TouchableOpacity onPress={onClose} style={styles.pickupLaterButton}>
              <Text style={styles.pickupLaterText}>I'll do this later</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default function CardScreen({ navigation }) {
  const [hasCreatedCardBefore, setHasCreatedCardBefore] = useState(false);
  const [showCurrencySelection, setShowCurrencySelection] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [showPickupLocationModal, setShowPickupLocationModal] = useState(false);
  const [showCardSelectionModal, setShowCardSelectionModal] = useState(false);
  const [pendingOrderId, setPendingOrderId] = useState(null);
  const [completedOrdersWithoutPickup, setCompletedOrdersWithoutPickup] = useState([]);

  const checkWallet = async () => {
    setLoading(true);
    setIsNetworkError(false);
    try {
      const response = await GetWallet();
      console.log(response);
      // Update hasCreatedCardBefore based on API response
      if (response?.data?.hasCard !== undefined) {
        setHasCreatedCardBefore(response.data.hasCard);
      }
    } catch (error) {
      console.log(error);
      if (error.isNetworkError) {
        setIsNetworkError(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const getAllOrders = async () => {
    try {
      const res = await GetAllOrderByIds();
      console.log(res.data.orders);

      // Filter completed orders without pickup location
      const completedOrders = res.data.orders.filter(order =>
        order.status === "COMPLETED" && order.pickupLocationId === null
      );

      if (completedOrders.length > 0) {
        setCompletedOrdersWithoutPickup(completedOrders);

        if (completedOrders.length === 1) {
          // If only one completed order, show pickup location modal directly
          setPendingOrderId(completedOrders[0].orderId);
          setShowPickupLocationModal(true);
        } else {
          // If multiple completed orders, show card selection modal
          setShowCardSelectionModal(true);
        }
      }
    } catch (error) {
      console.log("Error fetching orders:", error);
    }
  };

  useEffect(()=>{
    getAllOrders()
  },[])

  useFocusEffect(
    useCallback(() => {
      checkWallet();
    }, [])
  );

  // Sample card data
  const cardData = [
    {
      id: "1",
      name: "Ruby",
      balance: "0.00",
      cardNumber: "1234 **** **** 1234",
      expiryDate: "08/28",
      holderName: "Anita Amadi",
      color: "gold",
      currency: "NGN",
    },

  ];

  // Show currency selection modal when ordering a physical card
  const handleOrderPhysicalCard = () => {
    setShowCurrencySelection(true);
    console.log("Modal visibility set to:", true);
  };

  // Function to handle navigation to get virtual card screen
  const handleGetVirtualCard = () => { Linking.openURL("https://getly.app/") };

  const handleCurrencySelection = (currency: string) => {
    navigation.navigate("OrderPhysicalCardScreen", { currency });
  };

  // Function to handle card selection from multiple completed orders
  const handleCardSelection = (orderId: string) => {
    setShowCardSelectionModal(false);
    setPendingOrderId(orderId);
    setShowPickupLocationModal(true);
  };

  // Function to handle pickup location navigation
  const handlePickupLocationContinue = (orderId: string) => {
    setShowPickupLocationModal(false);
    navigation.navigate("PickupLocationScreen", { orderId });
  };

  // Function to handle card activation
  const handleActivateCard = () => {
    navigation.navigate("ActivateCardScreen");
  };

  // Handle card selection from the stack
  const handleCardPress = (card: any) => {
    setSelectedCard(card);
    navigation.navigate("ZoomCardScreen", {
      cardType: card.color === "gold" ? "gold" : "navy",
    });
  };

  // Show network error if there's a network issue
  if (isNetworkError) {
    return (
      <View style={styles.container}>
        <Div>
          <Header title="Cards" />
          <NetworkError
            type="fullscreen"
            errorType="network"
            onRetry={checkWallet}
          />
        </Div>
      </View>
    );
  }

  // Show loading state while checking card status
  if (loading) {
    return <Loader type="overlay" size="large" style={{ marginLeft: 8 }} />;
  }

  if (!hasCreatedCardBefore) {
    return (
      <View style={styles.container}>
        <Div>
          <Header title="Cards" contStyle={{ width: "100%" }} />
          <View style={{ width: "90%" }}>
            <View style={styles.cardImageContainer}>
              <Image
                source={require("../../assets/order-card.png")}
                style={styles.cardImage}
                resizeMode="contain"
              />
            </View>
            {/* Order Card Content */}
            <View style={styles.orderCardContent}>
              <Text style={styles.orderCardTitle}>Order a card</Text>
              <Text style={styles.orderCardDescription}>
                Instantly order a card for making payments online and
                physically.
              </Text>
            </View>
            {/* Action Button */}
            <View style={styles.actionButtonContainer}>
              <Button
                btnText="Order your first card"
                onPress={handleOrderPhysicalCard}
              />
            </View>
          </View>
          {/* Currency Selection Modal - Include in both views */}
          <CardCurrencySelectionBottomSheet
            isVisible={showCurrencySelection}
            onClose={() => {
              console.log("Closing modal");
              setShowCurrencySelection(false);
            }}
            onSelectCurrency={handleCurrencySelection}
          />

          {/* Card Selection Modal */}
          <CardSelectionModal
            isVisible={showCardSelectionModal}
            onClose={() => setShowCardSelectionModal(false)}
            onSelectCard={handleCardSelection}
            completedOrders={completedOrdersWithoutPickup}
          />

          {/* Pickup Location Modal */}
          <PickupLocationModal
            isVisible={showPickupLocationModal}
            onClose={() => setShowPickupLocationModal(false)}
            onContinue={handlePickupLocationContinue}
            orderId={pendingOrderId}
          />
        </Div>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Div>
        <Header title="Cards" />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          style={{ width: "100%" }}
        >
          {/* Card Component */}
          {/* Card Stack Component */}
          <CardStack cards={cardData} onCardPress={handleCardPress} />
          {/* Action Buttons */}
          <View style={styles.actionButtonsContainer}>
            <Button
              btnText="Order a Physical Card"
              onPress={handleOrderPhysicalCard}
              style={styles.primaryButton}
            />
            <Button
              btnText="Get a Virtual Card"
              onPress={handleGetVirtualCard}
              style={styles.secondaryButton}
              btnTextStyle={styles.secondaryButtonText}
            />
          </View>
          {/* Activate Card Link */}
          <View style={styles.activateCardContainer}>
            <Text style={styles.activateCardText}>
              Already have a card?
              <Text
                style={styles.activateCardLink}
                onPress={handleActivateCard}
              >
                {" "}
                Activate Card
              </Text>
            </Text>
          </View>
        </ScrollView>
        <CardCurrencySelectionBottomSheet
          isVisible={showCurrencySelection}
          onClose={() => {
            console.log("Closing modal");
            setShowCurrencySelection(false);
          }}
          onSelectCurrency={handleCurrencySelection}
        />

        {/* Card Selection Modal */}
        <CardSelectionModal
          isVisible={showCardSelectionModal}
          onClose={() => setShowCardSelectionModal(false)}
          onSelectCard={handleCardSelection}
          completedOrders={completedOrdersWithoutPickup}
        />

        {/* Pickup Location Modal */}
        <PickupLocationModal
          isVisible={showPickupLocationModal}
          onClose={() => setShowPickupLocationModal(false)}
          onContinue={handlePickupLocationContinue}
          orderId={pendingOrderId}
        />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    width: "100%",
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  // Card styles are now handled by the CardStack component
  cardNumberContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  cardNumberText: {
    fontFamily: fonts.plusJBold,
    fontSize: 18,
    color: colors.white,
  },
  cardInfoRow: {
    flexDirection: "row",
    marginBottom: 10,
  },
  cardInfoLabel: {
    fontFamily: fonts.plusJRegular,
    fontSize: 12,
    color: colors.white,
    opacity: 0.7,
  },
  // Old cardHolderName style removed to avoid duplication
  cardBrandContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
  },
  cardBrandLogos: {
    flexDirection: "row",
  },
  cardBrandLogo: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginLeft: -8,
  },
  // Old circle styles removed to avoid duplication
  actionButtonsContainer: {
    width: "95%",
    alignSelf: "center",
    marginBottom: 20,
    marginTop: (15 * height) / 100,
  },
  primaryButton: {
    backgroundColor: colors.primary,
    marginBottom: 16,
  },
  secondaryButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  secondaryButtonText: {
    color: colors.textBlack,
  },
  activateCardContainer: {
    alignItems: "center",
    marginTop: 10,
  },
  activateCardText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: colors.textAsh,
  },
  activateCardLink: {
    fontFamily: fonts.plusJSemibold,
    color: colors.primary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  centeredModalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalBackdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: (14 * height) / 100,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 20,
  },
  modalHeaderContent: {
    flex: 1,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 16,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    marginBottom: 8,
    textAlign: "center",
    marginTop: 16,
  },
  modalSubtitle: {
    fontSize: 12,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    lineHeight: 20,
    textAlign: "center",
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: "#F2F2F2",
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    top: 0,
    right: 0,
  },
  closeButtonText: {
    fontSize: 16,
    color: colors.textBlack,
    fontFamily: fonts.plusJMedium,
  },

  // Currency selection styles
  currencyOptions: {
    marginBottom: 20,
  },
  currencyOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 8,
    marginBottom: 12,
  },
  selectedCurrencyOption: {
    borderColor: colors.primary,
  },
  currencyIcon: {
    width: 24,
    height: 24,
    marginRight: 12,
    borderRadius: 12,
  },
  currencyLabel: {
    flex: 1,
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
  },
  radioSelected: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  bottomSheetButtonContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
  cardImageContainer: {
    alignItems: "center",
    marginTop: (15 * height) / 100,
    marginBottom: 20,
  },
  cardImage: {
    width: 130,
    height: 130,
  },
  orderCardContent: {
    alignItems: "center",
  },
  orderCardTitle: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 24,
    color: colors.textBlack,
    marginBottom: 16,
  },
  orderCardDescription: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textAsh,
    textAlign: "center",
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  actionButtonContainer: {
    width: "100%",
    marginTop: 30,
  },
  actionButton: {
    backgroundColor: colors.primary,
    // Shadow for iOS
    shadowColor: colors.textBlack,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    // Shadow for Android
    elevation: 4,
  },

  // Card Selection Modal Styles
  cardSelectionModalContent: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 24,
    margin: 20,
    maxHeight: "80%",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  cardSelectionModalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 20,
  },
  cardSelectionModalTitle: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    flex: 1,
    textAlign: "center",
    marginRight: 30,
  },
  cardSelectionModalBody: {
    marginBottom: 24,
  },
  cardSelectionModalMessage: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 20,
  },
  cardOptions: {
    gap: 12,
  },
  cardOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 12,
    backgroundColor: colors.white,
  },
  cardOptionContent: {
    flex: 1,
  },
  cardOptionTitle: {
    fontSize: 16,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    marginBottom: 4,
  },
  cardOptionSubtitle: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
  },
  cardOptionArrow: {
    fontSize: 18,
    fontFamily: fonts.plusJMedium,
    color: colors.primary,
  },

  // Pickup Location Modal Styles
  pickupModalContent: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 24,
    margin: 20,
    maxHeight: "80%",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  pickupModalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 20,
  },
  pickupModalTitle: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    flex: 1,
    textAlign: "center",
    marginRight: 30,
  },
  pickupModalBody: {
    alignItems: "center",
    marginBottom: 24,
  },
  pickupModalImage: {
    width: 120,
    height: 120,
    marginBottom: 20,
  },
  pickupModalMessage: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 12,
  },
  pickupModalSubMessage: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    textAlign: "center",
    lineHeight: 20,
  },
  pickupModalButtons: {
    gap: 12,
  },
  pickupContinueButton: {
    backgroundColor: colors.primary,
  },
  pickupLaterButton: {
    alignItems: "center",
    paddingVertical: 12,
  },
  pickupLaterText: {
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.textAsh,
  },
});
