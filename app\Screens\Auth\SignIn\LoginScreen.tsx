import React, { useContext, useState } from "react";
import { storeUserEmail } from "../../../utils/userEmail";
import { useUpdateNextStep } from "../../../utils/updateNextStep";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Keyboard,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import Input from "../../../Components/Input";
import * as yup from "yup";
import { Formik } from "formik";
import { useToast } from "../../../Context/ToastContext";
import { Login } from "../../../RequestHandler.tsx/Auth";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../../../Context/CredentailsContext";

const { width, height } = Dimensions.get("window");

export default function LoginScreen({ navigation }) {
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const { setStoredCredentails } = useContext(CredentailsContext);
  const { updateUserNextStep } = useUpdateNextStep();

  // Define validation schema
  const loginSchema = yup.object().shape({
    email: yup
      .string()
      .email("Please enter a valid email address")
      .required("Email is required"),
    password: yup.string().required("Password is required"),
  });

  const persistLogin = (credentail: any) => {
    // Store credentials without timestamp since we're using JWT expiration
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(() => {
        // If the credential doesn't have a nextStep but has currentOnboardingStep,
        // we can set the nextStep based on the currentOnboardingStep
        if (!credentail.nextStep && credentail.data?.currentOnboardingStep) {
          const step = credentail.data.currentOnboardingStep;
          let nextStep = "";
          // Map currentOnboardingStep to appropriate nextStep
          switch (step) {
            case "kyc_not_verified":
              nextStep = "/auth/onboarding/kyc/verify";
              break;
            case "documents_not_uploaded":
              nextStep = "/auth/onboarding/documents/upload";
              break;
            case "pin_pending":
              nextStep = "/auth/onboarding/pin/create";
              break;
            // Add more cases as needed
          }
          setStoredCredentails({...credentail, nextStep});
          if (nextStep) {
            // Update both nextStep and currentOnboardingStatus
            updateUserNextStep(nextStep);
          }
        } else {
          // Set the credentials in the context
          setStoredCredentails(credentail);
        }
      })
      .catch((err) => {
        console.error("Error persisting login:", err);
      });
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            email: "",
            password: "",
          }}
          validationSchema={loginSchema}
          onSubmit={async (values) => {
            Keyboard.dismiss();
            setLoading(true);
            try {
              // Store the user's email in AsyncStorage
              await storeUserEmail(values.email);

              const res = await Login(values);
              persistLogin(res);
            } catch (error) {
              console.log(error);
              handleToast(error.message, "error");
            } finally {
              setLoading(false);
            }
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                automaticallyAdjustContentInsets={true}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                  alignItems: "center",
                  minHeight: "90%",
                  paddingHorizontal: 20,
                }}
              >
                <View style={styles.contentContainer}>
                  <H4 style={styles.mainTitle}>Welcome back!</H4>
                  <P style={styles.subtitle}>
                    We are happy to see you again. Please enter your email
                    address and password
                  </P>

                  <View style={styles.formContainer}>
                    <View style={styles.inputGroup}>
                      <Input
                        label={"Email address"}
                        placeholder="Enter your email address"
                        value={formikProps.values.email}
                        onChangeText={formikProps.handleChange("email")}
                        onBlur={formikProps.handleBlur("email")}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        error={
                          formikProps.errors.email && formikProps.touched.email
                        }
                        errorText={formikProps.errors.email}
                      />
                    </View>

                    <View style={styles.inputGroup}>
                      <Input
                        label={"Password"}
                        placeholder="Enter your password"
                        value={formikProps.values.password}
                        onChangeText={formikProps.handleChange("password")}
                        onBlur={formikProps.handleBlur("password")}
                        type="password"
                        autoCapitalize="none"
                        error={
                          formikProps.errors.password &&
                          formikProps.touched.password
                        }
                        errorText={formikProps.errors.password}
                      />
                    </View>

                    <View style={styles.forgotPasswordContainer}>
                      <TouchableOpacity
                        onPress={() =>
                          navigation.navigate("ForgotPasswordScreen")
                        }
                      >
                        <Text style={styles.forgotPasswordText}>
                          Forgot password?
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View style={styles.buttonContainer}>
                    <Button
                      btnText="Sign In"
                      onPress={formikProps.handleSubmit}
                      loading={loading}
                      style={styles.signInButton}
                    />
                  </View>
                </View>

                <View style={styles.footerContainer}>
                  <View style={styles.registerContainer}>
                    <Text style={styles.accountText}>
                      Don't have an account?
                    </Text>
                    <TouchableOpacity
                      onPress={() => navigation.navigate("SignUpScreen1")}
                    >
                      <Text style={styles.registerText}>Register</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </ScrollView>
            </View>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    width: "100%",
    flex: 1,
    marginTop: (5 * height) / 100,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  formContainer: {
    width: "100%",
  },
  inputGroup: {
    marginBottom: 24,
  },
  forgotPasswordContainer: {
    width: "100%",
    alignItems: "flex-end",
  },
  forgotPasswordText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: colors.textAsh,
  },
  buttonContainer: {
    width: "100%",
    marginTop: (15 * height) / 100,
  },
  signInButton: {
    backgroundColor: colors.primary,
  },
  footerContainer: {
    width: "100%",
    alignItems: "center",
    marginTop: "auto",
  },
  registerContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  accountText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: "#666",
  },
  registerText: {
    fontFamily: fonts.plusJBold,
    fontSize: 14,
    color: colors.black,
    marginLeft: 5,
  },
});
