import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Dimensions, Platform, View } from "react-native";
import { fonts } from "../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";
// import SettingsScreen from "../screens/SettingsScreen";
import P from "../Components/P";
import HomeScreen from "../Screens/HomeScreen";
import { colors } from "../Config/colors";
import CardScreen from "../Screens/Card/CardScreen";
import WalletScreen from "../Screens/Wallet/WalletScreen";
import ProfileScreen from "../Screens/ProfileScreen";
const { width, height } = Dimensions.get("window");

const BottomTabNavigator = () => {
  const Tab = createBottomTabNavigator();
  return (
    <Tab.Navigator id={undefined}
      screenOptions={{
        // tabBarActiveTintColor: "#fff",
        tabBarHideOnKeyboard: true,
        // tabBarInactiveTintColor: "gray",
        tabBarStyle: [
          {
            position: "absolute",
            height: 72,
            paddingBottom: Platform.OS === "ios" ? (2.5 * height) / 100 : 10,
            paddingTop: 10,
            backgroundColor: "#FEF9ED",
            justifyContent: "space-around",
            paddingLeft: (3 * width) / 100,
            paddingRight: (3 * width) / 100,
            borderTopWidth: 1,
            borderColor: "#BF8708"
          },
          Platform.OS === "ios"
            ? {
                shadowColor: "#00000021",
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.5,
                shadowRadius: 4,
              }
            : { elevation: 20 },
        ],
        tabBarLabelStyle: {
          fontFamily: fonts.plusJMedium,
          fontSize: 11,
          // marginTop: 4,
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen} // Ensure this matches the screen component name
        options={{
          headerShown: false,
          tabBarIcon: () => null,
          tabBarLabel: ({ focused }) => (
            <View
              style={{
                alignItems: "center",
                justifyContent: "center",
                position: "absolute",
                top: focused ? 8: 16,
              }}
            >
              <SvgXml xml={focused ? svg.homeActive : svg.home} />
              {focused && (
                <P
                  style={{
                    color: "#735105",
                    fontFamily: fonts.plusJMedium,
                    fontSize: 12,
                    textAlign: "center",
                  }}
                >
                  Home
                </P>
              )}
            </View>
          ),
        }}
      />
        <Tab.Screen
          name="Card"
          component={CardScreen} // Ensure this matches the screen component name
          options={{
            headerShown: false,
            tabBarIcon: () => null,
            tabBarLabel: ({ focused }) => (
              <View
                style={{
                  alignItems: "center",
                  justifyContent: "center",
                  position: "absolute",
                  top: focused ? 8: 16,
                }}
              >
                <SvgXml xml={focused ? svg.cardActive : svg.card} />
                {focused && (
                  <P
                    style={{
                      color: "#735105",
                      fontFamily: fonts.plusJMedium,
                      fontSize: 12,
                      textAlign: "center",
                    }}
                  >
                    Card
                  </P>
                )}
              </View>
            ),
          }}
        />
      <Tab.Screen
        name="Wallet"
        component={WalletScreen} // Ensure this matches the screen component name
        options={{
          headerShown: false,
          tabBarIcon: () => null,
          tabBarLabel: ({ focused }) => (
            <View
              style={{
                alignItems: "center",
                justifyContent: "center",
                position: "absolute",
                top: focused ? 8: 16,
              }}
            >
              <SvgXml xml={focused ? svg.tHubActive : svg.tHUb} />
              {focused && (
                <P
                  style={{
                    color: "#735105",
                    fontFamily: fonts.plusJMedium,
                    fontSize: 12,
                    textAlign: "center",
                  }}
                >
                  Transactions
                </P>
              )}
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          headerShown: false,
          tabBarIcon: () => null,
          tabBarLabel: ({ focused }) => (
            <View
              style={{
                alignItems: "center",
                justifyContent: "center",
                position: "absolute",
                top: focused ? 8: 16,
              }}
            >
              <SvgXml xml={focused ? svg.profileActive : svg.profile} />
              {focused && (
                <P
                  style={{
                    color: "#735105",
                    fontFamily: fonts.plusJMedium,
                    fontSize: 12,
                    textAlign: "center",
                  }}
                >
                  Profile
                </P>
              )}
            </View>
          ),
        }}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;
