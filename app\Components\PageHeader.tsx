import React from "react";
import {
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  Dimensions,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import Icon from "react-native-vector-icons/Ionicons";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";
import P from "./P";

const { width } = Dimensions.get("window");

interface PageHeaderProps {
  currentPage?: number;
  totalPages?: number;
  onBack?: () => void;
  showBackButton?: boolean;
  type?: "bars" | "dots";
  progressPercent?: string;
}

export default function PageHeader({
  currentPage,
  totalPages,
  onBack,
  showBackButton = true,
  type = "dots",
  progressPercent,
}: PageHeaderProps) {
  const dots = Array(totalPages).fill(0);

  return (
    <View style={styles.headerContainer}>
      <View
        style={[
          styles.headerContent,
          { flexDirection: type === "bars" ? "column" : "row" },
        ]}
      >
        <>
          {type === "bars" ? (
            <View
              style={{
                width: "100%",
                flexDirection: "row",
                justifyContent: showBackButton ? "space-between" : "flex-end",
                marginBottom: 16,
              }}
            >
              {showBackButton && (
                <TouchableOpacity style={styles.backButton} onPress={onBack}>
                  <SvgXml xml={svg.arrowLeft} />
                </TouchableOpacity>
              )}
              <P
                style={{
                  fontSize: 14,
                  color: "#6B7280",
                  fontFamily: fonts.plusJRegular,
                }}
              >
                Step {`${currentPage} of ${totalPages}`}
              </P>
            </View>
          ) : (
            <>
              {showBackButton && (
                <TouchableOpacity style={styles.backButton} onPress={onBack}>
                  <SvgXml xml={svg.arrowLeft} />
                </TouchableOpacity>
              )}
            </>
          )}
        </>
        <>
          {type === "bars" ? (
            <View
              style={{
                width: "100%",
                height: 8,
                backgroundColor: colors.grayLight,
                borderRadius: 100,
              }}
            >
              <View
                style={{
                  height: "100%",
                  width: `${(100 / totalPages) * currentPage}%`,
                  backgroundColor: colors.primary,
                  borderRadius: 100
                }}
              ></View>
            </View>
          ) : (
            <>
              {totalPages && (
                <View style={styles.paginationContainer}>
                  {dots.map((_, index) => (
                    <View
                      key={index}
                      style={[
                        styles.paginationDot,
                        currentPage === index && styles.activeDot,
                      ]}
                    />
                  ))}
                </View>
              )}
            </>
          )}
        </>

        <View style={styles.placeholder} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  paginationContainer: {
    flexDirection: "row",
    justifyContent: "center",
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.grayLight,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: colors.primary,
    width: 50,
  },
  placeholder: {
    width: 40,
  },
});
