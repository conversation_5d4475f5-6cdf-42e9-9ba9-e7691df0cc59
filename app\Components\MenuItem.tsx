import React from "react";
import {
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";

interface MenuItemProps {
  icon: string;
  label: string;
  onPress: () => void;
}

const MenuItem = ({ icon, label, onPress }: MenuItemProps) => {
  return (
    <TouchableOpacity style={styles.menuItem} onPress={onPress}>
      <ImageBackground
        source={require("../assets/menu-board-bg.png")}
        style={styles.menuBg}
        resizeMode="cover"
      >
        <View style={{ padding: 15 }}>
          <SvgXml xml={icon} width={24} height={24} />
          <Text style={styles.menuLabel}>{label}</Text>
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  menuItem: {
    width: "48%",
    height: 160,
    backgroundColor: "#F7C148",
    borderRadius: 16,
    overflow: "hidden"
  },
  menuLabel: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 16,
    marginTop: 8,
    color: colors.textBlack,
  },
  menuBg: {
    width: "100%",
    height: "100%",
    justifyContent: "flex-end",
  },
});

export default MenuItem;
