import { createContext } from "react";

// Define the type for stored credentials
export interface StoredCredentials {
  token?: string;
  nextStep?: string;
  currentOnboardingStep?: string;
  data?: any;
  loginTimestamp?: number; // Timestamp when the user logged in
  // Add other properties that might exist in your credentials
}

// Define the type for the context
interface CredentialsContextType {
  storedCredentails: StoredCredentials | null;
  setStoredCredentails: (credentials: StoredCredentials | null) => void;
  updateNextStep: (nextStep: string, currentOnboardingStatus?: string) => Promise<void>;
}

// Create the context with proper typing
export const CredentailsContext = createContext<CredentialsContextType>({
  storedCredentails: null,
  setStoredCredentails: () => {},
  updateNextStep: async () => {},
});
