import React, { useState, useRef } from "react";
import {
  Dimensions,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
} from "react-native";
import { colors } from "../Config/colors";
import Div from "../Components/Div";
import H4 from "../Components/H4";
import P from "../Components/P";
import { fonts } from "../Config/Fonts";
import Button from "../Components/Button";

const { width, height } = Dimensions.get("window");
const MAX_WIDTH = 500;

export default function OnboardingScreen({ navigation }) {
  const [currentPage, setCurrentPage] = useState(0);
  const scrollViewRef = useRef(null);

  const onboardingData = [
    {
      image: require("../assets/onImg1.png"), // Placeholder - replace with actual asset
      title: "Send, Receive and Spend money globally",
      subtitle:
        "With <PERSON><PERSON>'s virtual USD accounts, you can easily receive payments from anywhere in any currency.",
    },
    {
      image: require("../assets/onImg2.png"), // Placeholder - replace with actual asset
      title: "Create Your Invoice on Getly",
      subtitle:
        "Send invoices to your clients straight from the getly app, and receive your payment once it's sent.",
    },
    {
      image: require("../assets/onImg3.png"), // Placeholder - replace with actual asset
      title: "Manage your virtual cards conveniently",
      subtitle:
        "Get local cards for global payments and make purchases conveniently.",
    },
    {
      image: require("../assets/onImg4.png"), // Placeholder - replace with actual asset
      title: "Focus on growing your business",
      subtitle:
        "Struggling with slow transfers and high fees? Getly solves these problems for African digital workers and businesses.",
    },
  ];

  const handleScroll = (event) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffsetX / width);
    setCurrentPage(currentIndex);
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          {/* Pagination indicators */}
          <View style={styles.paginationContainer}>
            {onboardingData.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.paginationDot,
                  currentPage === index && styles.activeDot,
                ]}
              />
            ))}
          </View>

          <Text style={styles.swipeText}>Swipe to learn more</Text>

          {/* Carousel */}
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            style={styles.carouselContainer}
          >
            {onboardingData.map((item, index) => (
              <View key={index} style={styles.slide}>
                {/* Image container */}
                <View style={styles.imageContainer}>
                  {index === 2 ? (
                    <>
                      <View style={styles.altB}>
                        <Image
                          source={require("../assets/cTop.png")}
                          style={{
                            objectFit: "contain",
                            width: 100,
                            position: "absolute",
                            top: (-10 * height) / 100,
                            right: 0,
                          }}
                        />
                        <Image
                          source={require("../assets/handPhone.png")}
                          style={{
                            width: (70 * width) / 100,
                            height: (70 * width) / 100,
                            objectFit: "contain",
                          }}
                        />
                        <Image
                          source={require("../assets/cB.png")}
                          style={{
                            objectFit: "contain",
                            width: 100,
                            position: "absolute",
                            bottom: (-8 * height) / 100,
                            left: 0,
                          }}
                        />
                      </View>
                    </>
                  ) : (
                    <Image
                      source={item.image}
                      style={[
                        styles.mockupImage,
                        {
                          objectFit: index === 1 ? "cover" : "contain",
                        },
                      ]}
                      //   resizeMode="contain"
                    />
                  )}
                </View>

                {/* Content section */}
                <View style={styles.contentContainer}>
                  <H4 style={styles.mainTitle}>{item.title}</H4>
                  <P style={styles.subtitle}>{item.subtitle}</P>
                </View>
              </View>
            ))}
          </ScrollView>

          {/* Buttons */}
          <View style={styles.buttonsContainer}>
            <Button
              btnText="Get Started"
              onPress={() => navigation.navigate("SignUpScreen1")}
              style={styles.getStartedButton}
            />
            <Button
              style={{ backgroundColor: colors.primarySubtle }}
              btnText="Login"
              type="alt"
              onPress={() => navigation.navigate("LoginScreen")}
            />
          </View>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  statusBarArea: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  timeText: {
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
  },
  statusIcons: {
    flexDirection: "row",
  },
  statusIconText: {
    fontSize: 14,
  },
  paginationContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: (5 * height) / 100,
    marginBottom: 16,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.grayLight,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: colors.primary,
    width: 50,
  },
  swipeText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 12,
    color: "#888",
    textAlign: "center",
    // marginBottom: 16,
  },
  carouselContainer: {
    flex: 1,
    width: Math.min(width, MAX_WIDTH),
  },
  slide: {
    width: Math.min(width, MAX_WIDTH),
    alignItems: "center",
    paddingHorizontal: width * 0.05,
  },
  imageContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: (3 * height) / 100,
    width: "100%",
  },
  mockupImage: {
    width: "100%",
    borderRadius: 20,
    height: (40 * height) / 100,
    objectFit: "contain",
  },
  altB: {
    width: Math.min(width, MAX_WIDTH),
    height: (40 * height) / 100,
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
  },
  contentContainer: {
    width: "100%",
    alignItems: "flex-start",
    marginTop: (3 * height) / 100,
    paddingRight: 50,
  },
  mainTitle: {
    fontFamily: fonts.plusJBold,
    fontSize: 24,
    textAlign: "left",
    marginBottom: 10,
    color: colors.textBlack,
  },
  subtitle: {
    textAlign: "left",
    marginBottom: 20,
    fontSize: 16,
    color: "#666",
    lineHeight: 22,
  },
  buttonsContainer: {
    width: "90%",
    marginBottom: 19,
  },
  getStartedButton: {
    width: "100%",
    backgroundColor: "#F5B740",
    marginBottom: 12,
  },
  loginButton: {
    width: "100%",
    backgroundColor: "#FFF9EA", // Light yellow background
    borderRadius: 50,
    padding: 15,
    alignItems: "center",
    marginBottom: 20,
  },
});
