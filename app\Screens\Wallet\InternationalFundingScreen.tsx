import React from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";

const InternationalFundingScreen: React.FC = () => {
  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={styles.scrollViewContent}>
        <Header title="International Funding" />
        <View style={styles.content}>
          <P style={styles.title}>International Funding Options</P>
          <P style={styles.description}>
            Here you can find information on how to fund your wallet from outside Nigeria.
            Please select your preferred method below.
          </P>
          {/* TODO: Add specific international funding options/instructions here */}
          <View style={styles.placeholderBox}>
            <P>International funding methods will be listed here.</P>
            {/* Examples: Bank transfer details for international users, card payment options, etc. */}
          </View>
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 32,
  },
  content: { flex: 1, padding: 20 },
   title: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 18,
    color: colors.textBlack,
    marginBottom: 8,
  },
  description: {
    fontFamily: fonts.plusJRegular,
    fontSize: 15,
    color: "#888",
    marginBottom: 20,
  },
  placeholderBox: {
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 150,
  }
});

export default InternationalFundingScreen; 