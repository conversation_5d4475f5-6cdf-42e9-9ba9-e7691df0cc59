import React, { useEffect, useState, useRef } from "react";
import {
  View,
  StyleSheet,
  Animated,
  ActivityIndicator,
  Modal,
  Dimensions,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import P from "./P";
import H4 from "./H4";

interface LoaderProps {
  visible?: boolean;
  type?: "dots" | "spinner" | "inline" | "overlay";
  message?: string;
  size?: "small" | "medium" | "large";
  color?: string;
  style?: any;
}

const { width } = Dimensions.get("window");

const Loader: React.FC<LoaderProps> = ({
  visible = true,
  type = "spinner",
  message,
  size = "medium",
  color = colors.primary,
  style,
}) => {
  const [activeDot, setActiveDot] = useState(0);
  const dotsArray = [1, 2, 3, 4];

  // Create animated values for each dot
  const animations = useRef(dotsArray.map(() => new Animated.Value(1)));
  const animationValues = animations.current;

  useEffect(() => {
    if (visible && type === "dots") {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length);

        // Animate the active dot
        Animated.sequence([
          Animated.timing(animationValues[activeDot], {
            toValue: 1.5, // Increase size by 1.5x
            duration: 200,
            useNativeDriver: false,
          }),
          Animated.timing(animationValues[activeDot], {
            toValue: 1, // Return to original size
            duration: 200,
            useNativeDriver: false,
          }),
        ]).start();
      }, 300);

      return () => clearInterval(interval);
    }
  }, [visible, activeDot, type]);

  // Get size values based on size prop
  const getSizeValues = () => {
    switch (size) {
      case "small":
        return {
          spinnerSize: "small" as const,
          dotSize: 12,
          containerHeight: 40,
          fontSize: 14,
        };
      case "large":
        return {
          spinnerSize: "large" as const,
          dotSize: 20,
          containerHeight: 80,
          fontSize: 18,
        };
      default: // medium
        return {
          spinnerSize: 30,
          dotSize: 16,
          containerHeight: 60,
          fontSize: 16,
        };
    }
  };

  const sizeValues = getSizeValues();

  // Dots Loader Component
  const DotsLoader = () => (
    <View style={[styles.dotsContainer, { height: sizeValues.containerHeight }]}>
      {dotsArray.map((dot, index) => (
        <Animated.View
          key={dot}
          style={[
            styles.dot,
            {
              width: sizeValues.dotSize,
              height: sizeValues.dotSize,
              backgroundColor: color,
              transform: [{ scale: animationValues[index] }],
            },
          ]}
        />
      ))}
    </View>
  );

  // Spinner Loader Component
  const SpinnerLoader = () => (
    <View style={[styles.spinnerContainer, { height: sizeValues.containerHeight }]}>
      <ActivityIndicator size={sizeValues.spinnerSize} color={color} />
    </View>
  );

  // Inline Loader (for use within other components)
  const InlineLoader = () => (
    <View style={[styles.inlineContainer, style]}>
      {type === "dots" ? <DotsLoader /> : <SpinnerLoader />}
      {message && (
        <P style={[styles.message, { fontSize: sizeValues.fontSize }]}>
          {message}
        </P>
      )}
    </View>
  );

  // Overlay Loader (modal with backdrop)
  const OverlayLoader = () => (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      statusBarTranslucent
    >
      <View style={styles.overlayContainer}>
        <View style={styles.overlayContent}>
          {type === "dots" ? <DotsLoader /> : <SpinnerLoader />}
          {message && (
            <H4 style={[styles.overlayMessage, { fontSize: sizeValues.fontSize }]}>
              {message}
            </H4>
          )}
        </View>
      </View>
    </Modal>
  );

  // Don't render anything if not visible (for inline types)
  if (!visible && (type === "inline" || type === "spinner" || type === "dots")) {
    return null;
  }

  // Render based on type
  switch (type) {
    case "overlay":
      return <OverlayLoader />;
    case "inline":
    case "spinner":
    case "dots":
    default:
      return <InlineLoader />;
  }
};

const styles = StyleSheet.create({
  // Dots styles
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  dot: {
    borderRadius: 50,
    marginHorizontal: 4,
  },

  // Spinner styles
  spinnerContainer: {
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },

  // Inline styles
  inlineContainer: {
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  message: {
    marginTop: 12,
    textAlign: "center",
    color: colors.textAsh,
    fontFamily: fonts.plusJRegular,
  },

  // Overlay styles
  overlayContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  overlayContent: {
    backgroundColor: colors.white,
    borderRadius: 16,
    alignItems: "center",
    minWidth: width * 0.2,
    maxWidth: width * 0.8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  overlayMessage: {
    marginTop: 16,
    textAlign: "center",
    color: colors.textBlack,
    fontFamily: fonts.plusJSemibold,
  },
});

export default Loader;
