import React from "react";
import {
  StyleProp,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import { svg } from "../Config/Svg";
import { useNavigation } from "@react-navigation/native";

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  showNotification?: boolean;
  onBackPress?: () => void;
  onNotificationPress?: () => void;
  contStyle?: StyleProp<ViewStyle>;
}

const Header = ({
  title,
  showBackButton = true,
  showNotification = true,
  onBackPress,
  onNotificationPress,
  contStyle,
}: HeaderProps) => {
  const navigation = useNavigation<any>();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.header, contStyle]}>
      {showBackButton ? (
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <SvgXml xml={svg.goBack} width={24} height={24} />
        </TouchableOpacity>
      ) : (
        <View style={styles.emptySpace} />
      )}
      <Text style={styles.headerTitle}>{title}</Text>
      {showNotification ? (
        <TouchableOpacity
          style={styles.notificationButton}
          onPress={onNotificationPress}
        >
          <SvgXml xml={svg.notification} width={24} height={24} />
        </TouchableOpacity>
      ) : (
        <View style={styles.emptySpace} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    width: "100%",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#FDECC8",
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 18,
    color: colors.textBlack,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#FDECC8",
    justifyContent: "center",
    alignItems: "center",
  },
  emptySpace: {
    width: 40,
  },
});

export default Header;
