import { Country as CSCCountry } from 'country-state-city';

export interface Country {
  label: string;
  value: string;
  code: string;
  flagUrl?: string;
  isoCode: string;
}

// Get all countries from country-state-city library and format them
export const countries: Country[] = CSCCountry.getAllCountries().map(country => ({
  label: country.name,
  value: country.isoCode,
  code: country.isoCode,
  isoCode: country.isoCode,
  flagUrl: `https://flagcdn.com/w2560/${country.isoCode.toLowerCase()}.png`
})).sort((a, b) => a.label.localeCompare(b.label));

// Function to get country by value
export const getCountryByValue = (value: string): Country | undefined => {
  return countries.find(country => country.value === value);
};

// Function to get country by label
export const getCountryByLabel = (label: string): Country | undefined => {
  return countries.find(country => country.label.toLowerCase() === label.toLowerCase());
};
