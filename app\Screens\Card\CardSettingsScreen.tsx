import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ImageBackground,
  ScrollView,
  BackHandler,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Icon from "react-native-vector-icons/Feather";
import { useFocusEffect } from '@react-navigation/native';

const { width, height } = Dimensions.get("window");

interface CardSettingsScreenProps {
  navigation: any;
}

export default function CardSettingsScreen({
  navigation,
}: CardSettingsScreenProps) {
 
  // Set up back button handler

  // We're using our custom Header component with onBackPress,
  // so we don't need to override the default header

  // Navigate to view card PIN
  const navigateToViewCardPIN = () => {
    navigation.navigate("VerifyPINScreen", { action: "view" });
  };

  // Navigate to change card PIN
  const navigateToChangeCardPIN = () => {
    navigation.navigate("VerifyPINScreen", { action: "change" });
  };

  return (
    <View style={styles.container}>
      <Div>
        <Header
          title="Card Settings"
          showNotification={true}
        />
        <ScrollView style={{ width: "95%" }}>
          <View style={styles.content}>
            {/* Card Preview */}
            <View style={styles.cardContainer}>
              <View style={styles.card}>
                <ImageBackground
                  source={require("../../assets/bg-background.png")}
                  style={{
                    width: "60%",
                    height: "110%",
                    position: "absolute",
                    right: -90,
                  }}
                  resizeMode="cover"
                />
                <View style={styles.cardHeader}>
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      gap: 16,
                    }}
                  >
                    <Text style={styles.cardHolderName}>Anita Amadi</Text>
                    <Text style={styles.cardExpiry}>07/25</Text>
                  </View>
                  <TouchableOpacity style={styles.eyeIcon}>
                    <Icon name="eye" size={20} color="#DF7400" />
                  </TouchableOpacity>
                </View>
                <Text style={styles.cardCvv}>CVV: 654</Text>
                <Text style={styles.cardNumber}>1234 1234 1234 1234</Text>
                <View style={styles.cardBrand}>
                  <Text style={styles.cardBalance}>NGN 0.00</Text>
                  <View style={styles.masterCardIcon}>
                    <View style={[styles.circle, styles.redCircle]} />
                    <View style={[styles.circle, styles.yellowCircle]} />
                  </View>
                </View>
              </View>
            </View>

            {/* Card PIN Options */}
            <View style={styles.pinOptions}>
              <TouchableOpacity
                style={styles.optionItem}
                onPress={navigateToViewCardPIN}
              >
                <Text style={styles.optionText}>View Card PIN</Text>
                <Icon name="chevron-right" size={20} color="#333" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.optionItem}
                onPress={navigateToChangeCardPIN}
              >
                <Text style={styles.optionText}>Change Card PIN</Text>
                <Icon name="chevron-right" size={20} color="#333" />
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  cardContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 20,
  },
  card: {
    width: "100%",
    minHeight: 215,
    backgroundColor: colors.cardColor,
    borderRadius: 12,
    padding: 16,
    position: "relative",
    overflow: "hidden",
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  cardHolderName: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardExpiry: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  eyeIcon: {
    padding: 4,
  },
  cardCvv: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginBottom: 8,
    marginTop: 10,
  },
  cardNumber: {
    fontSize: 18,
    fontFamily: fonts.plusJBold,
    color: colors.textBlack,
    marginTop: 8,
    letterSpacing: 1,
  },
  cardBalance: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardBrand: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    position: "absolute",
    bottom: 16,
    right: 16,
  },
  masterCardIcon: {
    flexDirection: "row",
    alignItems: "center",
  },
  circle: {
    width: 31,
    height: 31,
    borderRadius: 100,
  },
  redCircle: {
    backgroundColor: "#EB001B",
    marginRight: -8,
  },
  yellowCircle: {
    backgroundColor: "#F79E1B",
  },
  pinOptions: {
    marginTop: 10,
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    backgroundColor: "#F9F9FA",
    borderRadius: 8,
    marginBottom: 8,
    paddingHorizontal: 12,
  },
  optionText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
});
