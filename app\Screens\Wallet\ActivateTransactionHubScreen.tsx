import React, { useEffect, useState } from "react";
import { View, StyleSheet, ScrollView, Keyboard } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Input from "../../Components/Input";
import Button from "../../Components/Button";
import Div from "../../Components/Div";
import SelectInput from "../../Components/SelectInput";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { Formik } from "formik";
import * as yup from "yup";
import { countries } from "../../data/countries";
import { getStatesByCountry } from "../../data/states";
import { CreateWalletForNigerains } from "../../RequestHandler.tsx/Wallet";
import { useToast } from "../../Context/ToastContext";
import AsyncStorage from "@react-native-async-storage/async-storage";

const validationSchema = yup.object().shape({
  country: yup.string().required("Country is required"),
  state: yup.string().required("State is required"),
  streetAddress: yup.string().required("Street Address is required"),
  city: yup.string().required("City is required"),
  postalCode: yup.string().required("Postal Code is required"),
  phoneNumber: yup
    .string()
    .required("Phone number is required")
    .matches(/^\+?[1-9]\d{1,14}$/, "Please enter a valid phone number"),
});

interface ActivateTransactionHubScreenProps {
  navigation: any;
  route: any
}

const ActivateTransactionHubScreen: React.FC<
  ActivateTransactionHubScreenProps
> = ({ navigation, route }) => {
  const [selectedCountry, setSelectedCountry] = useState("");
  const [availableStates, setAvailableStates] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const { handleToast } = useToast();

  // Extract country from route params safely
  const country = route?.params?.country || "";

  // Auto-fill Nigeria when country is NGA
  useEffect(() => {
    if (country === "NGA") {
      setSelectedCountry("NG");
      setAvailableStates(getStatesByCountry("NG"));
      console.log("Auto-filled Nigeria for NGA user");
    }
  }, [country]);

  // Handle keyboard visibility
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setIsKeyboardVisible(true);
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsKeyboardVisible(false);
    });

    // Cleanup listeners
    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  return (
    <Div>
      <ScrollView
        style={{ width: "100%" }}
        contentContainerStyle={[
          styles.scrollViewContent,
          { paddingBottom: isKeyboardVisible ? 32 : 100 }
        ]}
        automaticallyAdjustKeyboardInsets={true}
      >
        <Header
          title="Activate your transaction hub"
          showNotification={false}
        />
        {country !== "NGA" && <View style={{ paddingHorizontal: 16 }}> <P>Please provide the address where you'll be staying in the country you're visiting.</P></View>}
        <View style={styles.content}>

          <Formik
            initialValues={{
              country: country === "NGA" ? "NG" : "",
              state: "",
              streetAddress: "",
              city: "",
              postalCode: "",
              phoneNumber: "",
            }}
            validationSchema={validationSchema}
            onSubmit={async (values) => {
              setLoading(true);
              try {
                const requestBody = {
                  currency: "NGN", // Fixed currency for Nigerian users
                  billingAddress: {
                    line1: values.streetAddress.trim(),
                    city: values.city.trim(),
                    state: values.state.trim(),
                    postalCode: values.postalCode.trim(),
                  },
                  phoneNumber: values.phoneNumber.trim(),
                };
                console.log("Creating wallet with data:", requestBody);
                const response = await CreateWalletForNigerains(requestBody);
                console.log("Wallet creation response:", response);
                handleToast("Successfull", "success");
                navigation.goBack();
              } catch (error) {
                console.error("Error creating wallet:", error);
                handleToast(
                  error?.message ||
                  "Failed to create wallet. Please try again.",
                  "error"
                );
              } finally {
                setLoading(false);
              }
            }}
          >
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
              setFieldValue,
            }) => (
              <View style={{ width: "100%" }}>
                <SelectInput
                  label="Country"
                  placeholder="Select your Country"
                  value={values.country}
                  options={countries.map((country) => ({
                    label: country.label,
                    value: country.value,
                    flagUrl: country.flagUrl,
                  }))}
                  onSelect={(option) => {
                    setFieldValue("country", option.value);
                    setSelectedCountry(option.value);
                    setAvailableStates(getStatesByCountry(option.value));
                    // Reset state when country changes
                    setFieldValue("state", "");
                  }}
                  error={!!errors.country && touched.country}
                  errorText={errors.country}
                  showFlags={true}
                />
                <SelectInput
                  label="State"
                  placeholder="Select State"
                  value={values.state}
                  options={availableStates.map((state) => ({
                    label: state.label,
                    value: state.value,
                  }))}
                  onSelect={(option) => {
                    setFieldValue("state", option.value);
                  }}
                  error={!!errors.state && touched.state}
                  errorText={errors.state}
                  contStyle={{ marginTop: 16 }}
                  disabled={!selectedCountry}
                />
                <Input
                  label={"Street Address"}
                  placeholder="Enter Street Address"
                  value={values.streetAddress}
                  onChangeText={handleChange("streetAddress")}
                  onBlur={handleBlur("streetAddress")}
                  error={!!errors.streetAddress && touched.streetAddress}
                  errorText={errors.streetAddress}
                  contStyle={{ marginTop: 16 }}
                />

                <View style={styles.row}>
                  <View style={styles.halfInput}>
                    <Input
                      label={"City"}
                      placeholder="Enter City"
                      value={values.city}
                      onChangeText={handleChange("city")}
                      onBlur={handleBlur("city")}
                      error={!!errors.city && touched.city}
                      errorText={errors.city}
                      contStyle={{ marginTop: 16 }}
                    />
                  </View>
                  <View style={styles.halfInput}>
                    <Input
                      label={"Postal Code"}
                      placeholder="Enter Postal Code"
                      value={values.postalCode}
                      onChangeText={handleChange("postalCode")}
                      onBlur={handleBlur("postalCode")}
                      keyboardType="numeric"
                      error={!!errors.postalCode && touched.postalCode}
                      errorText={errors.postalCode}
                      contStyle={{ marginTop: 16 }}
                    />
                  </View>
                </View>

                <Input
                  label={"Phone Number"}
                  placeholder="Enter phone number (e.g., +234...)"
                  value={values.phoneNumber}
                  onChangeText={handleChange("phoneNumber")}
                  onBlur={handleBlur("phoneNumber")}
                  error={!!errors.phoneNumber && touched.phoneNumber}
                  errorText={errors.phoneNumber}
                  contStyle={{ marginTop: 16 }}
                  keyboardType="phone-pad"
                />

                {!isKeyboardVisible && (
                  <Button
                    btnText={"Create wallet"}
                    loading={loading}
                    onPress={handleSubmit}
                    style={styles.createWalletBtn}
                  />
                )}
              </View>
            )}
          </Formik>
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  scrollViewContent: {
    flexGrow: 1,
  },
  content: { flex: 1, padding: 20 },
  label: {
    fontFamily: fonts.plusJMedium,
    fontSize: 15,
    color: colors.textBlack,
    marginBottom: 8,
    marginTop: 12,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  halfInput: {
    width: "48%",
  },
  createWalletBtn: {
    marginTop: 32,
    backgroundColor: "#FBC94A",
    borderRadius: 8,
  },
});

export default ActivateTransactionHubScreen;
