import { useState, useCallback } from 'react';

interface ApiRequestState {
  loading: boolean;
  error: any;
  data: any;
  isNetworkError: boolean;
  isTimeoutError: boolean;
  isServerError: boolean;
}

interface UseApiRequestOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  showToast?: boolean;
}

export const useApiRequest = (options: UseApiRequestOptions = {}) => {
  const [state, setState] = useState<ApiRequestState>({
    loading: false,
    error: null,
    data: null,
    isNetworkError: false,
    isTimeoutError: false,
    isServerError: false,
  });

  const execute = useCallback(async (apiCall: () => Promise<any>) => {
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      isNetworkError: false,
      isTimeoutError: false,
      isServerError: false,
    }));

    try {
      const result = await apiCall();
      
      setState(prev => ({
        ...prev,
        loading: false,
        data: result,
        error: null,
      }));

      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (error: any) {
      console.error('API Request Error:', error);

      // Determine error type
      const isNetworkError = error?.isNetworkError || 
        error?.message?.includes('Network request failed') ||
        error?.message?.includes('network error');
      
      const isTimeoutError = error?.message?.includes('timeout') ||
        error?.message?.includes('Request timeout');
      
      const isServerError = error?.status >= 500 || 
        error?.message?.includes('server error');

      setState(prev => ({
        ...prev,
        loading: false,
        error,
        isNetworkError,
        isTimeoutError,
        isServerError,
      }));

      if (options.onError) {
        options.onError(error);
      }

      throw error;
    }
  }, [options]);

  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      data: null,
      isNetworkError: false,
      isTimeoutError: false,
      isServerError: false,
    });
  }, []);

  const retry = useCallback(async (apiCall: () => Promise<any>) => {
    return execute(apiCall);
  }, [execute]);

  return {
    ...state,
    execute,
    reset,
    retry,
  };
};

// Helper hook for common request patterns
export const useApiRequestWithToast = (toastHandler?: (message: string, type: 'success' | 'error') => void) => {
  return useApiRequest({
    onSuccess: (data) => {
      if (toastHandler && data?.message) {
        toastHandler(data.message, 'success');
      }
    },
    onError: (error) => {
      if (toastHandler) {
        const message = error?.message || 'An error occurred. Please try again.';
        toastHandler(message, 'error');
      }
    },
  });
};
