import React from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { SvgXml } from "react-native-svg";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import { svg } from "../Config/Svg";

interface ProfileMenuItemProps {
  icon: string;
  label: string;
  onPress: () => void;
}

const ProfileMenuItem = ({ icon, label, onPress }: ProfileMenuItemProps) => {
  return (
    <TouchableOpacity style={styles.menuItem} onPress={onPress}>
      <View style={styles.menuItemLeft}>
        <SvgXml xml={icon} />
        <Text style={styles.menuItemLabel}>{label}</Text>
      </View>
      <SvgXml xml={svg.arrowRight} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  menuItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  menuItemLabel: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
    marginLeft: 8,
  },
});

export default ProfileMenuItem;
