import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";

const request = new RequestHandler();

// Function to get auth token from AsyncStorage
export async function getAuthToken(): Promise<string> {
  try {
    const cookiesString = await AsyncStorage.getItem("cookies");
    if (cookiesString) {
      const cookies = JSON.parse(cookiesString);
      if (
        (cookies && cookies.data && cookies.data.token) ||
        (cookies && cookies.token)
      ) {
        return cookies.data.token ? cookies.data.token : cookies.token;
      }
    }
    return "";
  } catch (error) {
    console.error("Error getting auth token:", error);
    return "";
  }
}
const decodeJwt = (token: string): string | undefined => {
  if (token) {
    try {
      const payload = token.split(".")[1];
      const decodedPayload = JSON.parse(atob(payload));
      return decodedPayload.id;
    } catch (error) {
      console.error("Error decoding JWT:", error);
      return undefined;
    }
  }
  return undefined;
};

export async function GetUser(): Promise<any> {
  const token = await getAuthToken();
  const userId = decodeJwt(token);
  return request.get(`api/v1/elevate-api/getly/user/${userId}`, token);
}

export async function GetWallet(): Promise<any> {
  const token = await getAuthToken();
  const userId = decodeJwt(token);
  return request.get(`api/v1/elevate-api/getly/user/${userId}/cards-or-accounts`, token);
}
