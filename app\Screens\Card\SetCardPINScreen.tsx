import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  Dimensions,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Button from "../../Components/Button";
import Input from "../../Components/Input";

const { width, height } = Dimensions.get("window");

interface SetCardPINScreenProps {
  navigation: any;
}

export default function SetCardPINScreen({
  navigation,
}: SetCardPINScreenProps) {
  const [newPin, setNewPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [loading, setLoading] = useState(false);

  // Handle PIN input
  const handlePinInput = (text: string, field: "new" | "confirm") => {
    // Only allow numbers
    const numericValue = text.replace(/[^0-9]/g, "");

    if (field === "new") {
      setNewPin(numericValue);
    } else {
      setConfirmPin(numericValue);
    }
  };

  // Handle confirm button press
  const handleConfirm = () => {
    // Validate inputs
    if (!newPin || newPin.length !== 4) {
      Alert.alert("Error", "Please enter a valid 4-digit PIN");
      return;
    }

    if (!confirmPin || confirmPin.length !== 4) {
      Alert.alert("Error", "Please confirm your PIN");
      return;
    }

    if (newPin !== confirmPin) {
      Alert.alert("Error", "PINs do not match");
      return;
    }

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      // Navigate to success screen
      navigation.navigate("CardActivationSuccessScreen");
    }, 1500);
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <Div>
          <Header title="Activate Card" showNotification={false} />
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            automaticallyAdjustKeyboardInsets={true}
          >
            <View style={styles.content}>
              {/* Instructions */}
              <Text style={styles.instructions}>
                Set a new 4-digit card PIN. Use your card PIN to withdraw cash
                at any ATM, make POS payments and online transactions in Naira.
              </Text>

              {/* New PIN Input */}
              <View style={styles.inputContainer}>
                <Input
                  label="New Card PIN"
                  placeholder="Enter 4-digit PIN"
                  value={newPin}
                  onChangeText={(text) => handlePinInput(text, "new")}
                  keyboardType="numeric"
                  maxLenght={4}
                  type="password"
                />
              </View>

              {/* Confirm PIN Input */}
              <View style={styles.inputContainer}>
                <Input
                  label="Confirm New Card PIN"
                  placeholder="Re-enter 4-digit PIN"
                  value={confirmPin}
                  onChangeText={(text) => handlePinInput(text, "confirm")}
                  keyboardType="numeric"
                  maxLenght={4}
                  type="password"
                />
              </View>

              {/* Confirm Button */}
              <View style={styles.buttonContainer}>
                <Button
                  btnText="Confirm"
                  onPress={handleConfirm}
                  loading={loading}
                />
              </View>
            </View>
          </ScrollView>
        </Div>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 40,
  },
  instructions: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginBottom: 24,
    lineHeight: 24,
  },
  inputContainer: {
    marginBottom: 20,
  },
  buttonContainer: {
    marginTop: 32,
  },
});
