{"data": {"orderId": "cmbjkerhw0001kjcixv6di1zu", "orderStatus": "COMPLETED", "providerResult": {"__v": 0, "_id": "6841bfa107d0d900246eb413", "account": "68404f7e4ab23b00249e073d", "amount": 5, "client": "63a652707ce84a001eac0dfc", "createdAt": "2025-06-05T16:02:41.064Z", "createdBy": "63a652707ce84a001eac0e05", "creditAccountName": "GETLY LTD. / SUDO SETTLEMENT ACCOUNT", "creditAccountNumber": "**********", "creditBankVerificationNumber": null, "creditKYCLevel": "3", "debitAccountName": "GETLYLTD. / JOHN ADAMU", "debitAccountNumber": "**********", "debitBankVerificationNumber": null, "debitKYCLevel": "3", "destinationInstitutionCode": "090286", "fees": 0, "isDeleted": false, "isReversed": false, "mandateReference": null, "nameEnquiryReference": "090286250605160240251359851907", "narration": "Internal Fund Transfer|Getly Card Order Fee|GETLYLTD. / JOHN ADAMU|ORD-Cj3ymwp35kiu|GETLY LTD. / SUDO SETTLEMENT ACCOUNT", "paymentReference": "ORD-Cj3ymwp35kiu", "provider": "BANK", "providerChannel": "TRANSFER", "providerChannelCode": "IBS", "queued": false, "responseCode": "00", "responseMessage": "Approved or completed successfully", "reversalReference": null, "sessionId": "090286250605160241463721452971", "stampDuty": 0, "status": "Completed", "transactionLocation": "9.0932,7.4429", "type": "Outwards", "updatedAt": "2025-06-05T16:02:41.245Z", "vat": 0}, "transactionId": "cmbjkeu580003kjcilgqr1ykm"}, "status": "success"}