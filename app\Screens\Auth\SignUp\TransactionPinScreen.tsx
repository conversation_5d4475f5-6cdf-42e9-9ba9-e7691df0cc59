import React, { useState, useRef, useEffect, useContext } from "react";
import { useUpdateNextStep } from "../../../utils/updateNextStep";
import { Dimensions, StyleSheet, View, TextInput } from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";
import { CreatePin } from "../../../RequestHandler.tsx/Auth";
import { useToast } from "../../../Context/ToastContext";
import { getUserEmail } from "../../../utils/userEmail";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../../../Context/CredentailsContext";

const { height } = Dimensions.get("window");

export default function TransactionPinScreen({ navigation }) {
  // State for PIN inputs
  const [pin, setPin] = useState(["", "", "", ""]);
  const [confirmPin, setConfirmPin] = useState(["", "", "", ""]);
  const [isPinMatch, setIsPinMatch] = useState(true);
  const [isFormComplete, setIsFormComplete] = useState(false);
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const { updateUserNextStep } = useUpdateNextStep();
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);

  // Refs for input fields
  const pinInputRefs = useRef([]);
  const confirmPinInputRefs = useRef([]);

  // Initialize refs
  useEffect(() => {
    pinInputRefs.current = pinInputRefs.current.slice(0, 4);
    confirmPinInputRefs.current = confirmPinInputRefs.current.slice(0, 4);
  }, []);

  // Check if form is complete and pins match
  useEffect(() => {
    const pinComplete = pin.every((digit) => digit !== "");
    const confirmPinComplete = confirmPin.every((digit) => digit !== "");

    if (pinComplete && confirmPinComplete) {
      const pinString = pin.join("");
      const confirmPinString = confirmPin.join("");
      setIsPinMatch(pinString === confirmPinString);
      setIsFormComplete(pinString === confirmPinString);
    } else {
      setIsFormComplete(false);
    }
  }, [pin, confirmPin]);

  // Handle PIN input
  const handlePinChange = (text: string, index: number) => {
    if (text.length <= 1 && /^[0-9]*$/.test(text)) {
      const newPin = [...pin];
      newPin[index] = text;
      setPin(newPin);

      // Auto-focus next input
      if (text !== "" && index < 3) {
        pinInputRefs.current[index + 1].focus();
      }
    }
  };

  // Handle confirm PIN input
  const handleConfirmPinChange = (text: string, index: number) => {
    if (text.length <= 1 && /^[0-9]*$/.test(text)) {
      const newConfirmPin = [...confirmPin];
      newConfirmPin[index] = text;
      setConfirmPin(newConfirmPin);

      // Auto-focus next input
      if (text !== "" && index < 3) {
        confirmPinInputRefs.current[index + 1].focus();
      }
    }
  };

  // Handle backspace key press
  const handleKeyPress = (e: any, index: number, isPinInput: boolean) => {
    if (e.nativeEvent.key === "Backspace") {
      if (isPinInput) {
        if (pin[index] === "" && index > 0) {
          pinInputRefs.current[index - 1].focus();
        }
      } else {
        if (confirmPin[index] === "" && index > 0) {
          confirmPinInputRefs.current[index - 1].focus();
        }
      }
    }
  };

  const persistLogin = (credentail: any) => {
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(() => {
        setStoredCredentails(credentail);
        navigation.reset({
          index: 0,
          routes: [{ name: "BottomTabNavigator1" }],
        });
      })
      .catch((err) => {
        console.error("Error persisting login:", err);
      });
  };
  // Handle create PIN button press
  const handleCreatePin = async () => {
    if (isFormComplete) {
      setLoading(true);
      try {
        const pinString = pin.join("");
        const confirmPinString = confirmPin.join("");
        const email = await getUserEmail();
        const body = {
          email: email,
          transactionPin: pinString,
          confirmTransactionPin: confirmPinString,
        };
        const res = await CreatePin(body);     
        persistLogin(res);
        handleToast("Transaction PIN created successfully", "success");
      } catch (error) {
        console.log(error);
        
        const errorMessage =
          error && error.message
            ? error.message
            : "Failed to create transaction PIN";
        handleToast(errorMessage, "error");
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={5}
            totalPages={6}
            onBack={() => navigation.pop()}
          />
          <View style={styles.contentContainer}>
            <H4 style={styles.mainTitle}>Set a Transaction Pin</H4>
            <P style={styles.subtitle}>
              Set up a 4 digit transaction pin that is unique to you alone.
            </P>
            <View style={styles.pinContainer}>
              <View style={styles.pinItemWrap}>
                <P style={styles.pinLabel}>Enter Transaction Pin</P>
                <View style={styles.pinInputContainer}>
                  {[0, 1, 2, 3].map((index) => (
                    <TextInput
                      key={`pin-${index}`}
                      ref={(ref) => (pinInputRefs.current[index] = ref)}
                      style={styles.pinInput}
                      value={pin[index]}
                      onChangeText={(text) => handlePinChange(text, index)}
                      keyboardType="numeric"
                      maxLength={1}
                      secureTextEntry={true}
                      onKeyPress={(e) => handleKeyPress(e, index, true)}
                    />
                  ))}
                </View>

                <P style={[styles.pinLabel, { marginTop: 30 }]}>
                  Confirm Transaction Pin
                </P>
                <View style={styles.pinInputContainer}>
                  {[0, 1, 2, 3].map((index) => (
                    <TextInput
                      key={`confirm-pin-${index}`}
                      ref={(ref) => (confirmPinInputRefs.current[index] = ref)}
                      style={[
                        styles.pinInput,
                        !isPinMatch &&
                          confirmPin[index] !== "" &&
                          styles.pinInputError,
                      ]}
                      value={confirmPin[index]}
                      onChangeText={(text) =>
                        handleConfirmPinChange(text, index)
                      }
                      keyboardType="numeric"
                      maxLength={1}
                      secureTextEntry={true}
                      onKeyPress={(e) => handleKeyPress(e, index, false)}
                    />
                  ))}
                </View>
                {!isPinMatch && (
                  <P style={styles.errorText}>PINs do not match</P>
                )}
              </View>
            </View>
          </View>

          {isFormComplete && (
            <View style={styles.buttonContainer}>
              <Button
                btnText="Create Pin"
                onPress={handleCreatePin}
                loading={loading}
              />
            </View>
          )}
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  pinContainer: {
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    minHeight: 280,
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
  },
  pinItemWrap: {
    width: "60%",
  },
  pinLabel: {
    fontFamily: fonts.plusJMedium,
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 16,
    textAlign: "center",
  },
  pinInputContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  pinInput: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderColor: "#8E8693",
    borderRadius: 4,
    textAlign: "center",
    fontSize: 16,
    color: colors.textAsh,
    fontFamily: fonts.plusJMedium,
    backgroundColor: colors.white,
  },
  pinInputError: {
    borderColor: colors.error || "red",
  },
  errorText: {
    color: colors.error || "red",
    fontSize: 14,
    marginTop: 8,
    textAlign: "center",
  },
  buttonContainer: {
    width: "90%",
    marginBottom: (5 * height) / 100,
    alignSelf: "center",
  },
  disabledButton: {
    backgroundColor: "#CCCCCC",
  },
});
