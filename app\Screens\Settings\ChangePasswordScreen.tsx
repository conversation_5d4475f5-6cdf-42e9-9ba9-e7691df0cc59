import React from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Input from "../../Components/Input";
import Button from "../../Components/Button";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { Formik } from "formik";
import * as yup from "yup";

const validationSchema = yup.object().shape({
  newPassword: yup.string().required("New password is required"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("newPassword"), null], "Passwords must match")
    .required("Please confirm your new password"),
});

const ChangePasswordScreen: React.FC = () => {
  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={{ paddingBottom: 32 }}>
        <Header title="Change Password" />
        <View style={styles.content}>
          <P style={styles.title}>Enter a new password</P>
          <P style={styles.subtitle}>
            Reset password. Your new password must be different from the previous one
          </P>
          <Formik
            initialValues={{ newPassword: "", confirmPassword: "" }}
            validationSchema={validationSchema}
            onSubmit={(values) => {
              // handle password change
            }}
          >
            {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
              <View style={{ width: "100%" }}>
                <Input
                  label="New password"
                  placeholder="Enter your new password"
                  value={values.newPassword}
                  onChangeText={handleChange("newPassword")}
                  onBlur={handleBlur("newPassword")}
                  type="password"
                  error={!!errors.newPassword && touched.newPassword}
                  errorText={errors.newPassword}
                />
                <Input
                  label="Confirm new password"
                  placeholder="Re-enter your new password"
                  value={values.confirmPassword}
                  onChangeText={handleChange("confirmPassword")}
                  onBlur={handleBlur("confirmPassword")}
                  type="password"
                  error={!!errors.confirmPassword && touched.confirmPassword}
                  errorText={errors.confirmPassword}
                  contStyle={{marginTop: 16}}
                />
                <Button
                  btnText="Save"
                  onPress={handleSubmit}
                  style={styles.saveBtn}
                />
              </View>
            )}
          </Formik>
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  content: { flex: 1, padding: 20 },
  title: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    color: colors.textBlack,
  },
  subtitle: {
    fontFamily: fonts.plusJRegular,
    fontSize: 15,
    color: "#888",
    marginBottom: 20,
  },
  saveBtn: {
    marginTop: 40,
    backgroundColor: "#FBC94A",
    borderRadius: 8,
  },
});

export default ChangePasswordScreen; 