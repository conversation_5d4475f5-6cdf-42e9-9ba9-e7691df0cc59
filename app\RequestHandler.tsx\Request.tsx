import AsyncStorage from "@react-native-async-storage/async-storage";

export class RequestHandler {
  private BASE_URL = "https://getly-api.onrender.com/";
  private TIMEOUT_MS = 60000; // 20 seconds timeout

  // Helper method to handle fetch with timeout
  private async fetchWithTimeout(url: string, options: RequestInit): Promise<Response> {
    const controller = new AbortController();
    const { signal } = controller;
    
    // Create a timeout promise
    const timeout = new Promise<Response>((_, reject) => {
      setTimeout(() => {
        controller.abort();
        reject(new Error('Request timeout'));
      }, this.TIMEOUT_MS);
    });

    // Create the fetch promise with the abort signal
    const fetchPromise = fetch(url, {
      ...options,
      signal
    });

    // Race between fetch and timeout
    try {
      return await Promise.race([fetchPromise, timeout]) as Response;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  public async get(
    path: string,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: token ? `Bearer ${token}` : "",
        },
      });
      
      const data = await response.json();
      
      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }
      
      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, throw the error data
        throw data;
      }
    } catch (error) {
      // Check if it's a network error
      if (error.message && (
        error.message.includes('Network request failed') || 
        error.message.includes('Request timeout')
      )) {
        const networkError = {
          isNetworkError: true,
          message: error.message,
          status: 'network_error'
        };
        
        // Call the callback functions with the network error
        if (functions && functions.length > 0) {
          functions.forEach((func) => func(networkError));
        }
        
        throw networkError;
      }
      
      // For other errors (including API errors), just throw them
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(error));
      }
      
      throw error;
    }
  }

  public async post(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify(body),
      });
      
      const data = await response.json();
      
      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }
      
      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, throw the error data
        throw data;
      }
    } catch (error) {
      // Check if it's a network error
      if (error.message && (
        error.message.includes('Network request failed') || 
        error.message.includes('Request timeout')
      )) {
        const networkError = {
          isNetworkError: true,
          message: error.message,
          status: 'network_error'
        };
        
        // Call the callback functions with the network error
        if (functions && functions.length > 0) {
          functions.forEach((func) => func(networkError));
        }
        
        throw networkError;
      }
      
      // For other errors (including API errors), just throw them
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(error));
      }
      
      throw error;
    }
  }

  public async put(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify(body),
      });
      
      const data = await response.json();
      
      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }
      
      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, throw the error data
        throw data;
      }
    } catch (error) {
      // Check if it's a network error
      if (error.message && (
        error.message.includes('Network request failed') || 
        error.message.includes('Request timeout')
      )) {
        const networkError = {
          isNetworkError: true,
          message: error.message,
          status: 'network_error'
        };
        
        // Call the callback functions with the network error
        if (functions && functions.length > 0) {
          functions.forEach((func) => func(networkError));
        }
        
        throw networkError;
      }
      
      // For other errors (including API errors), just throw them
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(error));
      }
      
      throw error;
    }
  }

  public async patch(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify(body),
      });
      
      const data = await response.json();
      
      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }
      
      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, throw the error data
        throw data;
      }
    } catch (error) {
      // Check if it's a network error
      if (error.message && (
        error.message.includes('Network request failed') || 
        error.message.includes('Request timeout')
      )) {
        const networkError = {
          isNetworkError: true,
          message: error.message,
          status: 'network_error'
        };
        
        // Call the callback functions with the network error
        if (functions && functions.length > 0) {
          functions.forEach((func) => func(networkError));
        }
        
        throw networkError;
      }
      
      // For other errors (including API errors), just throw them
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(error));
      }
      
      throw error;
    }
  }

  public async delete(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify(body),
      });
      
      const data = await response.json();
      
      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }
      
      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, throw the error data
        throw data;
      }
    } catch (error) {
      // Check if it's a network error
      if (error.message && (
        error.message.includes('Network request failed') || 
        error.message.includes('Request timeout')
      )) {
        const networkError = {
          isNetworkError: true,
          message: error.message,
          status: 'network_error'
        };
        
        // Call the callback functions with the network error
        if (functions && functions.length > 0) {
          functions.forEach((func) => func(networkError));
        }
        
        throw networkError;
      }
      
      // For other errors (including API errors), just throw them
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(error));
      }
      
      throw error;
    }
  }
}