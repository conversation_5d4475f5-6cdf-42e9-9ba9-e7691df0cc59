import React, { useState, useEffect } from "react";
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    SafeAreaView,
    Dimensions,
    TextInput,
    TouchableOpacity,
    Image,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import Header from "../../Components/Header";
import P from "../../Components/P";
import H4 from "../../Components/H4";
import Icon from "react-native-vector-icons/Feather";
import PaymentStatusModal, {
    PaymentStatus,
} from "../../Components/PaymentStatusModal";
import { GetWalletBalance } from "../../RequestHandler.tsx/Wallet";
import { useToast } from "../../Context/ToastContext";

const { width, height } = Dimensions.get("window");

interface PaymentScreenProps {
    navigation: any;
    route: {
        params: {
            amount: number;
            currency: string;
            convertedAmount: number;
            convertedCurrency: string;
            fee: number;
            totalAmount: number;
            accountIds: string[];
        };
    };
}

export default function PaymentNGNScreen({
    navigation,
    route,
}: PaymentScreenProps) {
    const {
        amount,
        currency,
        convertedAmount,
        convertedCurrency,
        fee,
        totalAmount,
        accountIds,
    } = route.params;

    const [cardNumber, setCardNumber] = useState("");
    const [expiryDate, setExpiryDate] = useState("");
    const [cvv, setCvv] = useState("");
    const [nameOnCard, setNameOnCard] = useState("");
    const [address, setAddress] = useState("");
    const [postalCode, setPostalCode] = useState("");
    const [selectedCard, setSelectedCard] = useState("");
    const [loading, setLoading] = useState(false);
    const [isbalInsufficient, setIsbalInsufficient] = useState(false);
    const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(
        null
    );
    const [loadBal, setLoadBal] = useState(false)
    const [bal, setBal] = useState(0)
    const [showStatusModal, setShowStatusModal] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");

    const { handleToast } = useToast()
    const getWalletBalance = async () => {
        setLoadBal(true)
        try {
            const res = await GetWalletBalance(accountIds[0])
            console.log(res);
            if (res.data) {
                if (res.data.currentBalance < amount + fee) {
                    // setIsbalInsufficient(true)
                } else {
                    setIsbalInsufficient(false)
                }
                setBal(res.data.currentBalance)
            }
        } catch (error) {
            console.log(error)
        } finally {
            setLoadBal(false)
        }
    }



    useEffect(() => {
        getWalletBalance()
    }, [])

    // Handle payment submission
    const handlePayment = () => {
        if (!isbalInsufficient) {
            navigation.navigate("ValidatePinScreen", {
                amount: amount + fee,
                currency: "NGN",
                accountIds: accountIds,
            });
        } else {
            handleToast("Insufficient Balance", "error")
        }
    };

    // Handle button press in the status modal


    return (
        <SafeAreaView style={styles.container}>
            <Div>
                <Header
                    title="Make Payment"
                    contStyle={{ justifyContent: "flex-start", gap: 24 }}
                    showNotification={false}
                />
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={styles.scrollContent}
                    style={{ width: "95%" }}
                >
                    {/* Price Information */}
                    <View style={styles.priceInfoCard}>
                        <View style={styles.priceRow}>
                            <View>
                                <P style={styles.priceLabel}>Price</P>
                                <H4 style={styles.priceValue}>N{amount.toLocaleString()}</H4>
                            </View>
                            <View style={styles.priceValueContainer}>
                                <View style={styles.currencyFlag}>
                                    <Image
                                        source={{
                                            uri: `https://flagcdn.com/w2560/ng.png`,
                                        }}
                                        style={[styles.flagImage, { objectFit: "fill" }]}
                                    />
                                </View>
                                <P style={styles.currencyText}>NGN</P>
                            </View>
                        </View>
                    </View>

                    {/* Transaction Fee */}
                    <View style={styles.feeContainer}>
                        <P style={styles.feeLabel}>Transaction fee</P>
                        <P style={styles.feeValue}>₦{fee}</P>
                    </View>

                    <View style={styles.divider} />

                    {/* Total Amount */}
                    <View style={styles.totalContainer}>
                        <P style={styles.totalLabel}>Total amount charged</P>
                        <P style={styles.totalValue}>₦{amount + fee}</P>
                    </View>

                    {/* Payment Form */}
                    <View style={styles.formContainer}>
                        <P style={{ fontFamily: fonts.plusJBold, marginBottom: 10 }}>Wallet Details</P>
                        <View style={styles.formGroup}>
                            <P style={styles.formLabel}>Naira wallet</P>
                            <TextInput
                                editable={false}
                                style={styles.input}
                                placeholder="First name Last Name"
                                placeholderTextColor="#D7D5D9"
                                value={`NGN ${(amount + fee).toLocaleString()}`}
                            />
                        </View>
                        <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "space-between" }}>
                            <P style={{ fontSize: 10, color: isbalInsufficient ? colors.red : colors.textBlack }}>{isbalInsufficient ? "Insufficient Balance" : " Wallet Balance:"}</P>
                            <P style={{ fontSize: 10, color: isbalInsufficient ? colors.red : colors.textBlack }}>₦{bal}</P>
                        </View>
                    </View>

                    {/* Pay Button */}
                    <View style={styles.payButtonContainer}>
                        <Button
                            btnText="Pay Now"
                            onPress={handlePayment}
                            loading={loading}
                        />
                    </View>
                </ScrollView>
            </Div>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
    scrollContent: {
        paddingHorizontal: 16,
        paddingBottom: 40,
    },
    priceInfoCard: {
        backgroundColor: colors.white,
        borderTopEndRadius: 12,
        borderTopStartRadius: 12,
        marginTop: 20,
        borderWidth: 1,
        borderColor: "#E5E7EB",
    },
    priceRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        padding: 16,
    },
    priceLabel: {
        fontSize: 14,
        color: colors.textAsh,
        marginBottom: 8,
    },
    priceValueContainer: {
        flexDirection: "row",
        alignItems: "center",
        gap: 4,
    },
    priceValue: {
        fontSize: 24,
        fontFamily: fonts.plusJSemibold,
        marginRight: 8,
    },
    currencyFlag: {
        width: 32,
        height: 32,
        borderRadius: 100,
        marginRight: 4,
        overflow: "hidden",
    },
    flagImage: {
        width: 32,
        height: 32,
        borderRadius: 10,
    },
    currencyText: {
        fontSize: 12,
        color: colors.textBlack,
        fontFamily: fonts.plusJSemibold,
    },
    conversionRate: {
        paddingTop: 12,
    },
    conversionText: {
        fontSize: 12,
        color: "#DF7400",
        fontFamily: fonts.plusJMedium,
    },
    feeContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 44,
    },
    feeLabel: {
        fontSize: 14,
        color: colors.textBlack,
    },
    feeValue: {
        fontSize: 14,
        color: colors.textBlack,
    },
    divider: {
        height: 1,
        borderWidth: 1.5,
        borderColor: "#D7D5D9",
        marginVertical: 12,
        borderStyle: "dashed",
    },
    totalContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 44,
    },
    totalLabel: {
        fontSize: 14,
        fontFamily: fonts.plusJMedium,
        color: colors.textBlack,
    },
    totalValue: {
        fontSize: 14,
        fontFamily: fonts.plusJSemibold,
        color: colors.textBlack,
    },
    formContainer: {
        marginBottom: 20,
    },
    formGroup: {
        marginBottom: 4,
    },
    formLabel: {
        fontSize: 12,
        color: colors.textBlack,
        marginBottom: 8,
    },
    sectionTitle: {
        fontSize: 12,
        fontFamily: fonts.plusJMedium,
        color: colors.textBlack,
        marginBottom: 12,
    },
    input: {
        height: 48,
        borderWidth: 1,
        borderColor: "#E5E7EB",
        borderRadius: 8,
        paddingHorizontal: 12,
        fontSize: 12,
        fontFamily: fonts.plusJRegular,
    },
    selectInput: {
        height: 48,
        borderWidth: 1,
        borderColor: "#E5E7EB",
        borderRadius: 8,
        paddingHorizontal: 12,
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    selectText: {
        fontSize: 12,
        color: "#555059",
    },
    rowInputs: {
        flexDirection: "row",
    },
    payButtonContainer: {
        marginTop: 10,
    },
    placeHolderColor: {
        color: "#D7D5D9",
    },
});
