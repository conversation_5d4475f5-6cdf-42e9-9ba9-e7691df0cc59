import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
  Dimensions,
  Animated,
  ScrollView,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import Button from "./Button";
import Icon from "react-native-vector-icons/Feather";

const { width, height } = Dimensions.get("window");

interface CurrencyOption {
  id: string;
  name: string;
  code: string;
  flag: string;
}

interface SelectCardCurrencyModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (currency: CurrencyOption) => void;
}

const currencyOptions: CurrencyOption[] = [
  { id: "ngn", name: "Nigerian Naira", code: "NGN", flag: "🇳🇬" },
  { id: "usd", name: "US Dollar", code: "USD", flag: "🇺🇸" },
  { id: "gbp", name: "British Pound", code: "GBP", flag: "🇬🇧" },
  { id: "eur", name: "Euro", code: "EUR", flag: "🇪🇺" },
];

const SelectCardCurrencyModal: React.FC<SelectCardCurrencyModalProps> = ({
  isVisible,
  onClose,
  onSelect,
}) => {
  const [selectedCurrency, setSelectedCurrency] = useState<string | null>(null);
  
  // Animation value for bottom sheet
  const slideAnim = React.useRef(new Animated.Value(height)).current;

  React.useEffect(() => {
    if (isVisible) {
      // Reset selected currency when opening
      setSelectedCurrency(null);
      
      // Slide up animation
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Slide down animation
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible, slideAnim]);

  // Handle currency selection
  const handleCurrencySelect = (currencyId: string) => {
    setSelectedCurrency(currencyId);
  };

  // Handle continue button press
  const handleContinue = () => {
    if (selectedCurrency) {
      const selected = currencyOptions.find(c => c.id === selectedCurrency);
      if (selected) {
        onSelect(selected);
      }
    }
  };

  // Handle backdrop press
  const handleBackdropPress = () => {
    onClose();
  };

  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={handleBackdropPress}
        />
        
        <Animated.View
          style={[
            styles.bottomSheet,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Select Card Currency</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon name="x" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.currencyList}>
            {currencyOptions.map((currency) => (
              <TouchableOpacity
                key={currency.id}
                style={[
                  styles.currencyItem,
                  selectedCurrency === currency.id && styles.selectedCurrencyItem,
                ]}
                onPress={() => handleCurrencySelect(currency.id)}
              >
                <View style={styles.currencyInfo}>
                  <Text style={styles.currencyFlag}>{currency.flag}</Text>
                  <View>
                    <Text style={styles.currencyName}>{currency.name}</Text>
                    <Text style={styles.currencyCode}>{currency.code}</Text>
                  </View>
                </View>
                
                {selectedCurrency === currency.id && (
                  <Icon name="check" size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <View style={styles.buttonContainer}>
            <Button
              btnText="Continue"
              onPress={handleContinue}
              disabled={!selectedCurrency}
            />
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bottomSheet: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingBottom: 30,
    maxHeight: height * 0.7,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F2F2F2",
  },
  title: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  closeButton: {
    padding: 4,
  },
  currencyList: {
    marginTop: 16,
    marginBottom: 16,
  },
  currencyItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F2F2F2",
  },
  selectedCurrencyItem: {
    backgroundColor: "#F9F9F9",
  },
  currencyInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  currencyFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  currencyName: {
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
  },
  currencyCode: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    marginTop: 4,
  },
  buttonContainer: {
    marginTop: 16,
  },
});

export default SelectCardCurrencyModal;
