import React, { useState } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import SelectInput from "./SelectInput";
import { countries } from "../data/countries";
import { getStatesByCountry } from "../data/states";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import Button from "./Button";
import H4 from "./H4";

// Example usage of SelectInput component
const SelectInputExample: React.FC = () => {
  const [selectedCountry, setSelectedCountry] = useState("");
  const [selectedState, setSelectedState] = useState("");
  const [availableStates, setAvailableStates] = useState<any[]>([]);

  // Sample data for demonstration
  const sampleOptions = [
    { label: "Option 1", value: "opt1" },
    { label: "Option 2", value: "opt2" },
    { label: "Option 3", value: "opt3" },
    { label: "Very Long Option Name That Should Be Handled Properly", value: "opt4" },
  ];

  const handleCountrySelect = (option: any) => {
    setSelectedCountry(option.value);
    setSelectedState(""); // Reset state when country changes
    setAvailableStates(getStatesByCountry(option.value));
  };

  const handleStateSelect = (option: any) => {
    setSelectedState(option.value);
  };

  const handleSubmit = () => {
    console.log("Selected Country:", selectedCountry);
    console.log("Selected State:", selectedState);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <H4 style={styles.title}>SelectInput Component Examples</H4>

        {/* Basic SelectInput */}
        <SelectInput
          label="Basic Select"
          placeholder="Choose an option"
          options={sampleOptions}
          onSelect={(option) => console.log("Selected:", option)}
          contStyle={styles.inputContainer}
        />

        {/* Country SelectInput with Flags */}
        <SelectInput
          label="Country (with flags)"
          placeholder="Select your country"
          value={selectedCountry}
          options={countries.map(country => ({
            label: country.label,
            value: country.value,
            flagUrl: country.flagUrl
          }))}
          onSelect={handleCountrySelect}
          contStyle={styles.inputContainer}
          showFlags={true}
        />

        {/* State SelectInput (dependent on country) */}
        <SelectInput
          label="State/Province"
          placeholder={selectedCountry ? "Select your state" : "Please select a country first"}
          value={selectedState}
          options={availableStates.map(state => ({
            label: state.label,
            value: state.value
          }))}
          onSelect={handleStateSelect}
          disabled={!selectedCountry}
          contStyle={styles.inputContainer}
        />

        {/* SelectInput with error */}
        <SelectInput
          label="Select with Error"
          placeholder="This has an error"
          options={sampleOptions}
          onSelect={(option) => console.log("Selected:", option)}
          error={true}
          errorText="This field is required"
          contStyle={styles.inputContainer}
        />

        {/* Disabled SelectInput */}
        <SelectInput
          label="Disabled Select"
          placeholder="This is disabled"
          options={sampleOptions}
          onSelect={(option) => console.log("Selected:", option)}
          disabled={true}
          contStyle={styles.inputContainer}
        />

        {/* Non-searchable SelectInput */}
        <SelectInput
          label="Non-searchable Select"
          placeholder="No search functionality"
          options={sampleOptions}
          onSelect={(option) => console.log("Selected:", option)}
          searchable={false}
          contStyle={styles.inputContainer}
        />

        <Button
          btnText="Submit"
          onPress={handleSubmit}
          style={styles.submitButton}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    marginBottom: 20,
    textAlign: "center",
  },
  inputContainer: {
    marginBottom: 20,
  },
  submitButton: {
    marginTop: 20,
    backgroundColor: colors.primary,
  },
});

export default SelectInputExample;
