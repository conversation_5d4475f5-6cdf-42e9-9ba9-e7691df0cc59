import { StatusBar } from "expo-status-bar";
import { AppState, AppStateStatus } from "react-native";
import { useFonts } from "expo-font";
import { useEffect, useState, useRef } from "react";
import MainStack from "./app/Navigation/MainStack";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { CredentailsContext } from "./app/Context/CredentailsContext";
import CustomSplashScreen from "./app/Screens/CustomSplashScreen";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ToastProvider } from "./app/Context/ToastContext";
import SessionExpiredModal from "./app/Components/SessionExpiredModal";

export default function App() {
  const [appReady, setAppReady] = useState(false);
  const [storedCredentails, setStoredCredentails] = useState<any>(null);
  const [showSessionExpiredModal, setShowSessionExpiredModal] = useState(false);
  const appState = useRef(AppState.currentState);
  const [fontsLoaded] = useFonts({
    "plusJ-bold": require("./app/assets/fonts/PlusJakartaSans-Bold.ttf"),
    "plusJ-semibold": require("./app/assets/fonts/PlusJakartaSans-SemiBold.ttf"),
    "plusJ-medium": require("./app/assets/fonts/PlusJakartaSans-Medium.ttf"),
    "plusJ-regular": require("./app/assets/fonts/PlusJakartaSans-Regular.ttf"),
    "plusJ-light": require("./app/assets/fonts/PlusJakartaSans-Light.ttf"),
  });

  // Parse JWT token and check if it's expired
  const parseJwt = (token: any): boolean => {
    if (token) {
      try {
        const tkn = token.token || token;
        const payload = tkn.split(".")[1];
        const decodedPayload = JSON.parse(atob(payload));

        // Check if token is expired
        if (decodedPayload.exp * 1000 < new Date().getTime()) {
          return true; // Token is expired
        }
        return false; // Token is valid
      } catch (error) {
        console.error("Error parsing JWT token:", error);
        return true; // Consider expired on error
      }
    }
    return true; // No token, consider expired
  };

  const checkLoginCredentails = async () => {
    try {
      const res = await AsyncStorage.getItem("cookies");
      if (res !== null && typeof res === "string") {
        const newRes = JSON.parse(res);

        // Get the token from the credentials
        const token = newRes.data?.token || newRes.token;

        // Check if token is expired using parseJwt
        const isExpired = parseJwt(token);

        if (isExpired) {
          // If login has expired, log the user out and show the session expired modal
          await clearLogin();
          setShowSessionExpiredModal(true);
        } else {
          setStoredCredentails(newRes);
        }
      } else {
        setStoredCredentails(null);
      }
      setAppReady(true);
    } catch (err) {
      console.log("Error fetching credentials:", err);
      setAppReady(true);
    }
  };

  // Function to clear login credentials
  const clearLogin = async () => {
    try {
      await AsyncStorage.removeItem("cookies");
      setStoredCredentails(null);
    } catch (error) {
      console.error("Error clearing login:", error);
    }
  };

  // Handle app state changes (background/foreground)
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (
      appState.current.match(/inactive|background/) &&
      nextAppState === "active"
    ) {
      // App has come to the foreground
      checkLoginCredentails();
    }
    appState.current = nextAppState;
  };

  useEffect(() => {
    // Set up app state change listener
    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      subscription.remove();
    };
  }, []);

  // Check token expiration every 20 seconds while the app is in use
  useEffect(() => {
    // clearLogin();
    // Only set up the interval if the user is logged in
    if (storedCredentails) {
      console.log("Setting up token expiration check interval");

      // Check token expiration every 20 seconds
      const tokenCheckInterval = setInterval(() => {
        // Get the token from stored credentials
        const token = storedCredentails.data?.token || storedCredentails.token;

        // Check if token is expired
        if (token && parseJwt(token)) {
          console.log("Token expired during active session");
          // Token is expired, log the user out
          clearLogin();
          setShowSessionExpiredModal(true);
          // Clear the interval since user is now logged out
          clearInterval(tokenCheckInterval);
        }
      }, 20000); // Check every 20 seconds (20000 ms)

      // Clean up the interval when component unmounts or credentials change
      return () => {
        console.log("Clearing token expiration check interval");
        clearInterval(tokenCheckInterval);
      };
    }
  }, [storedCredentails]); // Re-create interval when credentials change

  useEffect(() => {
    if (fontsLoaded) {
      setTimeout(checkLoginCredentails, 2000);
    }
  }, [fontsLoaded]);

  if (!appReady) {
    return <CustomSplashScreen />;
  }

  // Handle session expired modal close
  const handleSessionExpiredClose = () => {
    setShowSessionExpiredModal(false);
  };

  return (
    <>
      <StatusBar style="auto" />
      <CredentailsContext.Provider
        value={{
          storedCredentails,
          setStoredCredentails,
          updateNextStep: async (nextStep: string) => {
            try {
              // Get current credentials from AsyncStorage
              const credentialsStr = await AsyncStorage.getItem("cookies");
              if (credentialsStr) {
                // Parse the stored credentials
                const credentials = JSON.parse(credentialsStr);
                // Update the nextStep property
                const updatedCredentials = {
                  ...credentials,
                  nextStep: nextStep,
                };
                // Save back to AsyncStorage
                await AsyncStorage.setItem(
                  "cookies",
                  JSON.stringify(updatedCredentials)
                );

                // Update the context state
                setStoredCredentails(updatedCredentials);
              }
            } catch (error) {
              console.error("Error updating nextStep:", error);
            }
          },
        }}
      >
        <ToastProvider>
          <GestureHandlerRootView style={{ flex: 1 }}>
            <MainStack />

            {/* Session Expired Modal */}
            <SessionExpiredModal
              visible={showSessionExpiredModal}
              onClose={handleSessionExpiredClose}
            />
          </GestureHandlerRootView>
        </ToastProvider>
      </CredentailsContext.Provider>
    </>
  );
}

// Removed unused styles
