import React from "react";
import { View, StyleSheet, TouchableOpacity, ScrollView } from "react-native";
import Header from "../../Components/Header";
import P from "../../Components/P";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import { useNavigation } from "@react-navigation/native";

const chatOptions = [
  {
    label: "Live Chat",
    description: "Start a conversation on live chat",
    icon: svg.liveChat, // TODO: Replace with live chat SVG
    onPress: () => {},
  },
  {
    label: "Email",
    description: "We typically respond within a day",
    icon: svg.email,
    onPress: () => {},
  },
];

const socialOptions = [
  {
    label: "Instagram",
    icon: svg.iG,
    onPress: () => {},
  },
  {
    label: "Linkedin",
    icon: svg.linkedin,
    onPress: () => {},
  },
  {
    label: "Twitter",
    icon: svg.twitter,
    onPress: () => {},
  },
];

const supportOptions = [
  {
    label: "Resources portal",
    description: "Read articles & FAQs",
    icon: svg.document, // TODO: Replace with resources SVG
    onPress: () => {},
  },
];

const SupportScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  return (
    <Div>
      <ScrollView
        style={{ width: "100%" }}
        contentContainerStyle={{ paddingBottom: 32 }}
      >
        <Header title="Verification" />
        <View style={styles.section}>
          <P style={styles.sectionTitle}>Chat</P>
          {chatOptions.map((item) => (
            <TouchableOpacity
              key={item.label}
              style={styles.optionBox}
              onPress={item.onPress}
            >
              <View style={styles.iconBox}>
                <SvgXml xml={item.icon}/>
              </View>
              <View style={styles.textBox}>
                <P style={styles.optionLabel}>{item.label}</P>
                <P style={styles.optionDesc}>{item.description}</P>
              </View>
              <SvgXml
                xml={svg.chevronRight || svg.settings}
                width={22}
                height={22}
              />
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.section}>
          <P style={styles.sectionTitle}>Social Media</P>
          {socialOptions.map((item) => (
            <TouchableOpacity
              key={item.label}
              style={styles.optionBox}
              onPress={item.onPress}
            >
              <View style={styles.iconBox}>
                <SvgXml xml={item.icon} width={22} height={22} />
              </View>
              <P style={styles.optionLabel}>{item.label}</P>
              <View style={{position: "absolute", right: 16}}>

              <SvgXml
                xml={svg.chevronRight || svg.settings}
                width={22}
                height={22}
              />
              </View>
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.section}>
          <P style={styles.sectionTitle}>Support</P>
          {supportOptions.map((item) => (
            <TouchableOpacity
              key={item.label}
              style={styles.optionBox}
              onPress={item.onPress}
            >
              <View style={styles.iconBox}>
                <SvgXml xml={item.icon} width={22} height={22} />
              </View>
              <View style={styles.textBox}>
                <P style={styles.optionLabel}>{item.label}</P>
                <P style={styles.optionDesc}>{item.description}</P>
              </View>
              <SvgXml
                xml={svg.chevronRight || svg.settings}
                width={22}
                height={22}
              />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: colors.white },
  section: { marginTop: 24, paddingHorizontal: 20 },
  sectionTitle: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 15,
    color: colors.textBlack,
    marginBottom: 12,
  },
  optionBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF7E6",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  iconBox: {
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  textBox: { flex: 1 },
  optionLabel: {
    fontFamily: fonts.plusJMedium,
    fontSize: 15,
    color: colors.textBlack,
  },
  optionDesc: {
    fontFamily: fonts.plusJRegular,
    fontSize: 13,
    color: "#888",
    marginTop: 2,
  },
});

export default SupportScreen;
