import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
  SafeAreaView,
  Dimensions,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  FlatList,
  Linking,
  ActivityIndicator,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Button from "../../Components/Button";
import Header from "../../Components/Header";
import P from "../../Components/P";
import H4 from "../../Components/H4";
import Icon from "react-native-vector-icons/Feather";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import { GetWallet } from "../../RequestHandler.tsx/User";

const { width, height } = Dimensions.get("window");
interface PProps {
  navigation: any;
  route?: {
    params?: {
      currency?: string;
    };
  };
}

// Currency selection bottom sheet component
const CardCurrencySelectionBottomSheet = ({
  isVisible,
  onClose,
  onSelectCurrency,
}) => {
  const [selectedCurrency, setSelectedCurrency] = useState(null);

  const currencies = [
    { label: "NGN/Naira Card", value: "ngn", code: "ng" },
    { label: "USD/Dollar Card", value: "usd", code: "us" },
  ];

  const handleContinue = () => {
    if (selectedCurrency) {
      if (selectedCurrency === "ngn") {
        onSelectCurrency(selectedCurrency);
      } else {
        Linking.openURL("https://getly.app/")
      }
      onClose();
    }
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <H4 style={styles.modalTitle}>Select Card Currency</H4>
                <P style={styles.modalSubtitle}>
                  You will be able to make payments{"\n"}with the currency you
                  select
                </P>
              </View>

              <View style={styles.currencyOptions}>
                {currencies.map((currency) => (
                  <TouchableOpacity
                    key={currency.value}
                    style={[
                      styles.currencyOption,
                      selectedCurrency === currency.value &&
                      styles.selectedCurrencyOption,
                    ]}
                    onPress={() => setSelectedCurrency(currency.value)}
                  >
                    <Image
                      source={{
                        uri: `https://flagcdn.com/w2560/${currency.code.toLowerCase()}.png`,
                      }}
                      style={[
                        styles.currencyIcon,
                        {
                          objectFit: currency.code === "ng" ? "fill" : "cover",
                        },
                      ]}
                    />
                    <P style={styles.currencyLabel}>{currency.label}</P>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={styles.bottomSheetButtonContainer}>
                <Button
                  btnText="Continue"
                  onPress={handleContinue}
                  disabled={!selectedCurrency}
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

// Card details screen component
interface CardDetailsScreenProps {
  currency: string | null;
  onGetCard: () => void;
  onChangeCurrency: () => void;
}

const CardDetailsScreen = ({
  currency,
  onGetCard,
  onChangeCurrency
}: CardDetailsScreenProps) => {
  // Default to NGN if currency is null
  const currencyValue = currency || "ngn";
  return (
    <View style={styles.cardDetailsContainer}>
      <View style={styles.cardInfoBox}>
        <View style={styles.flagContainer}>
          <Image
            source={{
              uri: `https://flagcdn.com/w2560/${currencyValue === "ngn" ? "ng" : "us"
                }.png`,
            }}
            style={[
              styles.flagIcon,
              { objectFit: currencyValue === "ngn" ? "fill" : "cover" },
            ]}
          />
          <P style={styles.currencyTitle}>
            {currencyValue === "ngn" ? "NGN" : "USD"}
          </P>
        </View>
        <View style={styles.featuresList}>
          <View style={styles.featureItem}>
            <SvgXml xml={svg.circleCheck} />
            <P style={styles.featureText}>
              {currencyValue === "ngn"
                ? "Accepted across Nigeria"
                : "Accepted globally"}
            </P>
          </View>
          <View style={styles.featureItem}>
            <SvgXml xml={svg.circleCheck} />
            <P style={styles.featureText}>
              {currencyValue === "ngn"
                ? "Fund in USD and get NGN instantly"
                : "Fund in any currency"}
            </P>
          </View>
          <View style={styles.featureItem}>
            <SvgXml xml={svg.circleCheck} />
            <P style={styles.featureText}>Local Billing Address</P>
          </View>
          <View style={styles.featureItem}>
            <SvgXml xml={svg.circleCheck} />
            <P style={styles.featureText}>
              Transfer funds between cards easily for free
            </P>
          </View>
        </View>
      </View>

      <View style={styles.cardPricingContainer}>
        <P style={styles.cardTypeTitle}>
          Physical {currencyValue.toUpperCase()} Card
        </P>
        <P style={styles.cardLimits}>
          N4M Spend limit per quarter. Pickup at International Airports &
          Locations across Nigeria
        </P>
      </View>

      <View style={styles.cardActionButtons}>
        <Button
          btnText={
            currencyValue === "ngn"
              ? "Get NGN Card for N5,000"
              : "Get USD Card for $10"
          }
          onPress={onGetCard}
        />
        <Button
          btnText="Change Card Currency"
          onPress={onChangeCurrency}
          type="alt"
          style={styles.secondaryButton}
        />
      </View>
    </View>
  );
};

// Checkout bottom sheet component
interface CheckoutBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  currency: string | null;
  onSelectPaymentMethod: (method: string) => void;
  navigation: any;
}

const CheckoutBottomSheet = ({
  isVisible,
  onClose,
  currency,
  onSelectPaymentMethod,
  navigation
}: CheckoutBottomSheetProps) => {
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [hasWallet, setHasWallet] = useState(false)
  const [loading, setLoading] = useState(false)
  const [accountIds, setAccountId] = useState(null)

  const handleProceed = () => {
    if (selectedMethod) {
      console.log(selectedMethod);
      if (selectedMethod === "ngn_wallet") {
        if (accountIds) {
          navigation.navigate("PaymentNGNScreen", {
            amount: 5000,
            currency: "NGN",
            convertedAmount: 4,
            convertedCurrency: "USD",
            fee: 50,
            totalAmount: 4.5,
            accountIds: accountIds
          });
        }
      } else {
        onSelectPaymentMethod(selectedMethod);
      }
      onClose();
    }
  };

  const checkWallet = async () => {
    setLoading(true);
    try {
      const response = await GetWallet();
      // Update hasWallet based on API response
      if (response?.data?.hasAccount !== undefined) {
        setHasWallet(response.data.hasAccount);
        setAccountId(response.data.accountIds)
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    checkWallet()
  }, [])

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <H4 style={styles.modalTitle}>Checkout</H4>
                <P style={styles.modalSubtitle}>
                  Use any of the payment methods below to make your payment of{" "}
                  <P
                    style={{
                      fontSize: 12,
                      fontFamily: fonts.plusJMedium,
                      color: colors.textAsh,
                    }}
                  >
                    {currency === "usd" ? "$10" : "N5,000"}
                  </P>
                </P>
              </View>
              {/* Payment Methods */}
              {loading ? <View>
                <ActivityIndicator size="large" color={colors.black} />
              </View> : <>

                <View style={styles.paymentMethodsContainer}>
                  {hasWallet && <TouchableOpacity
                    style={[
                      styles.paymentMethodOption,
                      styles.primaryPaymentOption,
                      selectedMethod === "ngn_wallet" &&
                      styles.selectedPaymentMethod,
                    ]}
                    onPress={() => setSelectedMethod("ngn_wallet")}
                  >
                    <P style={styles.paymentMethodLabel}>
                      Pay with your {currency === "usd" ? "USD" : "NGN"} wallet
                    </P>
                  </TouchableOpacity>}
                  <TouchableOpacity
                    style={[
                      styles.paymentMethodOption,
                      styles.primaryPaymentOption,
                      selectedMethod === "usd_card" &&
                      styles.selectedPaymentMethod,
                    ]}
                    onPress={() => setSelectedMethod("usd_card")}
                  >
                    <P style={styles.paymentMethodLabel}>
                      Pay with {currency === "usd" ? "NGN naira" : "USD dollar"} card
                    </P>
                  </TouchableOpacity>
                </View>
                {/* Proceed Button */}
                <View style={styles.bottomSheetButtonContainer}>
                  <Button
                    btnText="Proceed to Payment"
                    onPress={handleProceed}
                    disabled={!selectedMethod}
                  />
                </View>
              </>}

            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default function OrderPhysicalCardScreen({
  navigation,
  route,
}: PProps) {
  // Get currency from route params
  const routeCurrency = route?.params?.currency;
  console.log("Route params currency:", routeCurrency);

  const [showCurrencySelection, setShowCurrencySelection] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState(routeCurrency || null);
  const [showCheckout, setShowCheckout] = useState(false);

  // Function to handle currency selection
  const handleCurrencySelection = (currency: string) => {
    console.log("Currency selected:", currency);
    setSelectedCurrency(currency);
  };

  // Function to handle get card button click
  const handleGetCard = () => {
    setShowCheckout(true);
  };

  // Function to handle change currency button click
  const handleChangeCurrency = () => {
    setShowCurrencySelection(true);
  };

  // Function to handle payment method selection
  const handlePaymentMethodSelection = (method: string) => {
    console.log("Payment method selected:", method);
    // Navigate to payment screen if USD card is selected
    if (method === "usd_card") {
      // Navigate to payment screen
      navigation.navigate("PaymentScreen", {
        amount: 5000,
        currency: "NGN",
        convertedAmount: 4,
        convertedCurrency: "USD",
        fee: 0.5,
        totalAmount: 4.5,
      });
    } else {
      // Handle NGN wallet payment
      // This would typically involve an API call
      // For demo purposes, navigate directly to pickup location screen
      navigation.navigate("PickupLocationScreen", {
        cardType: selectedCurrency || "ngn"
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header
          title={"Order a physical card"}
          showNotification={false}
        />

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <CardDetailsScreen
            currency={selectedCurrency}
            onGetCard={handleGetCard}
            onChangeCurrency={handleChangeCurrency}
          />
        </ScrollView>

        {/* Currency Selection Bottom Sheet */}
        <CardCurrencySelectionBottomSheet
          isVisible={showCurrencySelection}
          onClose={() => setShowCurrencySelection(false)}
          onSelectCurrency={handleCurrencySelection}
        />

        {/* Checkout Bottom Sheet */}
        <CheckoutBottomSheet
          navigation={navigation}
          isVisible={showCheckout}
          onClose={() => setShowCheckout(false)}
          currency={selectedCurrency}
          onSelectPaymentMethod={handlePaymentMethodSelection}
        />
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 40,
    alignItems: "center",
    flexGrow: 1,
  },
  cardImageContainer: {
    alignItems: "center",
    marginTop: (15 * height) / 100,
    marginBottom: 20,
  },
  cardImage: {
    width: 130,
    height: 130,
  },
  orderCardContent: {
    alignItems: "center",
  },
  orderCardTitle: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 24,
    color: colors.textBlack,
    marginBottom: 16,
  },
  orderCardDescription: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textAsh,
    textAlign: "center",
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  actionButtonContainer: {
    width: "100%",
    marginTop: 30,
  },
  actionButton: {
    backgroundColor: colors.primary,
    // Shadow for iOS
    shadowColor: colors.textBlack,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    // Shadow for Android
    elevation: 4,
  },

  // Modal and bottom sheet styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: (14 * height) / 100,
    maxHeight: "80%",
  },
  modalHeader: {
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 16,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    marginBottom: 8,
    textAlign: "center",
    marginTop: 16,
  },
  modalSubtitle: {
    fontSize: 12,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    lineHeight: 20,
    textAlign: "center",
  },

  // Currency selection styles
  currencyOptions: {
    marginBottom: 20,
  },
  currencyOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 8,
    marginBottom: 12,
  },
  selectedCurrencyOption: {
    borderColor: colors.primary,
  },
  currencyIcon: {
    width: 24,
    height: 24,
    marginRight: 12,
    borderRadius: 12,
  },
  currencyLabel: {
    flex: 1,
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
  },
  radioSelected: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  bottomSheetButtonContainer: {
    marginTop: 10,
    marginBottom: 10,
  },

  // Card details screen styles
  cardDetailsContainer: {
    width: "100%",
    paddingVertical: 20,
  },
  cardInfoBox: {
    backgroundColor: "#DFDFF5",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  flagContainer: {
    marginBottom: 10,
    flexDirection: "row",
    gap: 8,
  },
  flagIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
  },
  currencyTitle: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  featuresList: {
    marginTop: 10,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  featureText: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginLeft: 10,
  },
  cardPricingContainer: {
    marginBottom: 35,
  },
  cardTypeTitle: {
    fontSize: 16,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    marginBottom: 8,
    textAlign: "center",
    marginTop: 20,
  },
  cardLimits: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    lineHeight: 20,
    textAlign: "center",
  },
  cardActionButtons: {
    gap: 12,
  },
  secondaryButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: colors.primary,
  },

  // Checkout styles
  checkoutContainer: {
    width: "100%",
    paddingVertical: 20,
  },
  checkoutSection: {
    marginTop: 24,
  },
  checkoutTitle: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    marginBottom: 8,
    textAlign: "center",
  },
  checkoutSubtitle: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    lineHeight: 20,
    textAlign: "center",
    marginBottom: 16,
  },
  paymentMethodsContainer: {
    marginVertical: 20,
  },
  paymentMethodOption: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  primaryPaymentOption: {
    borderWidth: 1,
    borderColor: "#E5E7EB",
    backgroundColor: colors.white,
  },
  selectedPaymentMethod: {
    borderColor: colors.primary,
  },
  paymentMethodLabel: {
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
  },
  proceedButtonContainer: {
    marginTop: 10,
  },
});
