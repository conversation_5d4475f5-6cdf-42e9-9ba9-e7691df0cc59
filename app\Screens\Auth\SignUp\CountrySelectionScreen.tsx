import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  FlatList,
  TextInput,
  Modal,
  Image,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import PageHeader from "../../../Components/PageHeader";
import Icon from "react-native-vector-icons/Feather";
import { SvgUri, SvgXml } from "react-native-svg";
import { CountryList } from "../../../Components/CountryList";

const { width, height } = Dimensions.get("window");

// List of countries with codes for flag images
const countries = CountryList.sort((a, b) => a.name.localeCompare(b.name));
type Country = {
  name: string;
  code: string;
};
export default function CountrySelectionScreen({ navigation }) {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredCountries, setFilteredCountries] = useState(countries);

  // Filter countries based on search query
  useEffect(() => {
    if (searchQuery) {
      const filtered = countries.filter((country) =>
        country.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCountries(filtered);
    } else {
      setFilteredCountries(countries);
    }
  }, [searchQuery]);

  const handleSelectCountry = (country) => {
    setSelectedCountry(country);
    setModalVisible(false);
    setSearchQuery("");
  };

  const handleContinue = () => {
    if (selectedCountry.code === "NG") {
      navigation.navigate("IDInformationScreen", {
        country: selectedCountry,
      });
    } else {
      navigation.navigate("VerifyIdentityScreen", { country: selectedCountry });
    }
  };

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSearchQuery("");
  };
  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <PageHeader
            currentPage={3}
            totalPages={6}
            onBack={() => navigation.pop()}
          />

          <View style={styles.contentContainer}>
            <H4 style={styles.mainTitle}>Select your Country of Origin</H4>
            <P style={styles.subtitle}>
              For identity verification and to comply with legal regulations,
              please select your country of origin
            </P>

            {/* Country selector button */}
            <TouchableOpacity
              style={styles.dropdownSelector}
              onPress={openModal}
            >
              {selectedCountry ? (
                <View style={styles.selectedCountryContainer}>
                  <Image
                    source={{
                      uri: `https://flagcdn.com/w2560/${selectedCountry.code.toLowerCase()}.png`,
                    }}
                    style={[
                      styles.selectedFlag,
                      {
                        objectFit:
                          selectedCountry.code === "NG" ? "fill" : "cover",
                      },
                    ]}
                  />
                  <P style={styles.dropdownText}>{selectedCountry.name}</P>
                </View>
              ) : (
                <P style={styles.dropdownText}>Choose your nationality</P>
              )}
              <Icon name="chevron-down" size={20} color="#888" />
            </TouchableOpacity>
          </View>

          {selectedCountry && (
            <View style={styles.buttonContainer}>
              <Button btnText="Continue" onPress={handleContinue} />
            </View>
          )}

          {/* Country Selection Modal */}
          <Modal
            animationType="slide"
            transparent={true}
            visible={modalVisible}
            onRequestClose={closeModal}
            statusBarTranslucent
          >
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <TouchableOpacity
                    onPress={closeModal}
                    style={styles.closeButton}
                  >
                    <Icon name="x" size={24} color={colors.textBlack} />
                  </TouchableOpacity>
                  <H4 style={styles.modalTitle}>Select Country</H4>
                  <View style={styles.emptySpace} />
                </View>

                <View style={styles.searchContainer}>
                  <Icon
                    name="search"
                    size={20}
                    color="#888"
                    style={styles.searchIcon}
                  />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search countries"
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    autoFocus
                  />
                  {searchQuery ? (
                    <TouchableOpacity onPress={() => setSearchQuery("")}>
                      <Icon name="x" size={20} color="#888" />
                    </TouchableOpacity>
                  ) : null}
                </View>

                <FlatList
                  data={filteredCountries}
                  keyExtractor={(item) => item.code}
                  style={styles.countryList}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.countryItem}
                      onPress={() => handleSelectCountry(item)}
                    >
                      <Image
                        source={{
                          uri: `https://flagcdn.com/w2560/${item.code.toLowerCase()}.png`,
                        }}
                        width={30}
                        style={[
                          styles.flagImage,
                          { objectFit: item.code === "NG" ? "fill" : "cover" },
                        ]}
                      />
                      <P style={styles.countryName}>{item.name}</P>
                    </TouchableOpacity>
                  )}
                  showsVerticalScrollIndicator={true}
                />
              </View>
            </View>
          </Modal>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  contentContainer: {
    paddingHorizontal: 20,
    flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 30,
  },
  dropdownSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    padding: 16,
  },
  selectedCountryContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  selectedFlag: {
    width: 24,
    height: 24,
    marginRight: 10,
    borderRadius: 100,
  },
  dropdownText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textBlack,
  },
  buttonContainer: {
    width: "90%",
    marginBottom: (5 * height) / 100,
    alignSelf: "center",
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    maxWidth: 500,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "80%",
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  closeButton: {
    padding: 4,
  },
  modalTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 18,
    color: colors.textBlack,
  },
  emptySpace: {
    width: 24, // Same width as the close button for balanced layout
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    backgroundColor: colors.primarySubtle,
    margin: 16,
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
  },
  countryList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  countryItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  flagImage: {
    width: 30,
    height: 30,
    marginRight: 16,
    borderRadius: 100,
  },
  countryName: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textBlack,
  },
});
