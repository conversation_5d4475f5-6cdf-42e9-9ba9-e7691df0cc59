import { countries } from '../data/countries';

// Nationality mapping for common countries
const nationalityMap: { [key: string]: string } = {
  // Africa
  'NG': 'Nigerian',
  'GH': 'Ghanaian',
  'KE': 'Kenyan',
  'ZA': 'South African',
  'EG': 'Egyptian',
  'MA': 'Moroccan',
  'TN': 'Tunisian',
  'ET': 'Ethiopian',
  'UG': 'Ugandan',
  'TZ': 'Tanzanian',
  'RW': 'Rwandan',
  'SN': 'Senegalese',
  'CI': 'Ivorian',
  'BF': 'Burkinabé',
  'ML': 'Malian',
  'NE': 'Nigerien',
  'TD': 'Chadian',
  'CM': 'Cameroonian',
  'GA': 'Gabonese',
  'CG': 'Congolese',
  'CD': 'Congolese',
  'AO': 'Angolan',
  'ZM': 'Zambian',
  'ZW': 'Zimbabwean',
  'BW': 'Botswanan',
  'NA': 'Namibian',
  'SZ': 'Swazi',
  'LS': '<PERSON>sotho',
  'MW': 'Malawian',
  'MZ': 'Mozambican',
  'MG': 'Malagasy',
  'M<PERSON>': 'Mauritian',
  'SC': 'Seychellois',
  'DJ': 'Djiboutian',
  'SO': 'Somali',
  'ER': 'Eritrean',
  'SD': 'Sudanese',
  'SS': 'South Sudanese',
  'LY': 'Libyan',
  'DZ': 'Algerian',
  'LR': 'Liberian',
  'SL': 'Sierra Leonean',
  'GN': 'Guinean',
  'GW': 'Guinea-Bissauan',
  'GM': 'Gambian',
  'CV': 'Cape Verdean',
  'ST': 'São Toméan',
  'GQ': 'Equatorial Guinean',
  'CF': 'Central African',
  'EH': 'Sahrawi',

  // North America
  'US': 'American',
  'CA': 'Canadian',
  'MX': 'Mexican',
  'GT': 'Guatemalan',
  'BZ': 'Belizean',
  'SV': 'Salvadoran',
  'HN': 'Honduran',
  'NI': 'Nicaraguan',
  'CR': 'Costa Rican',
  'PA': 'Panamanian',
  'CU': 'Cuban',
  'JM': 'Jamaican',
  'HT': 'Haitian',
  'DO': 'Dominican',
  'PR': 'Puerto Rican',
  'TT': 'Trinidadian',
  'BB': 'Barbadian',
  'GD': 'Grenadian',
  'LC': 'Saint Lucian',
  'VC': 'Saint Vincentian',
  'AG': 'Antiguan',
  'KN': 'Kittitian',
  'DM': 'Dominican',
  'BS': 'Bahamian',

  // South America
  'BR': 'Brazilian',
  'AR': 'Argentine',
  'CL': 'Chilean',
  'PE': 'Peruvian',
  'CO': 'Colombian',
  'VE': 'Venezuelan',
  'EC': 'Ecuadorian',
  'BO': 'Bolivian',
  'PY': 'Paraguayan',
  'UY': 'Uruguayan',
  'GY': 'Guyanese',
  'SR': 'Surinamese',
  'GF': 'French Guianese',

  // Europe
  'GB': 'British',
  'FR': 'French',
  'DE': 'German',
  'IT': 'Italian',
  'ES': 'Spanish',
  'PT': 'Portuguese',
  'NL': 'Dutch',
  'BE': 'Belgian',
  'CH': 'Swiss',
  'AT': 'Austrian',
  'SE': 'Swedish',
  'NO': 'Norwegian',
  'DK': 'Danish',
  'FI': 'Finnish',
  'IS': 'Icelandic',
  'IE': 'Irish',
  'PL': 'Polish',
  'CZ': 'Czech',
  'SK': 'Slovak',
  'HU': 'Hungarian',
  'RO': 'Romanian',
  'BG': 'Bulgarian',
  'HR': 'Croatian',
  'SI': 'Slovenian',
  'RS': 'Serbian',
  'BA': 'Bosnian',
  'ME': 'Montenegrin',
  'MK': 'Macedonian',
  'AL': 'Albanian',
  'GR': 'Greek',
  'CY': 'Cypriot',
  'MT': 'Maltese',
  'LU': 'Luxembourgish',
  'MC': 'Monégasque',
  'AD': 'Andorran',
  'SM': 'Sammarinese',
  'VA': 'Vatican',
  'LI': 'Liechtensteiner',
  'RU': 'Russian',
  'UA': 'Ukrainian',
  'BY': 'Belarusian',
  'MD': 'Moldovan',
  'LT': 'Lithuanian',
  'LV': 'Latvian',
  'EE': 'Estonian',

  // Asia
  'CN': 'Chinese',
  'JP': 'Japanese',
  'KR': 'South Korean',
  'KP': 'North Korean',
  'IN': 'Indian',
  'PK': 'Pakistani',
  'BD': 'Bangladeshi',
  'LK': 'Sri Lankan',
  'NP': 'Nepalese',
  'BT': 'Bhutanese',
  'MV': 'Maldivian',
  'AF': 'Afghan',
  'IR': 'Iranian',
  'IQ': 'Iraqi',
  'SY': 'Syrian',
  'LB': 'Lebanese',
  'JO': 'Jordanian',
  'IL': 'Israeli',
  'PS': 'Palestinian',
  'SA': 'Saudi',
  'AE': 'Emirati',
  'QA': 'Qatari',
  'BH': 'Bahraini',
  'KW': 'Kuwaiti',
  'OM': 'Omani',
  'YE': 'Yemeni',
  'TR': 'Turkish',
  'GE': 'Georgian',
  'AM': 'Armenian',
  'AZ': 'Azerbaijani',
  'KZ': 'Kazakhstani',
  'KG': 'Kyrgyzstani',
  'TJ': 'Tajikistani',
  'TM': 'Turkmen',
  'UZ': 'Uzbekistani',
  'MN': 'Mongolian',
  'TH': 'Thai',
  'VN': 'Vietnamese',
  'LA': 'Laotian',
  'KH': 'Cambodian',
  'MM': 'Burmese',
  'MY': 'Malaysian',
  'SG': 'Singaporean',
  'ID': 'Indonesian',
  'BN': 'Bruneian',
  'PH': 'Filipino',
  'TL': 'Timorese',

  // Oceania
  'AU': 'Australian',
  'NZ': 'New Zealander',
  'FJ': 'Fijian',
  'PG': 'Papua New Guinean',
  'SB': 'Solomon Islander',
  'VU': 'Vanuatuan',
  'NC': 'New Caledonian',
  'PF': 'French Polynesian',
  'WS': 'Samoan',
  'TO': 'Tongan',
  'TV': 'Tuvaluan',
  'KI': 'I-Kiribati',
  'NR': 'Nauruan',
  'PW': 'Palauan',
  'FM': 'Micronesian',
  'MH': 'Marshallese',
};

/**
 * Convert country code to country name
 * @param countryCode - 2-letter ISO country code (e.g., "NG") or 3-letter code (e.g., "NGA")
 * @returns Country name (e.g., "Nigeria") or the original code if not found
 */
export const getCountryName = (countryCode: string): string => {
  if (!countryCode) return '';
  
  // Convert 3-letter codes to 2-letter codes for common cases
  const codeMap: { [key: string]: string } = {
    'NGA': 'NG',
    'USA': 'US',
    'GBR': 'GB',
    'FRA': 'FR',
    'DEU': 'DE',
    'ITA': 'IT',
    'ESP': 'ES',
    'CAN': 'CA',
    'AUS': 'AU',
    'JPN': 'JP',
    'CHN': 'CN',
    'IND': 'IN',
    'BRA': 'BR',
    'RUS': 'RU',
    'ZAF': 'ZA',
    'EGY': 'EG',
    'KEN': 'KE',
    'GHA': 'GH',
  };
  
  // Convert to 2-letter code if it's a 3-letter code
  const normalizedCode = codeMap[countryCode.toUpperCase()] || countryCode.toUpperCase();
  
  // Find country in our countries data
  const country = countries.find(c => 
    c.isoCode === normalizedCode || 
    c.value === normalizedCode ||
    c.value === countryCode.toUpperCase()
  );
  
  return country ? country.label : countryCode;
};

/**
 * Get nationality from country code
 * @param countryCode - 2-letter ISO country code (e.g., "NG") or 3-letter code (e.g., "NGA")
 * @returns Nationality (e.g., "Nigerian") or a generated nationality if not found
 */
export const getNationality = (countryCode: string): string => {
  if (!countryCode) return '';
  
  // Convert 3-letter codes to 2-letter codes for common cases
  const codeMap: { [key: string]: string } = {
    'NGA': 'NG',
    'USA': 'US',
    'GBR': 'GB',
    'FRA': 'FR',
    'DEU': 'DE',
    'ITA': 'IT',
    'ESP': 'ES',
    'CAN': 'CA',
    'AUS': 'AU',
    'JPN': 'JP',
    'CHN': 'CN',
    'IND': 'IN',
    'BRA': 'BR',
    'RUS': 'RU',
    'ZAF': 'ZA',
    'EGY': 'EG',
    'KEN': 'KE',
    'GHA': 'GH',
  };
  
  // Convert to 2-letter code if it's a 3-letter code
  const normalizedCode = codeMap[countryCode.toUpperCase()] || countryCode.toUpperCase();
  
  // Check our nationality map first
  const nationality = nationalityMap[normalizedCode];
  if (nationality) {
    return nationality;
  }
  
  // If not found, try to generate from country name
  const countryName = getCountryName(countryCode);
  if (countryName && countryName !== countryCode) {
    // Simple rules for generating nationality
    if (countryName.endsWith('ia')) {
      return countryName.slice(0, -2) + 'ian';
    } else if (countryName.endsWith('land')) {
      return countryName.slice(0, -4) + 'lander';
    } else if (countryName.endsWith('stan')) {
      return countryName.slice(0, -4) + 'stani';
    } else {
      return countryName + 'an'; 
    }
  }
  
  return countryCode;
};

/**
 * Get both country name and nationality from country code
 * @param countryCode - 2-letter ISO country code (e.g., "NG") or 3-letter code (e.g., "NGA")
 * @returns Object with country name and nationality
 */
export const getCountryInfo = (countryCode: string): { country: string; nationality: string } => {
  return {
    country: getCountryName(countryCode),
    nationality: getNationality(countryCode),
  };
};
