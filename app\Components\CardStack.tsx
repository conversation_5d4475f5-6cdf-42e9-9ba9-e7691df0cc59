import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ImageBackground,
  Animated,
  Dimensions,
  Image,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import H4 from "./H4";

const { width } = Dimensions.get("window");
const CARD_WIDTH = width * 0.9;
const CARD_HEIGHT = 200;

interface CardData {
  id: string;
  name: string;
  balance: string;
  cardNumber: string;
  expiryDate: string;
  holderName: string;
  color: string;
  currency: string;
}

interface CardStackProps {
  cards: CardData[];
  onCardPress: (card: CardData) => void;
}

const CardStack: React.FC<CardStackProps> = ({ cards, onCardPress }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);

  // Animation values for each card
  const cardAnimations = cards.map(() => new Animated.Value(0));

  // Toggle between stacked and expanded states
  const toggleExpand = () => {
    console.log("Toggle expand called, current state:", isExpanded);
    if (isExpanded) {
      // Collapse the stack
      Animated.parallel(
        cardAnimations.map((anim) =>
          Animated.timing(anim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          })
        )
      ).start(() => {
        setIsExpanded(false);
        setSelectedCardId(null);
      });
    } else {
      // Expand the stack
      setIsExpanded(true);
      Animated.stagger(
        100,
        cardAnimations.map((anim, index) =>
          Animated.timing(anim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          })
        )
      ).start();
    }
  };

  // Handle card selection
  const handleCardPress = (card: CardData) => {
    console.log("Card pressed, isExpanded:", isExpanded);
    if (isExpanded) {
      // If already expanded, select a card
      setSelectedCardId(card.id);
      onCardPress(card);
    } else {
      // If collapsed, expand the stack first
      console.log("Expanding stack on card press");
      toggleExpand();
    }
  };

  // Get card background color
  const getCardBackgroundColor = (color: string) => {
    switch (color) {
      case "gold":
        return "#FFCC66";
      case "navy":
        return "#0A1128";
      case "purple":
        return "#4A148C";
      default:
        return "#0A1128"; // Default to navy
    }
  };

  // Check if the card color is dark
  const isDarkColor = (color: string) => {
    return color === "navy" || color === "purple";
  };

  return (
    <View style={styles.container}>
      {/* Tap area to expand/collapse the stack */}
      {/* {!isExpanded && (
        <TouchableOpacity
          style={styles.stackTapArea}
          onPress={toggleExpand}
          activeOpacity={0.8}
        >
          <Text style={styles.stackTapText}>Tap to view all cards</Text>
        </TouchableOpacity>
      )} */}

      {cards.map((card, index) => {
        // Calculate the position of each card in the stack
        const cardColor = getCardBackgroundColor(card.color);
        const isCardDark = isDarkColor(card.color);

        // Calculate transforms for animation
        const translateY = cardAnimations[index].interpolate({
          inputRange: [0, 1],
          outputRange: [index * -10, index * 80], // Stacked offset to expanded offset (increased for better spread)
        });

        // Add translateX for a fan effect when expanded
        const translateX = cardAnimations[index].interpolate({
          inputRange: [0, 1],
          outputRange: [0, index % 2 === 0 ? -20 : 20], // Alternate left and right for a fan effect
        });

        const scale = cardAnimations[index].interpolate({
          inputRange: [0, 1],
          outputRange: [1 - index * 0.05, 1], // Slightly smaller when stacked
        });

        const opacity = cardAnimations[index].interpolate({
          inputRange: [0, 1],
          outputRange: [index < 3 ? 1 : 0, 1], // Only show top 3 cards when stacked
        });

        // Add rotation for a more natural fan effect
        const rotate = cardAnimations[index].interpolate({
          inputRange: [0, 1],
          outputRange: ["0deg", index % 2 === 0 ? "-5deg" : "5deg"], // Alternate rotation
        });

        return (
          <Animated.View
            key={card.id}
            style={[
              styles.cardWrapper,
              {
                zIndex: cards.length - index,
                transform: [
                  { translateY },
                  { translateX },
                  { scale },
                  { rotate },
                ],
                opacity,
              },
            ]}
          >
            <TouchableOpacity
              style={[
                styles.card,
                { backgroundColor: cardColor },
                selectedCardId === card.id && styles.selectedCard,
              ]}
              onPress={() => handleCardPress(card)}
              activeOpacity={0.9}
            >
              <ImageBackground
                source={
                  isCardDark
                    ? require("../assets/BlueLogoMark.png")
                    : require("../assets/bg-background.png")
                }
                style={{
                  width: "60%",
                  height: "110%",
                  position: "absolute",
                  right: -90,
                  opacity: isCardDark ? 0.5 : 1,
                }}
                resizeMode="cover"
              />
              <View style={styles.cardHeader}>
                <Text style={[styles.cardName, isCardDark && styles.whiteText]}>
                  {card.name}
                </Text>
                <H4
                  style={[styles.cardBalance, isCardDark && styles.whiteText]}
                >
                  {card.currency} {card.balance}
                </H4>
              </View>
              <Text style={[styles.cardNumber, isCardDark && styles.whiteText]}>
                {card.cardNumber}
              </Text>
              <Text style={[styles.cardExpiry, isCardDark && styles.whiteText]}>
                VALID TILL {card.expiryDate}
              </Text>

              <View style={styles.cardBrand}>
                <Text
                  style={[styles.holderName, isCardDark && styles.whiteText]}
                >
                  {card.holderName}
                </Text>
                <View style={styles.masterCardIcon}>
                  <View style={[styles.circle, styles.redCircle]} />
                  <View style={[styles.circle, styles.yellowCircle]} />
                </View>
              </View>
            </TouchableOpacity>
          </Animated.View>
        );
      })}
      {/* Stack indicator dots */}
      {cards.length > 1 && (
        <View style={styles.stackIndicator}>
          {cards.map((card, i) => (
            <View
              key={`indicator-${card.id}`}
              style={[
                styles.stackDot,
                isExpanded && i === 0 && styles.activeDot,
              ]}
            />
          ))}
        </View>
      )}
      {/* Expand/Collapse button */}
      {cards.length > 1 && (
        <TouchableOpacity style={styles.expandButton} onPress={toggleExpand}>
          <Text style={styles.expandButtonText}>
            {isExpanded ? "Collapse" : "View All Cards"}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    marginVertical: 20,
    height: CARD_HEIGHT + 100, // Extra space for expanded cards
  },
  cardWrapper: {
    position: "absolute",
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  card: {
    width: "100%",
    height: "100%",
    borderRadius: 12,
    padding: 16,
    position: "relative",
    overflow: "hidden",
  },
  selectedCard: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  cardName: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardBalance: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardNumber: {
    fontSize: 18,
    fontFamily: fonts.plusJBold,
    color: colors.textBlack,
    marginTop: 8,
    letterSpacing: 1,
  },
  cardExpiry: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginTop: 8,
  },
  holderName: {
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
  },
  cardBrand: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    position: "absolute",
    bottom: 16,
    left: 16,
    right: 16,
  },
  masterCardIcon: {
    flexDirection: "row",
    alignItems: "center",
  },
  circle: {
    width: 25,
    height: 25,
    borderRadius: 100,
  },
  redCircle: {
    backgroundColor: "#EB001B",
    marginRight: -8,
  },
  yellowCircle: {
    backgroundColor: "#F79E1B",
  },
  whiteText: {
    color: colors.white,
  },
  expandButton: {
    position: "absolute",
    bottom: 0,
    backgroundColor: "transparent",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  expandButtonText: {
    color: colors.primary,
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
  },
  stackTapArea: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: 50,
    zIndex: 999,
    justifyContent: "center",
    alignItems: "center",
  },
  stackTapText: {
    color: colors.primary,
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  stackIndicator: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    bottom: 60,
    left: 0,
    right: 0,
  },
  stackDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#D9D9D9",
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: colors.primary,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
});

export default CardStack;
