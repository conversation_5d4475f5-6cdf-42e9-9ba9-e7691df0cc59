import React from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  FlatList,
  TouchableWithoutFeedback,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import P from "../Components/P";
import H4 from "../Components/H4";
import Icon from "react-native-vector-icons/Feather";

const { width, height } = Dimensions.get("window");

interface DropdownItem {
  label: string;
  value: string;
}

interface BottomSheetDropdownProps {
  isVisible: boolean;
  onClose: () => void;
  title: string;
  items: DropdownItem[];
  onSelectItem: (item: DropdownItem) => void;
  selectedValue?: string;
}

const BottomSheetDropdown: React.FC<BottomSheetDropdownProps> = ({
  isVisible,
  onClose,
  title,
  items,
  onSelectItem,
  selectedValue,
}) => {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <Icon name="x" size={24} color={colors.textBlack} />
                </TouchableOpacity>
                <H4 style={styles.modalTitle}>{title}</H4>
                <View style={styles.emptySpace} />
              </View>

              <FlatList
                data={items}
                keyExtractor={(item, index) => index.toString()}
                style={styles.itemList}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[styles.item]}
                    onPress={() => {
                      onSelectItem(item);
                      onClose();
                    }}
                  >
                    <P
                      style={[
                        styles.itemText,
                        selectedValue === item.value && styles.selectedItemText,
                      ]}
                    >
                      {item.label}
                    </P>
                    {selectedValue === item.value && (
                      <Icon name="check" size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                )}
              />
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "70%",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  closeButton: {
    padding: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: fonts.plusJMedium,
    color: colors.textBlack,
  },
  emptySpace: {
    width: 24, // Same width as the close button for balanced layout
  },
  itemList: {
    maxHeight: 300,
  },
  item: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
  },
  itemText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  selectedItemText: {
    fontFamily: fonts.plusJMedium,
    color: colors.primary,
  },
});

export default BottomSheetDropdown;
