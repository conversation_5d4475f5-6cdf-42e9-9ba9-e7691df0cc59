import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  TextInput,
  ScrollView,
} from "react-native";
import { colors } from "../../../../Config/colors";
import Div from "../../../../Components/Div";
import H4 from "../../../../Components/H4";
import P from "../../../../Components/P";
import { fonts } from "../../../../Config/Fonts";
import Button from "../../../../Components/Button";
import PageHeader from "../../../../Components/PageHeader";
import Icon from "react-native-vector-icons/Feather";
import Input from "../../../../Components/Input";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../../Config/Svg";

const { width, height } = Dimensions.get("window");

const occupations = [
  "Student",
  "Engineer",
  "Doctor",
  "Teacher",
  "Business Owner",
  "Accountant",
  "Designer",
  "Developer",
  "Consultant",
  "Other",
];

export default function AdditionalInformationScreen({ navigation, route }) {
  const { idPhoto, selfiePhoto } = route.params || {};
  const [address, setAddress] = useState("");
  const [occupation, setOccupation] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [dateOfBirth, setDateOfBirth] = useState("");
  const [dateOfBirthError, setDateOfBirthError] = useState(false);
  const [showOccupationModal, setShowOccupationModal] = useState(false);
  const [phoneError, setPhoneError] = useState(false);

  const validatePhone = (text) => {
    // Simple validation - can be enhanced
    const isValid = text.length >= 10;
    setPhoneError(!isValid);
    setPhoneNumber(text);
  };

  const validateDateOfBirth = (text) => {
    setDateOfBirth(text);

    // Auto-format as user types (DD-MM-YYYY)
    if (text.length === 2 && !text.includes("-")) {
      setDateOfBirth(`${text}-`);
    } else if (text.length === 5 && text.charAt(4) !== "-") {
      setDateOfBirth(`${text.substring(0, 5)}-`);
    }

    // Validate format
    const dateRegex = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/;
    const isValidFormat = dateRegex.test(text);

    if (isValidFormat) {
      // Check if it's a valid date
      const [day, month, year] = text.split("-").map(Number);
      const date = new Date(year, month - 1, day);
      const isValidDate =
        date.getDate() === day &&
        date.getMonth() === month - 1 &&
        date.getFullYear() === year;

      // Check if date is not in the future
      const isNotFuture = date <= new Date();

      setDateOfBirthError(!(isValidDate && isNotFuture));
    } else {
      setDateOfBirthError(text.length > 0);
    }
  };

  const handleContinue = () => {
    // Validate all fields
    if (
      address &&
      occupation &&
      phoneNumber &&
      !phoneError &&
      dateOfBirth &&
      !dateOfBirthError
    ) {
      // Navigate to success or completion screen
      console.log("Verification completed");
      navigation.navigate("TransactionPinScreen");
    }
  };

  const isFormValid =
    address &&
    occupation &&
    phoneNumber &&
    !phoneError &&
    dateOfBirth &&
    !dateOfBirthError;

  return (
    <View style={styles.mainContainer}>
      <Div>
        <ScrollView
          style={styles.container}
          contentContainerStyle={styles.scrollContent}
          automaticallyAdjustKeyboardInsets={true}
        >
          <PageHeader
            currentPage={6}
            totalPages={6}
            onBack={() => navigation.pop()}
            type="bars"
          />

          <View style={styles.contentContainer}>
            <H4 style={styles.mainTitle}>Additional Information</H4>
            <P style={styles.subtitle}>
              Please provide the following details to complete your
              verification.
            </P>

            <View style={styles.formGroup}>
              <Input
                label={"Current Address"}
                placeholder="Enter your full address"
                value={address}
                onChangeText={setAddress}
                //   onBlur={formikProps.handleBlur("emailOrPhone")}

                autoCapitalize="none"
              />
            </View>

            <View style={styles.formGroup}>
              <P style={styles.label}>Occupation</P>
              <TouchableOpacity
                style={styles.input}
                onPress={() => setShowOccupationModal(true)}
              >
                <P
                  style={occupation ? styles.inputText : styles.placeholderText}
                >
                  {occupation || "Select your occupation"}
                </P>
                <Icon name="chevron-down" size={20} color="#888" />
              </TouchableOpacity>
            </View>

            <View style={styles.formGroup}>
              <Input
                label={"Phone Number"}
                placeholder="+1**************"
                value={phoneNumber}
                onChangeText={validatePhone}
                keyboardType="phone-pad"
                //   onBlur={formikProps.handleBlur("emailOrPhone")}

                autoCapitalize="none"
                error={phoneError}
                errorText={" Please enter a valid phone number"}
              />
              {/* {phoneError && (
                <View style={styles.errorContainer}>
                  <Icon name="alert-circle" size={16} color={colors.error} />
                  <P style={styles.errorText}></P>
                </View>
              )} */}
            </View>

            <View style={styles.formGroup}>
              <Input
                label={"Date of Birth"}
                placeholder="DD-MM-YYYY"
                value={dateOfBirth}
                onChangeText={validateDateOfBirth}
                keyboardType="numeric"
                error={dateOfBirthError}
                errorText={"Please enter a valid date in DD-MM-YYYY format"}
                //   onBlur={formikProps.handleBlur("emailOrPhone")}

                autoCapitalize="none"
              />
              {/* {dateOfBirthError && (
                <View style={styles.errorContainer}>
                  <Icon name="alert-circle" size={16} color={colors.error} />
                  <P style={styles.errorText}>
                
                  </P>
                </View>
              )} */}
            </View>

            <View style={styles.securityNoteContainer}>
              <SvgXml xml={svg.info2} />
              <P style={styles.securityNoteText}>
                Your information is encrypted and secure
              </P>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              btnText="Continue"
              onPress={handleContinue}
              btnTextStyle={{ fontFamily: fonts.plusJRegular }}
              disabled={!isFormValid}
              style={!isFormValid ? styles.disabledButton : {}}
            />
          </View>
        </ScrollView>

        {/* Occupation Modal */}
        {showOccupationModal && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <TouchableOpacity onPress={() => setShowOccupationModal(false)}>
                  <Icon name="x" size={24} color={colors.textBlack} />
                </TouchableOpacity>
                <H4 style={styles.modalTitle}>Select Occupation</H4>
                <View style={styles.emptySpace} />
              </View>
              <ScrollView style={styles.modalList}>
                {occupations.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.modalItem}
                    onPress={() => {
                      setOccupation(item);
                      setShowOccupationModal(false);
                    }}
                  >
                    <P
                      style={[
                        styles.modalItemText,
                        {
                          color:
                            occupation === item
                              ? colors.primary
                              : colors.textBlack,
                        },
                      ]}
                    >
                      {item}
                    </P>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        )}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
  },
  scrollContent: {
    flexGrow: 1,
  },
  contentContainer: {
    width: "90%",
    alignSelf: "center",
    // flex: 1,
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textAsh,
    marginBottom: 24,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    color: colors.textBlack,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  inputError: {
    borderColor: colors.error,
  },
  inputText: {
    fontSize: 16,
    color: colors.textBlack,
  },
  placeholderText: {
    fontSize: 16,
    color: "#AAAAAA",
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    marginLeft: 8,
  },
  securityNoteContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primarySubtle,
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  securityNoteText: {
    fontSize: 14,
    color: colors.textBlack,
    marginLeft: 8,
  },
  buttonContainer: {
    width: "90%",
    marginTop: 24,
    alignSelf: "center",
  },
  disabledButton: {
    backgroundColor: "#E0E0E0",
  },
  // Modal styles
  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "70%",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: fonts.plusJMedium,
  },
  emptySpace: {
    width: 24,
  },
  modalList: {
    maxHeight: 300,
  },
  modalItem: {
    padding: 16,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.textBlack,
  },
});
