{"name": "getly", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "^1.16.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.2", "country-state-city": "^3.2.1", "expo": "~52.0.46", "expo-camera": "~16.0.18", "expo-dev-client": "~5.0.20", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.4", "expo-image-picker": "~16.0.6", "expo-router": "~4.0.20", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "formik": "^2.4.6", "react": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-otp-entry": "^1.8.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.4", "react-native-webview": "13.12.5", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "^18.3.20", "typescript": "^5.8.2"}, "private": true}