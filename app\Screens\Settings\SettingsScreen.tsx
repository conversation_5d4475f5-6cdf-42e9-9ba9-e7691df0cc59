import React from "react";
import { View, ScrollView, StyleSheet, TouchableOpacity } from "react-native";
import Header from "../../Components/Header";
import ProfileMenuItem from "../../Components/ProfileMenuItem";
import Div from "../../Components/Div";
import { colors } from "../../Config/colors";
import { svg } from "../../Config/Svg";
import { useNavigation } from "@react-navigation/native";
import { SvgXml } from "react-native-svg";
import P from "../../Components/P";

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  return (
    <Div>
      <ScrollView style={{ width: "100%" }} contentContainerStyle={{ paddingBottom: 32 }}>
        <Header title="Settings" />
        <View style={styles.list}>
          <ProfileMenuItem
            icon={svg.profile1}
            label="Account Information"
            onPress={() => navigation.navigate("AccountInfoScreen")}
          />
          <ProfileMenuItem
            icon={svg.verified}
            label="Verification"
            onPress={() => navigation.navigate("VerificationScreen")}
          />
        </View>
      </ScrollView>
    </Div>
  );
};

const styles = StyleSheet.create({
  list: { marginTop: 24, paddingHorizontal: 20 },
  tabBarPlaceholder: {
    height: 60,
    backgroundColor: "#FAF3E7",
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  menuItemText: {
    marginLeft: 16,
  },
});

export default SettingsScreen; 