import * as React from "react";
import {
  NavigationContainer,
  NavigationContainerRef,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import BottomTabNavigator from "./BottomTabNavigator";
import { CredentailsContext } from "../Context/CredentailsContext";
import OnboardingScreen from "../Screens/OnboardingScreen";
import SignUpScreen1 from "../Screens/Auth/SignUp/SignUpScreen1";
import VerifyEmailScreen from "../Screens/Auth/SignUp/VerifyEmailScreen";
import VerificationSuccessScreen from "../Screens/Auth/SignUp/VerificationSuccessScreen";
import LoginScreen from "../Screens/Auth/SignIn/LoginScreen";
import ForgotPasswordScreen from "../Screens/Auth/SignIn/ForgetPasswordScreen";
import ForgotPasswordEmailScreen from "../Screens/Auth/SignIn/ForgotPasswordEmailVerifyScreen";
import ResetPasswordScreen from "../Screens/Auth/SignIn/ResetPasswordScreen";
import IDInformationScreen from "../Screens/Auth/SignUp/IDInformationScreen";
import SelfieScreen from "../Screens/Auth/SignUp/SelfieScreen";
import IDConfirmationScreen from "../Screens/Auth/SignUp/IDConfirmationScreen";
import DocumentUploadScreen from "../Screens/Auth/SignUp/DocumentUploadScreen";
import TransactionPinScreen from "../Screens/Auth/SignUp/TransactionPinScreen";
import CountrySelectionScreen from "../Screens/Auth/SignUp/CountrySelectionScreen";
import VerifyIdentityScreen from "../Screens/Auth/SignUp/ForeignersKYC/VerifyIdentityScreen";
import PrivacyConsentScreen from "../Screens/Auth/SignUp/ForeignersKYC/PrivacyConsentScreen";
import SelectIDDocumentScreen from "../Screens/Auth/SignUp/ForeignersKYC/SelectIDDocumentScreen";
import IDCaptureScreen from "../Screens/Auth/SignUp/ForeignersKYC/IDCaptureScreen";
import ForeignersSelfieScreen from "../Screens/Auth/SignUp/ForeignersKYC/ForeignersSelfieScreen";
import AdditionalInformationScreen from "../Screens/Auth/SignUp/ForeignersKYC/AdditionalInformationScreen";
import ForeignersKYCScreen from "../Screens/Auth/SignUp/ForeignersKYC/ForeignersKYCScreen";
import OrderPhysicalCardScreen from "../Screens/Card/OrderPhysicalCardScreen";
import PaymentScreen from "../Screens/Card/PaymentScreen";
import PaymentStatusScreen from "../Screens/Card/PaymentStatusScreen";
import PickupLocationScreen from "../Screens/Card/PickupLocationScreen";
import CardActivationScreen from "../Screens/Card/CardActivationScreen";
import ZoomCardScreen from "../Screens/Card/ZoomCardScreen";
import CardSettingsScreen from "../Screens/Card/CardSettingsScreen";
import VerifyPINScreen from "../Screens/Card/VerifyPINScreen";
import ViewCardPINScreen from "../Screens/Card/ViewCardPINScreen";
import ChangeCardPINScreen from "../Screens/Card/ChangeCardPINScreen";
import CardTransactionsScreen from "../Screens/Card/CardTransactionsScreen";
import ChangeCardColorScreen from "../Screens/Card/ChangeCardColorScreen";
import ActivateCardScreen from "../Screens/Card/ActivateCardScreen";
import SetCardPINScreen from "../Screens/Card/SetCardPINScreen";
import CardActivationSuccessScreen from "../Screens/Card/CardActivationSuccessScreen";
import SettingsScreen from "../Screens/Settings/SettingsScreen";
import AccountInfoScreen from "../Screens/Settings/AccountInfoScreen";
import PrivacySecurityScreen from "../Screens/Settings/PrivacySecurityScreen";
import Security2FAScreen from "../Screens/Settings/Security2FAScreen";
import Setup2FAScreen from "../Screens/Settings/Setup2FAScreen";
import SupportScreen from "../Screens/Settings/SupportScreen";
import ChangePasswordScreen from "../Screens/Settings/ChangePasswordScreen";
import ChangeTransactionPINScreen from "../Screens/Settings/ChangeTransactionPINScreen";
import ActivateTransactionHubScreen from "../Screens/Wallet/ActivateTransactionHubScreen";
import InternationalFundScreen from "../Screens/Wallet/InternationalFundScreen";
import TransactionDetailsScreen from "../Screens/TransactionDetailsScreen";
import VerificationScreen from "../Screens/Settings/VerificationScreen";
import SendToNigerianBankScreen from "../Screens/Wallet/SendToNigerianBankScreen";
import PaymentNGNScreen from "../Screens/Card/PaymentNGNScreen";
import ValidatePinScreen from "../Screens/Card/ValidatePinScreen";



// Define the RootStackParamList to properly type the navigation
type RootStackParamList = {
  OnboardingScreen: undefined;
  SignUpScreen1: undefined;
  VerifyEmailScreen: { email: string };
  VerificationSuccessScreen: undefined;
  LoginScreen: undefined;
  ForgotPasswordScreen: undefined;
  ForgotPasswordEmailVerifyScreen: { email: string };
  ResetPasswordScreen: { email: string; otp: string };
  CountrySelectionScreen: undefined;
  DocumentUploadScreen: undefined;
  DocumentUploadScreen1: undefined;
  TransactionPinScreen: undefined;
  TransactionPinScreen1: undefined;
  BottomTabNavigator: undefined;
  BottomTabNavigator1: undefined;
  VerifyIdentityScreen: undefined;
  PrivacyConsentScreen: undefined;
  SelectIDDocumentScreen: undefined;
  IDCaptureScreen: undefined;
  ForeignersSelfieScreen: undefined;
  AdditionalInformationScreen: undefined;
  IDInformationScreen: undefined;
  SelfieScreen: undefined;
  IDConfirmationScreen: undefined;
  SettingsScreen: undefined;
  PrivacySecurityScreen: undefined;
  SupportScreen: undefined;
  TermsScreen: undefined;
  ForeignersKYCScreen: undefined;
  ForeignersKYCScreen1: undefined;
  OrderPhysicalCardScreen: undefined;
  PaymentScreen: {
    amount: number;
    currency: string;
    convertedAmount: number;
    convertedCurrency: string;
    fee: number;
    totalAmount: number;
  };
  PickupLocationScreen: {
    cardType?: string;
  };
  CardActivationScreen: {
    cardType?: string;
    location?: string;
  };
  ZoomCardScreen: {
    cardType?: string;
  };
  CardSettingsScreen: undefined;
  VerifyPINScreen: {
    action?: "view" | "change";
  };
  ViewCardPINScreen: undefined;
  ChangeCardPINScreen: undefined;
  CardTransactionsScreen: undefined;
  ChangeCardColorScreen: {
    currentColor?: string;
  };
  ActivateCardScreen: undefined;
  SetCardPINScreen: undefined;
  CardActivationSuccessScreen: undefined;
  AccountInfoScreen: undefined;
  Security2FAScreen: undefined;
  Setup2FAScreen: undefined;
  TransactionDetailsScreen: { transactionData: { id: string; name: string; status: string; amount: number; isDebit: boolean; } };
  VerificationScreen: undefined;
  ChangePasswordScreen: undefined;
  ChangeTransactionPINScreen: undefined;
  ActivateTransactionHubScreen: undefined;
  InternationalFundScreen: { amount: number; currency: string };
  SendToNigerianBankScreen: undefined;
  PaymentNGNScreen: undefined;
  ValidatePinScreen: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

export default function MainStack() {
  const navigationRef =
    React.useRef<NavigationContainerRef<RootStackParamList>>(null);
  return (
    <CredentailsContext.Consumer>
      {({ storedCredentails }) => (
        <NavigationContainer ref={navigationRef}>
          <Stack.Navigator id={undefined}>
            {storedCredentails ? (
              // Stack when the user is logged in
              <>
              {console.log(storedCredentails)}
                {storedCredentails?.nextStep?.includes(
                  "auth/onboarding/kyc/verify"
                ) ? (
                  <Stack.Screen
                    name="CountrySelectionScreen"
                    component={CountrySelectionScreen}
                    options={{
                      headerTransparent: true,
                      headerShadowVisible: false,
                      headerShown: false,
                    }}
                  />
                ) : storedCredentails?.nextStep?.includes(
                  "auth/onboarding/kyc/launch"
                ) ? (
                  <Stack.Screen
                    name="ForeignersKYCScreen"
                    component={ForeignersKYCScreen}
                    options={{
                      headerTransparent: true,
                      headerShadowVisible: false,
                      headerShown: false,
                    }}
                  />
                ) : storedCredentails?.nextStep?.includes(
                  "auth/onboarding/documents/upload"
                ) ? (
                  <Stack.Screen
                    name="DocumentUploadScreen"
                    component={DocumentUploadScreen}
                    options={{
                      headerTransparent: true,
                      headerShadowVisible: false,
                      headerShown: false,
                    }}
                  />
                ) : storedCredentails?.nextStep?.includes(
                  "auth/onboarding/pin/create"
                ) ? (
                  <Stack.Screen
                    name="TransactionPinScreen"
                    component={TransactionPinScreen}
                    options={{
                      headerTransparent: true,
                      headerShadowVisible: false,
                      headerShown: false,
                    }}
                  />
                ) : (
                  <Stack.Screen
                    name="BottomTabNavigator"
                    component={BottomTabNavigator}
                    options={{
                      headerTransparent: true,
                      headerShadowVisible: false,
                      headerShown: false,
                    }}
                  />
                )}
                <Stack.Screen
                  name="BottomTabNavigator1"
                  component={BottomTabNavigator}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="DocumentUploadScreen1"
                  component={DocumentUploadScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ForeignersKYCScreen1"
                  component={ForeignersKYCScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="VerifyIdentityScreen"
                  component={VerifyIdentityScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="TransactionPinScreen1"
                  component={TransactionPinScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PrivacyConsentScreen"
                  component={PrivacyConsentScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SelectIDDocumentScreen"
                  component={SelectIDDocumentScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="IDCaptureScreen"
                  component={IDCaptureScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ForeignersSelfieScreen"
                  component={ForeignersSelfieScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AdditionalInformationScreen"
                  component={AdditionalInformationScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="IDInformationScreen"
                  component={IDInformationScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SelfieScreen"
                  component={SelfieScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="IDConfirmationScreen"
                  component={IDConfirmationScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="OrderPhysicalCardScreen"
                  component={OrderPhysicalCardScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PaymentScreen"
                  component={PaymentScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PickupLocationScreen"
                  component={PickupLocationScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CardActivationScreen"
                  component={CardActivationScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ZoomCardScreen"
                  component={ZoomCardScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CardSettingsScreen"
                  component={CardSettingsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="VerifyPINScreen"
                  component={VerifyPINScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ViewCardPINScreen"
                  component={ViewCardPINScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ChangeCardPINScreen"
                  component={ChangeCardPINScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CardTransactionsScreen"
                  component={CardTransactionsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ChangeCardColorScreen"
                  component={ChangeCardColorScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ActivateCardScreen"
                  component={ActivateCardScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SetCardPINScreen"
                  component={SetCardPINScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CardActivationSuccessScreen"
                  component={CardActivationSuccessScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SettingsScreen"
                  component={SettingsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AccountInfoScreen"
                  component={AccountInfoScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PrivacySecurityScreen"
                  component={PrivacySecurityScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="Security2FAScreen"
                  component={Security2FAScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="Setup2FAScreen"
                  component={Setup2FAScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SupportScreen"
                  component={SupportScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="TransactionDetailsScreen"
                  component={TransactionDetailsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="VerificationScreen"
                  component={VerificationScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ChangePasswordScreen"
                  component={ChangePasswordScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ChangeTransactionPINScreen"
                  component={ChangeTransactionPINScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ActivateTransactionHubScreen"
                  component={ActivateTransactionHubScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="InternationalFundScreen"
                  component={InternationalFundScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PaymentNGNScreen"
                  component={PaymentNGNScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ValidatePinScreen"
                  component={ValidatePinScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SendToNigerianBankScreen"
                  component={SendToNigerianBankScreen}
                  options={{ headerShown: false }}
                />
              </>
            ) : (
              // Stack when the user is not logged in
              <>
                <Stack.Screen
                  name="OnboardingScreen"
                  component={OnboardingScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SignUpScreen1"
                  component={SignUpScreen1}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="VerifyEmailScreen"
                  component={VerifyEmailScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="VerificationSuccessScreen"
                  component={VerificationSuccessScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />

                <Stack.Screen
                  name="LoginScreen"
                  component={LoginScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ForgotPasswordScreen"
                  component={ForgotPasswordScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ForgotPasswordEmailVerifyScreen"
                  component={ForgotPasswordEmailScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ResetPasswordScreen"
                  component={ResetPasswordScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
              </>
            )}
          </Stack.Navigator>
        </NavigationContainer>
      )}
    </CredentailsContext.Consumer>
  );
}
