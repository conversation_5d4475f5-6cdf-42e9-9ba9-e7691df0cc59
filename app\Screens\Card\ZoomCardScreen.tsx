import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Alert,
  Clipboard,
  ScrollView,
  ImageBackground,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Icon from "react-native-vector-icons/Feather";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import BlockCardBottomSheet from "../../Components/BlockCardBottomSheet";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";

const { width, height } = Dimensions.get("window");

interface ZoomCardScreenProps {
  navigation: any;
  route?: {
    params?: {
      cardType?: string;
    };
  };
}

export default function ZoomCardScreen({
  navigation,
  route,
}: ZoomCardScreenProps) {
  const cardType = route?.params?.cardType || "ngn";
  const [isCardBlocked, setIsCardBlocked] = useState(false);
  const [showBlockCardSheet, setShowBlockCardSheet] = useState(false);

  // Handle copy card details
  const handleCopyCardDetails = () => {
    const cardDetails = "4567 6574 2533 9018 - CVV: 654 - Exp: 07/25";
    Clipboard.setString(cardDetails);
    Alert.alert("Copied", "Card details copied to clipboard");
  };

  // Navigate to card settings
  const navigateToCardSettings = () => {
    navigation.navigate("CardSettingsScreen");
  };

  // Show block card bottom sheet
  const showBlockCard = () => {
    setShowBlockCardSheet(true);
  };

  // Handle block card
  const handleBlockCard = () => {
    setIsCardBlocked(true);
    Alert.alert("Success", "Your card has been blocked");
  };

  // Handle unblock card
  const handleUnblockCard = () => {
    setIsCardBlocked(false);
    Alert.alert("Success", "Your card has been unblocked");
  };

  // Navigate to transactions
  const navigateToTransactions = () => {
    navigation.navigate("CardTransactionsScreen");
  };

  // Navigate to change card color
  const navigateToChangeCardColor = () => {
    console.log("Navigating to Change Card Color Screen");
    navigation.navigate("ChangeCardColorScreen", {
      currentColor: "gold",
    });
  };

  return (
    <View style={styles.container}>
      <Div>
        <Header title="Zoom Card" showNotification={true} />
        <ScrollView style={{ width: "95%" }}>
          <View style={styles.content}>
            {/* Card Preview */}
            <View style={styles.cardContainer}>
              <View style={styles.card}>
                <ImageBackground
                  source={require("../../assets/bg-background.png")}
                  style={{
                    width: "60%",
                    height: "110%",
                    position: "absolute",
                    right: -90,
                  }}
                  resizeMode="cover"
                />
                <View style={styles.cardHeader}>
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      gap: 16,
                    }}
                  >
                    <Text style={styles.cardHolderName}>Anita Amadi</Text>
                    <Text style={styles.cardExpiry}>07/25</Text>
                  </View>
                  <TouchableOpacity style={styles.eyeIcon}>
                    <Icon name="eye" size={20} color="#DF7400" />
                  </TouchableOpacity>
                </View>

                <Text style={styles.cardCvv}>CVV: 654</Text>
                <Text style={styles.cardNumber}>1234 1234 1234 1234</Text>

                <View style={styles.cardBrand}>
                  <Text style={styles.cardBalance}>NGN 0.00</Text>
                  <View style={styles.masterCardIcon}>
                    <View style={[styles.circle, styles.redCircle]} />
                    <View style={[styles.circle, styles.yellowCircle]} />
                  </View>
                </View>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.actionButton}>
                <SvgXml xml={svg.cardAdd} />
                <Text style={styles.actionButtonText}>Top Up</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.actionButton}     onPress={navigateToCardSettings}>
                <SvgXml xml={svg.cardSetting} />
                <Text style={styles.actionButtonText}>Settings</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={()=>{
                  navigation.navigate("CardTransactionsScreen")
                }}
              >
                  <SvgXml xml={svg.cardTransaction} />
                <Text style={styles.actionButtonText}>Transactions</Text>
              </TouchableOpacity>
            </View>

            {/* Copy Card Details Button */}
            <TouchableOpacity
              style={styles.copyCardButton}
              onPress={handleCopyCardDetails}
            >
              <Text style={styles.copyCardText}>Copy card details</Text>
              <Icon name="copy" size={18} color="#333" />
            </TouchableOpacity>

            {/* Card Options */}
            <View style={styles.cardOptions}>
              <TouchableOpacity
                style={styles.optionItem}
                onPress={navigateToCardSettings}
              >
                <Text style={styles.optionText}>Card Settings</Text>
                <Icon name="chevron-right" size={20} color="#333" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.optionItem}
                onPress={showBlockCard}
              >
                <Text style={styles.optionText}>Block your card</Text>
                <Icon name="chevron-right" size={20} color="#333" />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.optionItem}
                onPress={navigateToChangeCardColor}
              >
                <Text style={styles.optionText}>Change Card Colour</Text>
                <Icon name="chevron-right" size={20} color="#333" />
              </TouchableOpacity>
            </View>

            {/* Block Card Bottom Sheet */}
            <BlockCardBottomSheet
              isVisible={showBlockCardSheet}
              onClose={() => setShowBlockCardSheet(false)}
              onBlockCard={handleBlockCard}
              onUnblockCard={handleUnblockCard}
            />
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  cardContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 20,
  },
  card: {
    width: "100%",
    minHeight: 215,
    backgroundColor: colors.cardColor,
    borderRadius: 12,
    padding: 16,
    position: "relative",
    overflow: "hidden"
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  cardHolderName: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardExpiry: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  eyeIcon: {
    padding: 4,
  },
  cardCvv: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginBottom: 8,
    marginTop: 10,
  },
  cardNumber: {
    fontSize: 18,
    fontFamily: fonts.plusJBold,
    color: colors.textBlack,
    marginTop: 8,
    letterSpacing: 1,
  },
  cardBalance: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
  },
  cardBrand: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    position: "absolute",
    bottom: 16,
    right: 16,
  },
  masterCardIcon: {
    flexDirection: "row",
    alignItems: "center",
  },
  circle: {
    width: 31,
    height: 31,
    borderRadius: 100,
  },
  redCircle: {
    backgroundColor: "#EB001B",
    marginRight: -8,
  },
  yellowCircle: {
    backgroundColor: "#F79E1B",
  },
  actionButtons: {
    width: "65%",
    alignSelf: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  actionButton: {
    alignItems: "center",
    padding: 10,
  },
  actionButtonText: {
    fontSize: 12,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginTop: 5,
  },
  copyCardButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 8,
    marginBottom: 20,
    alignSelf: "center",
  },
  copyCardText: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginRight: 8,
  },
  cardOptions: {
    marginTop: 10,
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    backgroundColor: "#F9F9FA",
    borderRadius: 8,
    marginBottom: 8,
    paddingHorizontal: 12,
  },
  optionText: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
});
