import {
    StyleSheet,
    Text,
    View,
    Dimensions,
    TouchableOpacity,
} from "react-native";
import React from "react";
import { CSSProperties } from "react";
import { fonts } from "../Config/Fonts";
import { colors } from "../Config/colors";
const { width, height } = Dimensions.get("window");

interface PProps {
    children: any;
    style?: CSSProperties;
    onPress?: any;
}

export default function Link({ children, style, onPress }: PProps) {
    return (
        <TouchableOpacity onPress={onPress}>
            <Text
                style={[
                    {
                        fontFamily: fonts.plusJSemibold,
                        fontSize: 14,
                        color: colors.primary,
                    },
                    // @ts-ignore
                    style,
                ]}
            >
                {children}
            </Text>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({});
