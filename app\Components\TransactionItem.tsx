import React from "react";
import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import { SvgXml } from "react-native-svg";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import { svg } from "../Config/Svg";

interface TransactionItemProps {
  name: string;
  status: string;
  amount: number;
  isDebit: boolean;
}

const TransactionItem = ({
  name,
  status,
  amount,
  isDebit,
}: TransactionItemProps) => {
  return (
    <View style={styles.transactionItem}>
      <View style={styles.transactionLeft}>
        <SvgXml xml={isDebit ? svg.redArrowUp : svg.greenArrowDown} />
        <View style={styles.transactionInfo}>
          <Text style={styles.transactionName}>{name}</Text>
          <Text
            style={[
              styles.transactionStatus,
              {
                color:
                  status.toLowerCase() === "successful"
                    ? colors.success
                    : colors.error,
              },
            ]}
          >
            {status}
          </Text>
        </View>
      </View>
      <Text
        style={[
          styles.transactionAmount,
          { color: isDebit ? colors.error : colors.success },
        ]}
      >
        {isDebit ? "-" : "+"}N {Math.abs(amount)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  transactionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  transactionLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  transactionInfo: {
    marginLeft: 12,
  },
  transactionName: {
    fontFamily: fonts.plusJMedium,
    fontSize: 14,
    color: colors.textBlack,
    marginBottom: 4,
  },
  transactionStatus: {
    fontFamily: fonts.plusJRegular,
    fontSize: 12,
  },
  transactionAmount: {
    fontFamily: fonts.plusJBold,
    fontSize: 16,
  },
});

export default TransactionItem;
