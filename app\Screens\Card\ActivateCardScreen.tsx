import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Alert,
  Image,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Button from "../../Components/Button";
import Input from "../../Components/Input";
import Icon from "react-native-vector-icons/Feather";

const { width, height } = Dimensions.get("window");

interface ActivateCardScreenProps {
  navigation: any;
}

// Wallet options
const walletOptions = [
  { id: "naira", name: "Naira Wallet", icon: "ng" },
  // { id: "usd", name: "USD Wallet", icon: "us" },
];

export default function ActivateCardScreen({
  navigation,
}: ActivateCardScreenProps) {
  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);
  const [showWalletOptions, setShowWalletOptions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeIcon, setActiveIcon] = useState("ng");

  // Add these error states
  const [cardNumberError, setCardNumberError] = useState("");
  const [expiryDateError, setExpiryDateError] = useState("");
  const [walletError, setWalletError] = useState("");

  // Format card number with spaces
  const formatCardNumber = (text: string) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, "");

    // Add spaces after every 4 digits
    let formatted = "";
    for (let i = 0; i < cleaned.length; i++) {
      if (i > 0 && i % 4 === 0) {
        formatted += " ";
      }
      formatted += cleaned[i];
    }

    return formatted;
  };
  // Format expiry date as MM/YY
  // Format expiry date as MM/YY
  const formatExpiryDate = (text: string) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, "");

    // Add slash after 2 digits
    if (cleaned.length >= 2) {
      return cleaned.substring(0, 2) + "/" + cleaned.substring(2, 4);
    }

    return cleaned;
  };

  // Handle expiry date change
  const handleExpiryDateChange = (text: string) => {
    const formatted = formatExpiryDate(text);
    setExpiryDate(formatted);

    // Clear error when user starts typing
    if (expiryDateError) {
      setExpiryDateError("");
    }
  };

  // Handle card number change
  // Handle card number change
  const handleCardNumberChange = (text: string) => {
    const formatted = formatCardNumber(text);
    setCardNumber(formatted);

    // Clear error when user starts typing
    if (cardNumberError) {
      setCardNumberError("");
    }
  };
  // Handle wallet selection
  const handleWalletSelection = (walletId: string) => {
    setSelectedWallet(walletId);
    setShowWalletOptions(false);
  };

  // Get selected wallet name
  const getSelectedWalletName = () => {
    if (!selectedWallet) return "Select Wallet";
    const wallet = walletOptions.find((w) => w.id === selectedWallet);
    return wallet ? wallet.name : "Select Wallet";
  };

  // Handle confirm button press
  // Handle confirm button press
  // Handle confirm button press
  const handleConfirm = () => {
    // Reset all errors
    setCardNumberError("");
    setExpiryDateError("");
    setWalletError("");

    let hasErrors = false;

    // Validate card number
    if (!cardNumber || cardNumber.replace(/\s/g, "").length < 16) {
      setCardNumberError("Please enter a valid 16-digit card number");
      hasErrors = true;
    }

    // Validate expiry date
    if (!expiryDate || expiryDate.length < 5) {
      setExpiryDateError("Please enter a valid expiry date (MM/YY)");
      hasErrors = true;
    } else {
      // Validate expiry date format
      const [month, year] = expiryDate.split("/");
      const monthNum = parseInt(month);
      if (monthNum < 1 || monthNum > 12) {
        setExpiryDateError("Please enter a valid month (01-12)");
        hasErrors = true;
      }
    }

    // Validate wallet selection
    if (!selectedWallet) {
      setWalletError("Please select a wallet");
      hasErrors = true;
    }

    // If there are errors, don't proceed
    if (hasErrors) {
      return;
    }

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      // Navigate to set new PIN screen
      navigation.navigate("SetCardPINScreen");
    }, 1500);
  };
  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header title="Activate Card" showNotification={false} />

        <View style={styles.content}>
          {/* Card Number Input */}
          {/* Card Number Input */}
          <View style={styles.inputContainer}>
            <Input
              label="Enter Card Number"
              placeholder="Enter 16-digit card number"
              value={cardNumber}
              onChangeText={handleCardNumberChange}
              keyboardType="numeric"
              maxLenght={19}
              error={!!cardNumberError}
              errorText={cardNumberError}
            />
          </View>

          {/* Expiry Date Input */}
          <View style={styles.inputContainer}>
            <Input
              label="Enter Expiry Date"
              placeholder="MM/YY"
              value={expiryDate}
              onChangeText={handleExpiryDateChange}
              keyboardType="numeric"
              maxLenght={5}
              error={!!expiryDateError}
              errorText={expiryDateError}
            />
          </View>
          {/* Confirm Button */}
          <View style={styles.buttonContainer}>
            <Button
              btnText="Confirm"
              onPress={handleConfirm}
              loading={loading}
            />
          </View>
        </View>
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginBottom: 8,
  },
  walletSelector: {
    height: 59,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  walletSelectorText: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  walletOptionsContainer: {
    marginTop: 8,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 8,
    backgroundColor: colors.white,
    zIndex: 10,
  },
  walletOption: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F2F2F2",
    gap: 8,
  },
  selectedWalletOption: {
    backgroundColor: "#F9F9F9",
  },
  walletOptionIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  walletOptionText: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  selectedWalletDisplay: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    paddingVertical: 12,
    paddingHorizontal: 12,
    backgroundColor: "#F9F9F9",
    borderRadius: 8,
  },
  buttonContainer: {
    marginTop: 32,
  },
  flagIcon: {
    width: 24,
    height: 24,
    borderRadius: 100,
  },
});
