import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Alert,
  Image,
} from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import Div from "../../Components/Div";
import Header from "../../Components/Header";
import Button from "../../Components/Button";
import Input from "../../Components/Input";
import Icon from "react-native-vector-icons/Feather";

const { width, height } = Dimensions.get("window");

interface ActivateCardScreenProps {
  navigation: any;
}

// Wallet options
const walletOptions = [
  { id: "naira", name: "Naira Wallet", icon: "ng" },
  // { id: "usd", name: "USD Wallet", icon: "us" },
];

export default function ActivateCardScreen({
  navigation,
}: ActivateCardScreenProps) {
  const [cardNumber, setCardNumber] = useState("");
  const [cardPin, setCardPin] = useState("");
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);
  const [showWalletOptions, setShowWalletOptions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeIcon, setActiveIcon] = useState("ng");

  // Format card number with spaces
  const formatCardNumber = (text: string) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, "");

    // Add spaces after every 4 digits
    let formatted = "";
    for (let i = 0; i < cleaned.length; i++) {
      if (i > 0 && i % 4 === 0) {
        formatted += " ";
      }
      formatted += cleaned[i];
    }

    return formatted;
  };

  // Handle card number change
  const handleCardNumberChange = (text: string) => {
    const formatted = formatCardNumber(text);
    setCardNumber(formatted);
  };

  // Handle wallet selection
  const handleWalletSelection = (walletId: string) => {
    setSelectedWallet(walletId);
    setShowWalletOptions(false);
  };

  // Get selected wallet name
  const getSelectedWalletName = () => {
    if (!selectedWallet) return "Select Wallet";
    const wallet = walletOptions.find((w) => w.id === selectedWallet);
    return wallet ? wallet.name : "Select Wallet";
  };

  // Handle confirm button press
  const handleConfirm = () => {
    // Validate inputs
    if (!cardNumber || cardNumber.replace(/\s/g, "").length < 16) {
      Alert.alert("Error", "Please enter a valid 16-digit card number");
      return;
    }

    if (!cardPin || cardPin.length < 4) {
      Alert.alert("Error", "Please enter a valid 4-digit PIN");
      return;
    }

    if (!selectedWallet) {
      Alert.alert("Error", "Please select a wallet");
      return;
    }

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      // Navigate to set new PIN screen
      navigation.navigate("SetCardPINScreen");
    }, 1500);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Div>
        <Header title="Activate Card" showNotification={false} />

        <View style={styles.content}>
          {/* Card Number Input */}
          <View style={styles.inputContainer}>
            <Input
              label="Enter Card Number"
              placeholder="Enter 16-digit card number"
              value={cardNumber}
              onChangeText={handleCardNumberChange}
              keyboardType="numeric"
              maxLenght={19} // 16 digits + 3 spaces
            />
          </View>

          {/* Card PIN Input */}
          <View style={styles.inputContainer}>
            <Input
              label="Enter Card Default PIN"
              placeholder="Enter default PIN"
              value={cardPin}
              onChangeText={setCardPin}
              keyboardType="numeric"
              maxLenght={6}
              type="password"
            />
          </View>

          {/* Wallet Selection */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Select Wallet</Text>
            <TouchableOpacity
              style={styles.walletSelector}
              onPress={() => setShowWalletOptions(!showWalletOptions)}
            >
              <View
                style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
              >
                {selectedWallet && (
                  <Image
                    source={{
                      uri: `https://flagcdn.com/w2560/${activeIcon}.png`,
                    }}
                    style={[
                      styles.flagIcon,
                      { objectFit: activeIcon === "ng" ? "fill" : "cover" },
                    ]}
                  />
                )}
                <Text style={styles.walletSelectorText}>
                  {getSelectedWalletName()}
                </Text>
              </View>
              <Icon name="chevron-down" size={20} color="#000" />
            </TouchableOpacity>

            {showWalletOptions && (
              <View style={styles.walletOptionsContainer}>
                {walletOptions.map((wallet) => (
                  <TouchableOpacity
                    key={wallet.id}
                    style={[
                      styles.walletOption,
                      selectedWallet === wallet.id &&
                        styles.selectedWalletOption,
                    ]}
                    onPress={() => handleWalletSelection(wallet.id)}
                  >
                    <Image
                      source={{
                        uri: `https://flagcdn.com/w2560/${wallet.icon}.png`,
                      }}
                      style={[
                        styles.flagIcon,
                        { objectFit: wallet.icon === "ng" ? "fill" : "cover" },
                      ]}
                    />
                    <Text style={styles.walletOptionText}>{wallet.name}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          {/* Confirm Button */}
          <View style={styles.buttonContainer}>
            <Button
              btnText="Confirm"
              onPress={handleConfirm}
              loading={loading}
            />
          </View>
        </View>
      </Div>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
    marginBottom: 8,
  },
  walletSelector: {
    height: 59,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  walletSelectorText: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  walletOptionsContainer: {
    marginTop: 8,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 8,
    backgroundColor: colors.white,
    zIndex: 10,
  },
  walletOption: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F2F2F2",
    gap: 8,
  },
  selectedWalletOption: {
    backgroundColor: "#F9F9F9",
  },
  walletOptionIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  walletOptionText: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textBlack,
  },
  selectedWalletDisplay: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    paddingVertical: 12,
    paddingHorizontal: 12,
    backgroundColor: "#F9F9F9",
    borderRadius: 8,
  },
  buttonContainer: {
    marginTop: 32,
  },
  flagIcon: {
    width: 24,
    height: 24,
    borderRadius: 100,
  },
});
