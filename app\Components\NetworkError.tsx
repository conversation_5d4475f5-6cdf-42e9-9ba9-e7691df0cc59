import React from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  Image,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import P from "./P";
import H4 from "./H4";
import Button from "./Button";
import Icon from "react-native-vector-icons/Feather";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";

interface NetworkErrorProps {
  visible?: boolean;
  type?: "inline" | "modal" | "fullscreen";
  title?: string;
  message?: string;
  onRetry?: () => void;
  onClose?: () => void;
  retryText?: string;
  showCloseButton?: boolean;
  style?: any;
  errorType?: "network" | "timeout" | "server" | "generic";
}

const { width, height } = Dimensions.get("window");

const NetworkError: React.FC<NetworkErrorProps> = ({
  visible = true,
  type = "inline",
  title,
  message,
  onRetry,
  onClose,
  retryText = "Try Again",
  showCloseButton = false,
  style,
  errorType = "network",
}) => {
  // Get error content based on error type
  const getErrorContent = () => {
    switch (errorType) {
      case "timeout":
        return {
          defaultTitle: "Connection Timed Out",
          defaultMessage:
            "The request is taking too long to complete. This could be due to a slow internet connection or our servers might be busy. Please try again.",
          icon: svg.conE || "wifi-off",
        };
      case "server":
        return {
          defaultTitle: "Server Error",
          defaultMessage:
            "Something went wrong on our end. Our team has been notified and is working to fix this issue.",
          icon: svg.conE || "server",
        };
      case "generic":
        return {
          defaultTitle: "Something Went Wrong",
          defaultMessage:
            "An unexpected error occurred. Please try again or contact support if the problem persists.",
          icon: svg.conE || "alert-circle",
        };
      default: // network
        return {
          defaultTitle: "No Internet Connection",
          defaultMessage:
            "Please check your internet connection and try again.",
          icon: svg.conE || "wifi-off",
        };
    }
  };

  const errorContent = getErrorContent();
  const displayTitle = title || errorContent.defaultTitle;
  const displayMessage = message || errorContent.defaultMessage;

  // Inline Error Component
  const InlineError = () => (
    <View style={[styles.inlineContainer, style]}>
      <View style={styles.iconContainer}>
        {svg.conE ? (
          <SvgXml xml={svg.conE} width={80} height={80} />
        ) : (
          <Icon name={errorContent.icon} size={60} color={colors.textAsh} />
        )}
      </View>

      <H4 style={styles.title}>{displayTitle}</H4>
      <P style={styles.message}>{displayMessage}</P>

      <View style={styles.buttonContainer}>
        {onRetry && (
          <Button
            btnText={retryText}
            onPress={onRetry}
            style={styles.retryButton}
          />
        )}
        {showCloseButton && onClose && (
          <Button
            btnText="Close"
            onPress={onClose}
            type="alt"
            style={styles.closeButton}
          />
        )}
      </View>
    </View>
  );

  // Modal Error Component
  const ModalError = () => (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {onClose && (
            <TouchableOpacity style={styles.modalCloseButton} onPress={onClose}>
              <Icon name="x" size={24} color={colors.textBlack} />
            </TouchableOpacity>
          )}

          <View style={styles.modalIconContainer}>
            {svg.conE ? (
              <SvgXml xml={svg.conE} />
            ) : (
              <Icon name={errorContent.icon} size={80} color={colors.textAsh} />
            )}
          </View>

          <H4 style={styles.modalTitle}>{displayTitle}</H4>
          <P style={styles.modalMessage}>{displayMessage}</P>

          <View style={styles.modalButtonContainer}>
            {onRetry && (
              <Button
                btnText={retryText}
                onPress={onRetry}
                style={styles.modalRetryButton}
              />
            )}
          </View>
        </View>
      </View>
    </Modal>
  );

  // Fullscreen Error Component
  const FullscreenError = () => (
    <View style={styles.fullscreenContainer}>
      <View style={styles.fullscreenContent}>
        <View style={styles.fullscreenIconContainer}>
          {svg.conE ? (
            <SvgXml xml={svg.conE} width={120} height={120} />
          ) : (
            <Icon name={errorContent.icon} size={100} color={colors.textAsh} />
          )}
        </View>

        <H4 style={styles.fullscreenTitle}>{displayTitle}</H4>
        <P style={styles.fullscreenMessage}>{displayMessage}</P>

        <View style={styles.fullscreenButtonContainer}>
          {onRetry && (
            <Button
              btnText={retryText}
              onPress={onRetry}
              style={styles.fullscreenRetryButton}
            />
          )}
          {showCloseButton && onClose && (
            <Button
              btnText="Close"
              onPress={onClose}
              type="alt"
              style={styles.fullscreenCloseButton}
            />
          )}
        </View>
      </View>
    </View>
  );

  // Don't render if not visible (for inline type)
  if (!visible && type === "inline") {
    return null;
  }

  // Render based on type
  switch (type) {
    case "modal":
      return <ModalError />;
    case "fullscreen":
      return <FullscreenError />;
    case "inline":
    default:
      return <InlineError />;
  }
};

const styles = StyleSheet.create({
  // Inline styles
  inlineContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
    backgroundColor: colors.white,
  },
  iconContainer: {
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    textAlign: "center",
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  buttonContainer: {
    width: "100%",
    gap: 12,
  },
  retryButton: {
    backgroundColor: colors.primary,
  },
  closeButton: {
    borderWidth: 1,
    borderColor: colors.stroke,
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    width: width * 0.85,
    maxWidth: 400,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalCloseButton: {
    position: "absolute",
    top: 16,
    right: 16,
    padding: 4,
    zIndex: 1,
  },
  modalIconContainer: {
    marginBottom: 16,
    marginTop: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    textAlign: "center",
    marginBottom: 8,
  },
  modalMessage: {
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 20,
  },
  modalButtonContainer: {
    width: "100%",
  },
  modalRetryButton: {
    backgroundColor: colors.primary,
  },

  // Fullscreen styles
  fullscreenContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  fullscreenContent: {
    flex: 1,
    // justifyContent: "center",
    alignItems: "center",
    padding: 32,
    marginTop: (15 * height) / 100,
  },
  fullscreenIconContainer: {
    marginBottom: 32,
  },
  fullscreenTitle: {
    fontSize: 24,
    fontFamily: fonts.plusJSemibold,
    color: colors.textBlack,
    textAlign: "center",
    marginBottom: 16,
  },
  fullscreenMessage: {
    fontSize: 16,
    fontFamily: fonts.plusJRegular,
    color: colors.textAsh,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  fullscreenButtonContainer: {
    width: "100%",
    gap: 16,
  },
  fullscreenRetryButton: {
    backgroundColor: colors.primary,
  },
  fullscreenCloseButton: {
    borderWidth: 1,
    borderColor: colors.stroke,
  },
});

export default NetworkError;
