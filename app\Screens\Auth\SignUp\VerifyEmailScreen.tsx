import React, { useState, useEffect, useContext } from "react";
import { useUpdateNextStep } from "../../../utils/updateNextStep";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Platform,
  Text,
  Modal,
  Image,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import { OtpInput } from "react-native-otp-entry";
import AppHeader from "../../../Components/AppHeader";
import DotLoader from "../../../Components/DotLoader";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../../../Context/CredentailsContext";
import { useToast } from "../../../Context/ToastContext";
import PageHeader from "../../../Components/PageHeader";
import Button from "../../../Components/Button";
import Icon from "react-native-vector-icons/Feather";
import { ResendOtp, VerifyOtp } from "../../../RequestHandler.tsx/Auth";

const { width, height } = Dimensions.get("window");

export default function VerifyEmailScreen({ navigation, route }) {
  // Assuming email is passed from previous screen
  const { email } = route.params || "";
  const { handleToast } = useToast();

  // State for OTP inputs
  const [otpCode, setOtpCode] = useState("");
  const [timer, setTimer] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const [otpError, setOtpError] = useState("");
  const [isOtpFilled, setIsOtpFilled] = useState(false);
  const [loading, setLoading] = useState(false);
  const { setStoredCredentails } = useContext(CredentailsContext);
  const { updateUserNextStep } = useUpdateNextStep();
  const [data, setData] = useState([]);
  const [isEmailVerified, setIsEmailVerified] = useState(false);

  // Timer effect
  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setIsResendDisabled(false);
    }
  }, [timer]);

  const persistLogin = (credentail: any) => {
    // Store credentials without timestamp since we're using JWT expiration
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(() => {
        // @ts-ignore
        setStoredCredentails(credentail);
      })
      .catch((err) => {
        console.error("Error persisting login:", err);
      });
  };
  const resend = async () => {
    const body = {
      email: email,
    };
    try {
      const res = await ResendOtp(body);
    } catch (error) {}
  };
  const verifyOtp = async () => {
    setLoading(true);
    const body = {
      email: email,
      otp: otpCode,
    };
    try {
      const res = await VerifyOtp(body);
      setIsEmailVerified(true);
      setData(res);
    } catch (error) {
      handleToast(error.message, "error");
    } finally {
      setLoading(false);
    }
  };

  const resendOtp = () => {
    if (!isResendDisabled) {
      // Reset OTP field
      setOtpCode("");
      setIsOtpFilled(false);
      // Reset timer
      setTimer(60);
      setIsResendDisabled(true);
      setOtpError("");
      resend();

      // Add API call to resend OTP here
      // For now, just show a toast message
    }
  };

  // Handle OTP changes
  const handleOtpChange = (text: string) => {
    setOtpCode(text);
    if (text.length === 6) {
      setIsOtpFilled(true);
    } else {
      setIsOtpFilled(false);
    }
    if (otpError) {
      setOtpError("");
    }
  };

  // Handle OTP filled
  const handleOtpFilled = () => {
    setIsOtpFilled(true);
  };

  //handle submit
  const handleSubmit = () => {
    // Simulate API verification
    verifyOtp();
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <PageHeader
          currentPage={1}
          totalPages={6}
          onBack={() => navigation.goBack()}
        />
        <View style={styles.container}>
          {/* Back Button */}

          <H4 style={styles.mainTitle}>Verify email address</H4>
          <P style={styles.subtitle}>
            Enter the 6 digit code that was sent to your email address
            {email ? ` "${email}"` : ""}
          </P>

          {/* OTP Input */}
          <View style={styles.otpWrapper}>
            <OtpInput
              numberOfDigits={6}
              focusColor={colors.primary}
              autoFocus={true}
              hideStick={true}
              placeholder=""
              blurOnFilled={true}
              disabled={false}
              type="numeric"
              secureTextEntry={false}
              focusStickBlinkingDuration={500}
              onFocus={() => console.log("Focused")}
              onBlur={() => console.log("Blurred")}
              onTextChange={handleOtpChange}
              onFilled={handleOtpFilled}
              textInputProps={{
                accessibilityLabel: "Email Verification Code",
                caretHidden: false,
              }}
              theme={{
                containerStyle: styles.otpContainer,
                pinCodeContainerStyle: otpError
                  ? styles.otpInputError
                  : styles.otpInput,
                pinCodeTextStyle: styles.otpText,
                focusedPinCodeContainerStyle: styles.otpInputFocused,
                placeholderTextStyle: styles.otpPlaceholder,
                filledPinCodeContainerStyle: styles.otpInputFilled,
                disabledPinCodeContainerStyle: styles.otpInputDisabled,
              }}
            />
          </View>

          {/* Error Message */}
          {otpError ? (
            <View style={styles.errorContainer}>
              <Icon name="alert-circle" size={16} color={colors.red} />
              <P style={styles.errorText}>{otpError}</P>
            </View>
          ) : null}

          {/* Resend Code Button */}
          <View style={styles.resendContainer}>
            <>
              <P
                // @ts-ignore
                style={[
                  styles.resendText,
                  {
                    marginRight: 8,
                  },
                ]}
              >
                Didn't get any code?
              </P>
              <TouchableOpacity onPress={resendOtp} disabled={isResendDisabled}>
                <P
                  // @ts-ignore
                  style={[
                    styles.resendText2,
                    isResendDisabled && styles.disabledText,
                  ]}
                >
                  Resend
                </P>
              </TouchableOpacity>
            </>
          </View>
          {isResendDisabled && (
            <>
              <>
                <P style={styles.timerText}>
                  {`${timer < 10 ? "00:0" : "00:"}${timer}`}
                </P>
              </>
            </>
          )}
        </View>
        {isOtpFilled ? (
          <View style={{ width: "90%", marginBottom: (10 * height) / 100 }}>
            <Button btnText="Next" onPress={handleSubmit} loading={loading} />
          </View>
        ) : (
          <View style={styles.footerContainer}>
            <View style={styles.loginContainer}>
              <Text style={styles.accountText}>Already have an account?</Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("LoginScreen");
                }}
              >
                <Text style={styles.loginText}>Log in</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </Div>

      {isEmailVerified && (
        <Modal
          style={{ flex: 1 }}
          animationType="slide"
          transparent={true}
          visible={isEmailVerified}
          onRequestClose={() => setIsEmailVerified(false)}
        >
          <View style={styles.cont}>
            <View
              style={{
                width: "100%",
                alignItems: "center",
                marginTop: (20 * height) / 100,
              }}
            >
              <Image
                source={require("../../../assets/Done.png")}
                style={{ objectFit: "contain", width: 200, height: 200 }}
              />
              <H4>Successful!</H4>
              <P style={{ marginTop: 8, textAlign: "center" }}>
                Your email address has been{"\n"}verified
              </P>

              <TouchableOpacity
                style={{
                  paddingVertical: 12,
                  paddingHorizontal: 38,
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: colors.primary,
                  marginTop: 32,
                }}
                onPress={() => {
                  // navigation.navigate("CountrySelectionScreen");
                  persistLogin(data);
                }}
              >
                <P style={{ fontFamily: fonts.plusJBold }}>Continue</P>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  cont: {
    width: "100%",
    height,
    alignItems: "center",
    // justifyContent: "center",
    backgroundColor: "#FFFFFF",
  },
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    backgroundColor: colors.white,
  },
  backButton: {
    alignSelf: "flex-start",
    padding: 10,
    marginTop: 20,
  },
  backButtonText: {
    fontSize: 28,
    fontFamily: fonts.plusJBold,
  },
  title: {
    fontFamily: fonts.plusJBold,
    fontSize: 24,
    alignSelf: "flex-start",
    marginTop: 20,
    marginBottom: 18,
  },
  description: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textBlack,
    alignSelf: "flex-start",
    marginBottom: 29,
    lineHeight: 22,
  },
  emailText: {
    fontFamily: fonts.plusJMedium,
  },
  otpWrapper: {
    width: "100%",
  },
  otpContainer: {
    width: "100%",
    justifyContent: "space-between",
  },
  otpInput: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 4,
    marginHorizontal: 0,
  },
  otpInputError: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderColor: colors.red,
    borderRadius: 4,
    marginHorizontal: 0,
  },
  otpText: {
    color: colors.textAsh,
    fontSize: 16,
    fontFamily: fonts.plusJMedium,
  },
  otpInputFocused: {
    borderColor: colors.stroke,
    backgroundColor: colors.white,
  },
  otpPlaceholder: {
    color: colors.textBlack,
    fontSize: 20,
    fontFamily: fonts.plusJRegular,
  },
  otpInputFilled: {
    borderColor: colors.stroke,
    backgroundColor: colors.white,
  },
  otpInputDisabled: {
    borderColor: colors.stroke,
    backgroundColor: "#F5F5F5",
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  errorText: {
    color: colors.red,
    fontSize: 14,
    fontFamily: fonts.plusJRegular,
    marginLeft: 8,
  },
  resendContainer: {
    flexDirection: "row",
    marginTop: 16,
    marginBottom: 40,
  },
  resendText: {
    fontFamily: fonts.plusJLight,
    fontSize: 14,
    color: colors.textAsh,
  },
  resendText2: {
    fontFamily: fonts.plusJRegular,
    fontSize: 16,
    color: colors.textBlack,
  },
  disabledText: {
    color: colors.textAsh,
  },
  timerText: {
    fontFamily: fonts.plusJSemibold,
    fontSize: 20,
    color: colors.textBlack,
    marginLeft: 8,
    textAlign: "center",
  },
  mainTitle: {
    fontFamily: fonts.plusJMedium,
    fontSize: 24,
    marginBottom: 8,
    color: colors.textBlack,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    marginBottom: 30,
  },
  footerContainer: {
    width: "100%",
    alignItems: "center",
  },
  loginContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  accountText: {
    fontFamily: fonts.plusJRegular,
    fontSize: 14,
    color: "#666",
  },
  loginText: {
    fontFamily: fonts.plusJBold,
    fontSize: 14,
    color: colors.black,
    marginLeft: 5,
  },
  pointer: {
    color: "red",
  },
});
